import { Logger, RequestMethod, ValidationPipe, VersioningType } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { AppModule } from "./app.module";
import { ResponseInterceptor } from "./response.interceptor";
import apm from "modules/nestjs-elastic-apm";
import { customOriginValidation } from "./utils/custom-origin-validation";
// import { KeycloakGeneratorController } from "./module/keycloak-generator/keycloak-generator.controller";
// import { KeycloakGeneratorService } from "./module/keycloak-generator/keycloak-generator.service";
import { MicroserviceOptions, Transport } from "@nestjs/microservices";

const logger = new Logger();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors({
    methods: process.env.CORS_METHOD,
    origin: customOriginValidation,
  });
  app.useGlobalPipes(new ValidationPipe());
  app.useGlobalInterceptors(new ResponseInterceptor());
  app.setGlobalPrefix("api", { exclude: [":shortUrl"] });
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: "1",
  });
  app.enableShutdownHooks();

  const config = new DocumentBuilder()
    .setTitle("TBS Microservice API")
    .setDescription("The Body Shop : Analytics Microservice API")
    .setVersion("0.1")
    .addBearerAuth(
      {
        // I was also testing it without prefix 'Bearer ' before the JWT
        description: `[just text field] Please enter token in following format: Bearer <JWT>`,
        name: "Authorization",
        bearerFormat: "Bearer", // I`ve tested not to use this field, but the result was the same
        scheme: "Bearer",
        type: "http", // I`ve attempted type: 'apiKey' too
        in: "Header",
      },
      "access-token", // This name here is important for matching up with @ApiBearerAuth() in your controller!
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);

  SwaggerModule.setup("docs", app, document, {
    swaggerOptions: {
      docExpansion: "none",
      persistAuthorization: true,
      displayRequestDuration: true,
    },
  });

  // const service = app.get(KeycloakGeneratorService);
  // new KeycloakGeneratorController(service).generate(Object.values(document.paths)).then(() => {
  //   new Logger("Keycloak Generator").verbose("Successfully Synchronize Permission and Resource");
  // });

  // initialize connection to kafka server
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.KAFKA,
    options: {
      client: {
        brokers: process.env.KAFKA_HOST.split(","),
      },
      consumer: {
        groupId: process.env.KAFKA_GROUP_ID,
        allowAutoTopicCreation: true,
        heartbeatInterval: 10000,
      },
    },
  });

  await app.startAllMicroservices();
  await app.listen(process.env.APPS_PORT || 3000).then(() => {
    logger.log("Running on port: " + process.env.APPS_PORT);
  });
}
bootstrap();
