/**
 * Transform string size to number in bytes format.
 * ```js
 * transformAllowedSize("10 kb"); // will return 10240
 * ```
 * @param size - With format "{{value}} {{unit}}"
 */
export const transformAllowedSize = (size: string) => {
  const [value, unit] = size.split(" ");
  let res: number;

  if (!value && !unit) {
    throw new Error("Format params invalid");
  }

  switch (unit) {
    case "kb":
    case "kilobyte":
    case "kilobytes":
      res = Number(value) * 1024;
      break;
    case "mb":
    case "megabyte":
    case "megabytes":
      res = Number(value) * 1024 * 1024;
      break;
    case "gb":
    case "gigabyte":
    case "gigabytes":
      res = Number(value) * 1024 * 1024 * 1024;
      break;
    default:
      res = Number(value);
  }

  return res;
};

/**
 * set file as path instead url
 * @type {string}
 */
export const ConvertUrlToPath = (fileurl: string) => {
  if (fileurl !== null && fileurl !== undefined) {
    return fileurl.split(process.env.AWS_S3_BASE_URL)[1];
  }
};

/**
 * set file as url instead path
 * @type {string}
 */
export const ConvertPathToUrl = (filepath: string) => {
  if (filepath !== null && filepath !== undefined) {
    return process.env.AWS_S3_BASE_URL + filepath;
  }
};

/**
 * set file as url instead path
 * @type {string}
 */
export const ConvertPathToUrlChildren = (images: any) => {
  return images.map((image) => ({
    img_url: process.env.AWS_S3_BASE_URL + image.img_url,
    path_url: image.path_url,
  }));
};

/**
 * set file as url instead path
 * @type {string}
 */
export const ConvertUrlToPathChildren = (images: any) => {
  return images.map((image) => ({
    img_url: image.img_url.split(process.env.AWS_S3_BASE_URL)[1],
    path_url: image.path_url,
  }));
};

/**
 * convert string to date object
 * @type {string}
 */
export const ConvertToDate = (value: string) => {
  return value ? new Date(value) : null;
};

interface IPaginateFormat {
  data: Array<Record<string, any>>;
  page: number;
  limit: number;
  total: number;
  offset: number;
}

export const PaginateFormat = (params: IPaginateFormat) => {
  const { data, limit, page, total, offset } = params;

  const totalPage = Math.ceil(total / limit);

  return {
    docs: data,
    totalDocs: total,
    limit: limit,
    totalPages: totalPage,
    page: page,
    pagingCounter: offset + 1,
    hasPrevPage: page > 1,
    hasNextPage: page < totalPage,
    prevPage: page > 1 ? page - 1 : null,
    nextPage: page < totalPage ? page + 1 : null,
  };
};

/**
 * heading slash
 * @type {string}
 */
export const HeadingSlash = (path: any) => {
  return path.substring(0, 1) == "/" ? path : "/" + path;
};
