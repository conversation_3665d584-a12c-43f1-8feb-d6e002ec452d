import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose, { Document, SchemaTypes } from "mongoose";
import { ScreenTypeEnum } from "../enum/screen-type-enum";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { ConvertPathToUrl, ConvertUrlToPath, ConvertToDate } from "src/utils/function.util";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class Screen extends Document {
  @Prop({ required: false, type: String, enum: ScreenTypeEnum })
  type: ScreenTypeEnum;

  @Prop({ required: true, type: Number })
  position: number;

  @Prop({ required: true, type: Boolean })
  status: boolean;

  @Prop({ required: false, type: Date, set: ConvertToDate })
  startDate: Date;

  @Prop({ required: false, type: Date, set: ConvertToDate })
  endDate: Date;

  @Prop({ required: true, type: String, get: ConvertPathToUrl, set: ConvertUrlToPath })
  path: string;
}

export type ScreenDocument = Screen & Document;

export const ScreenSchema = SchemaFactory.createForClass(Screen);

ScreenSchema.plugin(mongoosePaginate);
