import { <PERSON>, Get, Param, Query } from "@nestjs/common";
import { <PERSON>pi<PERSON><PERSON>erAuth, ApiTags } from "@nestjs/swagger";
import { Public, InternalAccess } from "keycloak-connect-tbs";
import { GetActiveScreenDto } from "./dto/get-active-screen.dto";
import { ScreenService } from "./screen.service";

@ApiTags("Screen")
@ApiBearerAuth("access-token")
@Controller("screen")
// TODO : resources
export class ScreenController {
  constructor(private readonly screenService: ScreenService) {}

  @Get()
  @Public()
  @InternalAccess()
  findAll(@Query() pagination: GetActiveScreenDto) {
    return this.screenService.findAllPublic(pagination);
  }
}
