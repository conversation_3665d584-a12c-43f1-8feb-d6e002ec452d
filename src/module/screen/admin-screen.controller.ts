import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Query,
  Body,
  UploadedFile,
  FileTypeValidator,
  ParseFilePipe,
  UseInterceptors,
} from "@nestjs/common"; //main lib
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from "@nestjs/swagger"; //swagger required
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs"; //acl
import { ScreenService } from "./screen.service";
import { CreateScreenDto } from "./dto/create-screen.dto";
import { UpdateScreenDto } from "./dto/update-screen.dto";
import { GetScreenDto } from "./dto/get-screen.dto";
import { Role } from "../enum/role.enum";
import { Controllers, Scope } from "../enum/rbac.enum";
import { FileInterceptor, FilesInterceptor } from "@nestjs/platform-express";

@ApiTags("Admin - Screen") //module name
@Controller("admin/screen")
@ApiBearerAuth("access-token") //swagger
@Resource(Controllers.ADMIN_SCREEN)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
export class AdminScreenController {
  constructor(private readonly screenService: ScreenService) {}

  /**
   * create a screen
   */
  @Post()
  @ApiConsumes("multipart/form-data")
  @ApiBody({ type: CreateScreenDto })
  @UseInterceptors(FileInterceptor("file"))
  @Scopes(Scope.POST)
  createScreen(@Body() createScreenDto: CreateScreenDto, @UploadedFile("file") file: Express.Multer.File) {
    return this.screenService.createScreen(createScreenDto, file);
  }

  /**
   * get a screen
   * @param {id}
   */
  @Get(":id")
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.screenService.findById(id);
  }

  /**
   * get list of screen
   */
  @Get()
  @Scopes(Scope.GET)
  find(@Query() pagination: GetScreenDto) {
    return this.screenService.findAllAdmin(pagination);
  }

  /**
   * update a screen
   * @param {String} id
   * @param {UpdateScreenDto} updateScreenDto
   * @param {File} file
   */
  @Patch(":id")
  @ApiConsumes("multipart/form-data")
  @ApiBody({ type: UpdateScreenDto })
  @UseInterceptors(FileInterceptor("file"))
  @Scopes(Scope.PATCH)
  update(
    @Param("id") id: string,
    @Body() updateScreenDto: UpdateScreenDto,
    @UploadedFile("file") file: Express.Multer.File,
  ) {
    return this.screenService.update(id, updateScreenDto, file);
  }

  /**
   * delete a screen
   * @param {id}
   */
  @Delete(":id")
  @Scopes(Scope.DELETE)
  remove(@Param("id") id: string) {
    return this.screenService.remove(id);
  }
}
