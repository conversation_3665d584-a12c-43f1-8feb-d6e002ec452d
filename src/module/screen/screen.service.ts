import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { CreateScreenDto } from "./dto/create-screen.dto";
import { GetScreenDto } from "./dto/get-screen.dto";
import { GetActiveScreenDto } from "./dto/get-active-screen.dto";
import { UpdateScreenDto } from "./dto/update-screen.dto";
import mongoose, { PaginateModel } from "mongoose";
import { ScreenDocument } from "./schema/screen.schema";
import * as path from "path";
import { AmazonS3Services } from "src/amazon-s3/amazons3.service";

@Injectable()
export class ScreenService {
  constructor(
    @InjectModel("Screen") private screenModel: PaginateModel<ScreenDocument>,
    private readonly s3Services: AmazonS3Services,
  ) {}

  /**
   * create screen
   * @param {CreateScreenDto} createScreenDto
   * @param {File} file
   */
  async createScreen(createScreenDto: CreateScreenDto, file: Express.Multer.File) {
    const uploaded = await this._upload(file);
    createScreenDto.path = uploaded.Location;
    const newScreen = new this.screenModel(createScreenDto);
    return newScreen.save();
  }

  /**
   * get list screen for admin
   * @param {GetScreenDto} params
   */
  async findAllAdmin(params: GetScreenDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort } = params;

    if (String(params.status) === "true" || String(params.status) === "false") {
      filter.status = String(params.status) === "true";
    }

    if (params.type) {
      filter.type = params.type;
    }

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    const result = await this.screenModel.paginate(filter, options);

    return result;
  }

  /**
   * update screen
   * @param {string} id
   * @param {UpdateScreenDto} params
   * @param {File} file
   */
  async update(id: string, params: UpdateScreenDto, file: Express.Multer.File) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    if (file) {
      const uploaded = await this._upload(file);
      params.path = uploaded.Location;
    }

    const update = await this.screenModel.findOneAndUpdate({ _id: id }, params, { new: true });

    if (!update) {
      throw "Update screen failed.";
    }
    return update;
  }

  /**
   * get screen by id
   * @param {string} id
   */
  async findById(id: string) {
    const screen = this.screenModel.findById(id);

    if (!screen) {
      throw new HttpException("Screen is not found", HttpStatus.BAD_REQUEST);
    }

    return screen;
  }

  /**
   * delete a screen
   * @param {string} id
   */
  async remove(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const deleting = await this.screenModel.deleteOne({ _id: id }).exec();

    return deleting;
  }

  /**
   * get list screen for public
   */
  async findAllPublic(params: GetActiveScreenDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort } = params;

    filter.status = true;
    if (params.type) {
      filter.type = params.type;
    }

    filter.startDate = { $lte: new Date() };
    filter.endDate = { $gte: new Date() };

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort || "-position",
    };

    const result = await this.screenModel.paginate(filter, options);

    return result;
  }

  /**
   * upload file
   * @param {Express.Multer.File} file
   */
  private async _upload(file: Express.Multer.File) {
    if (!file) {
      throw new HttpException("File cannot be empty.", HttpStatus.BAD_REQUEST);
    }
    const ext = path.extname(file.originalname);
    const mimetype = file.mimetype;
    if (!ext.match(/(mkv|flv|webm|avi|mov|wmv|mp4|mpg|mpeg|jpg|jpeg|png)$/)) {
      throw new HttpException(
        "Invalid file format. Allowed file format: mkv, flv, webm, avi, mov, wmv, mp4, mpg, mpeg, jpg, jpeg or png",
        HttpStatus.BAD_REQUEST,
      );
    }

    return await this.s3Services.uploadFile(file.buffer, ext, "pos-assets/", mimetype);
  }
}
