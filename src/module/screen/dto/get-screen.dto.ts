import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON>ptional, IsEnum, IsBooleanString, ValidateIf, IsNotEmpty } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { ScreenTypeEnum } from "../enum/screen-type-enum";

export class GetScreenDto extends PaginationParamDto {
  @ApiProperty({ required: false, type: Boolean })
  @ValidateIf((obj) => obj.status)
  @IsNotEmpty()
  @IsBooleanString()
  status: boolean;

  @ApiProperty({
    required: false,
    description: "Type of screen",
    enum: ScreenTypeEnum,
  })
  @ValidateIf((obj) => [ScreenTypeEnum.image, ScreenTypeEnum.video].includes(obj.type))
  @IsNotEmpty()
  @IsEnum(ScreenTypeEnum)
  type: ScreenTypeEnum;
}
