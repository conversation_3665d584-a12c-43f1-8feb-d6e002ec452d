import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsEnum, IsString, ValidateIf, IsNotEmpty } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { ScreenTypeEnum } from "../enum/screen-type-enum";
import { Transform } from "class-transformer";

export class GetActiveScreenDto extends PaginationParamDto {
  @ApiProperty({
    required: false,
    description: "Type of screen",
    enum: ScreenTypeEnum,
  })
  @ValidateIf((obj) => [ScreenTypeEnum.image, ScreenTypeEnum.video].includes(obj.type))
  @IsNotEmpty()
  @IsEnum(ScreenTypeEnum)
  type?: ScreenTypeEnum;

  @ApiProperty({
    description: "[field] (Ascending) or -[field] (Descending), multiple sort split by ; (ex: -updatedAt;-storeName)",
    required: false,
    default: "-position",
  })
  @Transform(({ value }) => value.replace(";", " "))
  @IsOptional()
  @IsString()
  sort?: string;
}
