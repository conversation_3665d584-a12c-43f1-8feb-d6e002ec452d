import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsDate } from "class-validator";
import { ScreenTypeEnum } from "../enum/screen-type-enum";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class UpdateScreenDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  @TransformBoolean()
  status: boolean;

  @ApiProperty({ required: false, default: 0 })
  @Transform(({ value }) => Number(value))
  position?: number;

  @ApiProperty({
    description: "Type of screen",
    enum: ScreenTypeEnum,
  })
  @IsEnum(ScreenTypeEnum)
  type: ScreenTypeEnum;

  @ApiProperty({ required: false })
  @Transform(({ value }) => new Date(value))
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ required: false })
  @Transform(({ value }) => new Date(value))
  endDate?: Date;

  path: string;

  @ApiProperty({
    required: false,
    type: "string",
    format: "binary",
  })
  file: any;
}
