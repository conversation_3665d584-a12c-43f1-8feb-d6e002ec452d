import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { AdminScreenController } from "./admin-screen.controller";
import { ScreenController } from "./screen.controller";
import { ScreenService } from "./screen.service";
import { ScreenSchema } from "./schema/screen.schema";
import { AmazonS3Services } from "src/amazon-s3/amazons3.service";

@Module({
  controllers: [AdminScreenController, ScreenController],
  providers: [ScreenService, AmazonS3Services],
  imports: [
    MongooseModule.forFeature([
      {
        name: "Screen",
        schema: ScreenSchema,
      },
    ]),
  ],
})
export class ScreenModule {}
