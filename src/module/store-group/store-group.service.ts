import { InjectRedis } from "@liaoliaots/nestjs-redis";
import { HttpException, HttpStatus, Inject, Injectable, forwardRef } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import Redis from "ioredis";
import mongoose, { PaginateModel } from "mongoose";
import slugify from "slugify";
import { StoreService } from "../store/store.service";
import { CreateStoreGroupDto } from "./dto/create-store-group.dto";
import { GetSelectedStoreGroupDto, GetStoreGroupDto } from "./dto/get-store-group.dto";
import { StoreGroupDocument } from "./schema/store-group.schema";

@Injectable()
export class StoreGroupService {
  constructor(
    @InjectModel("StoreGroup") private storeGroupModel: PaginateModel<StoreGroupDocument>,
    @Inject(forwardRef(() => StoreService))
    private readonly storeService: StoreService,
    @InjectRedis()
    private readonly redisService: Redis,
  ) {}

  /**
   * create store group
   * @param {string} id
   */
  async create(params: CreateStoreGroupDto) {
    params["stores"] = await this.getStoreCodes(params.inclStore, params.exclStore);
    params["storeGroupCode"] = await this._generateStoreGroupCode(params["name"]);
    const storeGroup = new this.storeGroupModel(params);
    await storeGroup.save();
    await this.flushAllCache();
    return storeGroup;
  }

  /**
   * get store group by id
   * @param {string} id
   */
  async findById(id: string) {
    try {
      const storeGroup = await this.storeGroupModel.findById(id);
      if (!storeGroup) {
        throw new HttpException("store group is not found", HttpStatus.BAD_REQUEST);
      }
      return storeGroup;
    } catch (e) {
      throw new HttpException("store group is not found", HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * get all store group
   * @param GetNotificationTargetDto params
   */
  async findAll(params: GetStoreGroupDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort } = params;
    if (params.name) {
      filter.name = { $regex: new RegExp(params.name), $options: "i" };
    }
    if (params.store) {
      filter.stores = { $regex: new RegExp(params.store), $options: "i" };
    }

    if (params.keyword) {
      filter.$or = [
        { name: { $regex: new RegExp(params.keyword), $options: "i" } },
        { stores: { $regex: new RegExp(params.keyword), $options: "i" } },
      ];
    }
    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    const result = await this.storeGroupModel.paginate(filter, options);

    return result;
  }

  /**
   * update store group
   * @param {string} id
   * @param {UpdateContentDto} params
   */
  async update(id: string, params: CreateStoreGroupDto) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    params["stores"] = await this.getStoreCodes(params.inclStore, params.exclStore);
    const extStoreGroup = await this.storeGroupModel.findById({ _id: id });
    if (!extStoreGroup.storeGroupCode) {
      params["storeGroupCode"] = await this._generateStoreGroupCode(params["name"]);
    }

    const storeGroup = await this.storeGroupModel.findOneAndUpdate({ _id: id }, params, {
      new: true,
    });

    if (!storeGroup) {
      throw "Update data failed.";
    }
    await this.flushAllCache();
    return storeGroup;
  }

  /**
   * delete a store group
   * @param {string} id
   */
  async remove(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const deleting = await this.storeGroupModel.deleteOne({ _id: id }).exec();
    await this.flushAllCache();
    return deleting;
  }

  /**
   * refresh store group
   */
  async refreshAllStore() {
    const storeGroups = await this.storeGroupModel.find({});
    storeGroups.map(async (s) => {
      await this.update(s._id, {
        name: s.name,
        description: s.description,
        inclStore: s.inclStore,
        exclStore: s.exclStore,
      });
    });

    await this.flushAllCache();
  }

  /**
   * flush store groupe cache
   */
  async flushAllCache() {
    const keys = await this.redisService.keys("store-group-*");
    if (keys.length > 0) {
      await this.redisService.del(keys);
    }
  }

  /**
   * get store groups
   * @param {string} code
   * @returns array
   */
  async getStoreGroups(storeCode?: string) {
    const cacheKey = !storeCode ? "store-group-all" : `store-group-${storeCode}`;
    const cachedStores = await this.redisService.get(cacheKey);
    if (cachedStores) {
      return JSON.parse(cachedStores);
    }

    const filter = !storeCode ? { stores: { $not: { $size: 0 } } } : { stores: storeCode };
    const storeGroups = await this.storeGroupModel.find(filter);
    if (storeGroups.length > 0) {
      await this.redisService.set(
        cacheKey,
        JSON.stringify(storeGroups),
        "EX",
        process.env.REDIS_STORE_GROUP_CACHED_LIFETIME || 3600,
      );
    } else {
      await this.redisService.del(cacheKey);
    }
    return storeGroups;
  }

  /**
   * get store groups
   * @param {string} code
   * @returns array
   */
  async getStoreGroupByIds(param: GetSelectedStoreGroupDto) {
    const storeGroups = await this.storeGroupModel.find({ _id: { $in: param.store_group } });
    return storeGroups;
  }

  /**
   * get store codes by patern
   * @param {strig} incl
   * @param {string} excl
   * @returns {array}
   */
  async getStoreCodes(incl: string, excl?: string) {
    let stores = [];
    let _tmpStores = [];
    const allStores = await this.storeService.getAllStoreCodes();

    if (incl == "*") {
      _tmpStores = allStores;
    } else {
      const _incl = await this._splitStore(incl);
      allStores.map((s) => {
        const _prefix2 = s.substring(0, 2);
        const _prefix3 = s.substring(0, 3);
        if (_incl.exact.includes(s) || _incl.prefix.includes(_prefix2) || _incl.prefix.includes(_prefix3)) {
          _tmpStores.push(s);
        }
      });
    }

    if (!excl) {
      stores = _tmpStores;
    } else if (excl == "*") {
      stores = [];
    } else {
      const _excl = await this._splitStore(excl);
      _tmpStores.map((s) => {
        const _prefix2 = s.substring(0, 2);
        const _prefix3 = s.substring(0, 3);
        if (!_excl.exact.includes(s) && !_excl.prefix.includes(_prefix2) && !_excl.prefix.includes(_prefix3)) {
          stores.push(s);
        }
      });
    }
    return stores;
  }

  /**
   * split pattern
   * @param {string} stores
   * @returns
   */
  async _splitStore(stores: string) {
    const exact = [];
    const prefix = [];
    const codes = stores.split(",");
    codes.map((c) => {
      if (!c.match(/\*/g)) {
        exact.push(c);
      } else {
        prefix.push(c.replace("*", ""));
      }
    });

    return { exact: exact, prefix: prefix };
  }

  async _generateStoreGroupCode(storeGroupName: string) {
    let slug = slugify(storeGroupName, "_");
    const storeGroups = await this.storeGroupModel.find({ storeGroupCode: { $regex: `^${slug}` } });
    if (!storeGroups.length) {
      return slug;
    }
    let _lastSlug = "";
    storeGroups.map((s) => {
      if (_lastSlug.localeCompare(s.storeGroupCode) == -1) {
        const restPart = s.storeGroupCode.replace(slug, "");
        if (s.storeGroupCode !== slug && !restPart.match(/[a-z]/)) {
          _lastSlug = s.storeGroupCode;
        }
      }
    });
    const _lastNumber = _lastSlug.split("_").slice(-1);
    if (!_lastNumber[0].match(/[a-z]/)) {
      slug += "_" + (Number(_lastNumber[0]) + 1);
    }
    return slug;
  }
}
