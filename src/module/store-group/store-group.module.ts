import { Module, forwardRef } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { StoreGroupSchema } from "./schema/store-group.schema";
import { StoreGroupService } from "./store-group.service";
import { AdminStoreGroupController } from "./admin-store-group.controller";
import { StoreModule } from "../store/store.module";
import { StoreGroupController } from "./store-group.controller";
import { RedisModule } from "@liaoliaots/nestjs-redis";
import { ConfigModule, ConfigService } from "@nestjs/config";

@Module({
  controllers: [AdminStoreGroupController, StoreGroupController],
  providers: [StoreGroupService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "StoreGroup",
        schema: StoreGroupSchema,
      },
    ]),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const host = configService.get<string>("REDIS_HOST");
        const port = configService.get<number>("REDIS_PORT");
        const db = configService.get<number>("REDIS_STORE_GROUP_DB");
        return { config: { host, port, db } };
      },
    }),
    forwardRef(() => StoreModule),
  ],
  exports: [StoreGroupService],
})
export class StoreGroupModule {}
