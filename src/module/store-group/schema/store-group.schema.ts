import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class StoreGroup extends Document {
  @Prop({ required: true, type: String })
  name: string;

  @Prop({ required: true, unique: true, type: String, trim: true })
  storeGroupCode: string;

  @Prop({ required: true, type: String })
  description: string;

  @Prop({ required: false, type: String })
  inclStore: string;

  @Prop({ required: false, type: String })
  exclStore: string;

  @Prop({
    required: false,
    type: Array<any>,
  })
  stores: Array<any>;
}

export type StoreGroupDocument = StoreGroup & Document;

export const StoreGroupSchema = SchemaFactory.createForClass(StoreGroup);

StoreGroupSchema.plugin(mongoosePaginate);
