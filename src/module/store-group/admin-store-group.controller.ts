import { Body, Controller, Get, Param, Post, Query, Patch, Delete } from "@nestjs/common"; //main lib
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger"; //swagger required
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs"; //acl
import { Role } from "../enum/role.enum";
import { <PERSON><PERSON>, Scope } from "../enum/rbac.enum";
import { StoreGroupService } from "./store-group.service";
import { BypassFieldsValidation } from "global-body-validator-tbs";
import { CreateStoreGroupDto } from "./dto/create-store-group.dto";
import { GetStoreGroupDto } from "./dto/get-store-group.dto";

@ApiTags("Admin - Store Group") //module name
@Controller("admin/store-group")
@ApiBearerAuth("access-token") //swagger
@Resource(Controllers.ADMIN_STORE_GROUP)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
export class AdminStoreGroupController {
  constructor(private readonly storeGroupService: StoreGroupService) {}

  /*
   * create a store group
   */
  @Post()
  @Scopes(Scope.POST)
  @BypassFieldsValidation("*")
  createStoreGroup(@Body() createPopupDto: CreateStoreGroupDto) {
    return this.storeGroupService.create(createPopupDto);
  }

  /**
   * get a store group
   * @param {id}
   */
  @Get("get/:id")
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.storeGroupService.findById(id);
  }

  /**
   * get list of store group
   */
  @Get("get")
  @Scopes(Scope.GET)
  find(@Query() pagination: GetStoreGroupDto) {
    return this.storeGroupService.findAll(pagination);
  }

  /**
   * update a store group
   * @param {id}
   */
  @Patch("/:id")
  @Scopes(Scope.PATCH)
  @BypassFieldsValidation("*")
  update(@Param("id") id: string, @Body() updateQuestionDto: CreateStoreGroupDto) {
    return this.storeGroupService.update(id, updateQuestionDto);
  }

  /**
   * delete a store group
   * @param {id}
   */
  @Delete("/:id")
  @Scopes(Scope.DELETE)
  remove(@Param("id") id: string) {
    return this.storeGroupService.remove(id);
  }

  /**
   * refresh all store group
   * @param {id}
   */
  @Get("/refresh")
  @Scopes(Scope.GET)
  refresh() {
    return this.storeGroupService.refreshAllStore();
  }
}
