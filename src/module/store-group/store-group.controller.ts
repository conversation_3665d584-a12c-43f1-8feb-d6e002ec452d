/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Get, Param, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { InternalAccess, Public, Resource, Scopes } from "keycloak-connect-tbs"; //acl
import { <PERSON><PERSON>, Scope } from "../enum/rbac.enum";
import { GetSelectedStoreGroupDto } from "./dto/get-store-group.dto";
import { StoreGroupService } from "./store-group.service";

@Controller("store-group")
@ApiTags("Store Group")
@Resource(Controllers.STORE_GROUP)
@ApiBearerAuth("access-token") //swagger
export class StoreGroupController {
  constructor(private readonly storeGroupService: StoreGroupService) {}

  /**
   * get store  group list by id
   * @returns get store group list
   */
  @Get("detail/:id")
  @Scopes(Scope.GET)
  @InternalAccess()
  async getById(@Param("id") id: string) {
    return this.storeGroupService.findById(id);
  }

  /**
   * get store  group list by code
   * @returns get store group list
   */
  @Get("list/:code")
  @Scopes(Scope.GET)
  @InternalAccess()
  async listByCode(@Param("code") code: string) {
    return this.storeGroupService.getStoreGroups(code);
  }

  /**
   * get store group list
   * @returns get store group list
   */
  @Get("list")
  @Scopes(Scope.GET)
  @Public()
  async list() {
    return this.storeGroupService.getStoreGroups();
  }

  /**
   * get store group list by ids
   * @returns get store group list
   */
  @Post("get-by-ids")
  @Scopes(Scope.POST)
  @InternalAccess()
  async getByIds(@Body() param: GetSelectedStoreGroupDto) {
    return this.storeGroupService.getStoreGroupByIds(param);
  }
}
