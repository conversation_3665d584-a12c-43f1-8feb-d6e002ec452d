import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export class CreateStoreGroupDto {
  @ApiProperty({ required: true, description: "Store group name" })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ required: true, description: "Store group description" })
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty({ required: true, example: "* or 34997,35*" })
  @IsNotEmpty()
  @IsString()
  inclStore: string;

  @ApiProperty({ required: false, example: "34997,35*" })
  @IsOptional()
  @IsString()
  exclStore: string;
}
