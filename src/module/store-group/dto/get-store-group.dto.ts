import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsArray, ArrayMinSize } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { TransformArray } from "src/decorator/transform-array.decorator";

export class GetStoreGroupDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  store?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  keyword?: string;
}
export class GetSelectedStoreGroupDto {
  @ApiProperty({
    required: false,
    default: null,
    example: '["65a4b778dcc6792340095afc","65a4c165c39c62bfa901b3d5"]',
  })
  @IsArray()
  @IsOptional()
  @TransformArray()
  @ArrayMinSize(1)
  store_group: Record<string, any>;
}
