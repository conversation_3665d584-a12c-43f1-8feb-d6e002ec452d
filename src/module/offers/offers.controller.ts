import { Controller, Get, Param } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";
import { OffersCategoryService } from "./offers-category.service";
import { OffersService } from "./offers.service";

@ApiTags("Offers")
@Controller("offers")
// TODO : resources
export class OffersController {
  constructor(
    private readonly carouselService: OffersService,
    private readonly categoryService: OffersCategoryService,
  ) {}

  @Get("/categories")
  @Public()
  findCategories() {
    return this.categoryService.findAll();
  }

  @Get(":categoryId")
  @Public()
  find(@Param("categoryId") categoryId: string) {
    return this.carouselService.findAll(+categoryId);
  }

  // @Post()
  // create(@Body() createCarouselDto: CreateCarouselDto) {
  //   return this.carouselService.create(createCarouselDto);
  // }

  // @Patch(":id")
  // update(@Param("id") id: string, @Body() updateCarouselDto: UpdateCarouselDto) {
  //   return this.carouselService.update(+id, updateCarouselDto);
  // }

  // @Delete(":id")
  // remove(@Param("id") id: string) {
  //   return this.carouselService.remove(+id);
  // }
}
