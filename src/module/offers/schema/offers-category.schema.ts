import { Prop, raw, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
@Schema({
  timestamps: true,
})
export class OffersCategory extends Document {
  @Prop()
  id: number;

  @Prop()
  created_at: Date;

  @Prop()
  name: string;

  @Prop()
  updated_at: Date;
}

export type OffersCategoryDocument = OffersCategory & Document;

export const OffersCategorySchema = SchemaFactory.createForClass(OffersCategory);

OffersCategorySchema.plugin(mongoosePaginate);
