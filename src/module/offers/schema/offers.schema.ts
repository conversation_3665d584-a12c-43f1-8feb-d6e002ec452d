import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
@Schema({
  timestamps: true,
})
export class Offers extends Document {
  @Prop()
  id: number;

  @Prop()
  created_at: Date;

  @Prop()
  image: string;

  @Prop()
  position: number;

  @Prop()
  response_id: string;

  @Prop()
  response_type: string;

  @Prop()
  start_date: Date;

  @Prop()
  status: string;

  @Prop()
  title: string;

  @Prop()
  updated_at: Date;

  @Prop()
  link: string;

  @Prop()
  id_promo_banner_category: number;
}

export type OffersDocument = Offers & Document;

export const OffersSchema = SchemaFactory.createForClass(Offers);
