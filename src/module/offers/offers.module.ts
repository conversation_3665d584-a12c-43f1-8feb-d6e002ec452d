import { OffersService } from "./offers.service";
import { OffersController } from "./offers.controller";
import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { OffersCategoryService } from "./offers-category.service";
import { OffersCategorySchema } from "./schema/offers-category.schema";
import { OffersSchema } from "./schema/offers.schema";
import { ApmModule } from "modules/nestjs-elastic-apm";

@Module({
  controllers: [OffersController],
  providers: [OffersService, OffersCategoryService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "Offers",
        schema: OffersSchema,
      },
      {
        name: "OffersCategory",
        schema: OffersCategorySchema,
      },
    ]),
    ApmModule.register(),
  ],
})
export class OffersModule {}
