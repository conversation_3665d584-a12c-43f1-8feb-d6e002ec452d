import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { OffersDocument } from "./schema/offers.schema";

@Injectable()
export class OffersService {
  constructor(@InjectModel("Offers") private carouselModel: Model<OffersDocument>) {}

  async findAll(categoryId: number) {
    return this.carouselModel
      .find({ status: "Active", id_promo_banner_category: categoryId })
      .sort({ position: -1 })
      .lean()
      .exec();
  }
}
