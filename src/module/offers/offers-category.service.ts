import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { OffersCategoryDocument } from "./schema/offers-category.schema";

@Injectable()
export class OffersCategoryService {
  constructor(@InjectModel("OffersCategory") private model: Model<OffersCategoryDocument>) {}

  async findAll() {
    return this.model.find({ status: 1 }).sort({ orders: -1 }).exec();
  }
}
