import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { Public, Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { Role, Controllers, Scope } from "../enum";
import { CreateStoreDto } from "./dto/create-store.dto";
import { FilterStoreDto } from "./dto/filter-store.dto";
import { UpdateStoreDto } from "./dto/update-store.dto";
import { StoreService } from "./store.service";

@ApiTags("Admin - Stores")
@ApiBearerAuth("access-token")
@Controller("admin/store")
@Resource(Controllers.ADMIN_STORE)
export class AdminStoreController {
  constructor(private readonly storeService: StoreService) {}

  @Post()
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @Scopes(Scope.POST)
  create(@Body() payload: CreateStoreDto) {
    return this.storeService.create(payload);
  }

  @ApiOperation({
    description: `
    things to search with keyword:
    - store name: (substring)
    - store code: (exact match)
    - city: (substring)
    - state: (substring)

    *search methods:
    exact match = you're expected to provide a complete data to query. i.e: 14144 (store code)
    substring = partial and insensitive-case query is allowed. i.e: MEDAN/medan/Medan (city); Boemi Kedaton Lampung (store name)
    `,
  })
  @Get()
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @Scopes(Scope.GET)
  findAll(@Query() filter: FilterStoreDto) {
    return this.storeService.findAllAdmin(filter, filter.page, filter.limit, filter.sort, filter.radius);
  }

  @Get(":id")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.storeService.findById(id);
  }

  @Patch(":id")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @Scopes(Scope.PATCH)
  update(@Param("id") id: string, @Body() updateStoreDto: UpdateStoreDto) {
    return this.storeService.update(id, updateStoreDto);
  }

  @Delete(":id")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @Scopes(Scope.DELETE)
  remove(@Param("id") id: string): Promise<any> {
    return this.storeService.remove(id);
  }

  @Post("sync-gold-store")
  @Public()
  syncGoldStore() {
    return this.storeService.syncStoreGold();
  }

  @Post("sync-gold-store/:storeCode")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @Scopes(Scope.POST)
  syncDataEmployee(@Param("storeCode") storeCode: string) {
    return this.storeService.syncSingleStore(storeCode);
  }
}
