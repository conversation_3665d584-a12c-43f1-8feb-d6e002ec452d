import { HttpException, HttpStatus, Inject, Injectable, forwardRef } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import * as dayjs from "dayjs";
import * as timezone from "dayjs/plugin/timezone";
import * as utc from "dayjs/plugin/utc";
import mongoose, { PaginateModel } from "mongoose";
import { TbsSiteConfigService } from "tbs-site-config";
import { ChannelHeader, ChannelHeaderReverse } from "../enum/channel-header.enum";
import { StoreClass } from "../enum/store-class.enum";
import { PaymentService } from "../microservices/payments/payment.service";
import { StoreGroupService } from "../store-group/store-group.service";
import { CreateStoreDto } from "./dto/create-store.dto";
import { FilterStoreDto } from "./dto/filter-store.dto";
import { UpdateStoreDto } from "./dto/update-store.dto";
import { GoldStoreDocument } from "./schema/gold-store.schema";
import { Store, StoreDocument } from "./schema/store.schema";
dayjs.extend(utc);
dayjs.extend(timezone);
@Injectable()
export class StoreService {
  constructor(
    @InjectModel(Store.name) private storeModel: PaginateModel<StoreDocument>,
    @InjectModel("GoldStore") private goldStoreModel: PaginateModel<GoldStoreDocument>,
    private readonly paymentService: PaymentService,
    @Inject(forwardRef(() => StoreGroupService))
    private readonly storeGroupService: StoreGroupService,
    private readonly siteConfigService: TbsSiteConfigService,
  ) {}

  async create(createStoreDto: CreateStoreDto): Promise<Store> {
    const existingStore = await this.storeModel.findOne({ name: createStoreDto.name }).exec();

    if (existingStore) {
      throw new HttpException("Store is already exist", HttpStatus.BAD_REQUEST);
    }

    /** Validate payment method code */
    if (createStoreDto.edc) {
      for (const edc of createStoreDto.edc) {
        const paymentMethod = await this.paymentService.verifyPayment(edc.paymentMethodCode, ChannelHeader.POS);
        if (!paymentMethod) {
          throw new HttpException("Payment method not found", HttpStatus.BAD_REQUEST);
        }
        edc["paymentMethodName"] = paymentMethod.name;
      }
    }

    const newStore = new this.storeModel(createStoreDto);
    newStore.location = {
      type: "Point",
      coordinates: [createStoreDto.lng, createStoreDto.lat],
    };

    await newStore.save();
    await this.storeGroupService.refreshAllStore();
    return newStore;
  }

  distance(lat1, lon1, lat2, lon2): number {
    const p = 0.017453292519943295; // Math.PI / 180
    const c = Math.cos;
    const a = 0.5 - c((lat2 - lat1) * p) / 2 + (c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p))) / 2;

    return 12742 * Math.asin(Math.sqrt(a)); // 2 * R; R = 6371 km
  }

  async findAll(filter: FilterStoreDto, page = 1, limit = 10, sort: string, radius: number) {
    const maxDistanceInMeters = radius ? radius : 10000;
    let filterLocation = {};
    let filterKeyword = {};
    if (filter.keyword) {
      filterKeyword = {
        $or: [
          { name: { $regex: filter.keyword, $options: "i" } },
          { storeCode: filter.keyword },
          { city: { $regex: filter.keyword, $options: "i" } },
        ],
      };
    }
    if (filter.lat && filter.lng) {
      filterLocation = {
        location: {
          $near: {
            $geometry: {
              type: "Point",
              coordinates: [filter.lng, filter.lat],
            },
            $maxDistance: maxDistanceInMeters,
          },
        },
      };
    }

    const filterStoreClass = { storeClass: { $nin: [StoreClass.BAZAAR, StoreClass.OUTLET, StoreClass.TestMarket] } };

    const filterStatus = { status: 1 };

    const query = Object.assign({}, filterKeyword, filterLocation, filterStoreClass, filterStatus);

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
      lean: true,
    };

    const result = await this.storeModel.paginate(query, options);
    // return distance in km

    result.docs.forEach((e) => {
      Object.assign(e, {
        distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
      });
    });

    return result;
  }

  async findAllAdmin(filter: FilterStoreDto, page = 1, limit = 10, sort: string, radius: number) {
    const maxDistanceInMeters = radius ? radius : 10000;
    let filterLocation = {};
    let filterKeyword = {};
    if (filter.keyword) {
      filterKeyword = {
        $or: [
          { name: { $regex: filter.keyword, $options: "i" } },
          { city: { $regex: filter.keyword, $options: "i" } },
          { state: { $regex: filter.keyword, $options: "i" } },
          { storeCode: filter.keyword },
        ],
      };
    }
    if (filter.lat && filter.lng) {
      filterLocation = {
        location: {
          $near: {
            $geometry: {
              type: "Point",
              coordinates: [filter.lng, filter.lat],
            },
            $maxDistance: maxDistanceInMeters,
          },
        },
      };
    }

    const query = Object.assign({}, filterKeyword, filterLocation);
    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    const result = await this.storeModel.paginate(query, options);

    result.docs.forEach((e) => {
      Object.assign(e, {
        distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
      });
    });

    return result;
  }

  async findByStoreCode(storeCode: string) {
    return await this.storeModel.findOne({ storeCode: storeCode });
  }

  async findById(id: string) {
    let store;
    if (!mongoose.Types.ObjectId.isValid(id)) {
      store = await this.storeModel.findOne({ storeCode: id }).populate("inStoreServices");
    } else {
      store = this.storeModel.findById(id).populate("inStoreServices");
    }

    if (!store) {
      throw new HttpException("Store tidak ditemukan", HttpStatus.BAD_REQUEST);
    }

    return store;
  }

  async update(id: string, payload: UpdateStoreDto) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    /** Validate payment method code */
    if (payload.edc) {
      for (const edc of payload.edc) {
        const paymentMethod = await this.paymentService.verifyPayment(edc.paymentMethodCode, ChannelHeader.POS);
        if (!paymentMethod) {
          throw new HttpException("Payment method not found", HttpStatus.BAD_REQUEST);
        }
        edc["paymentMethodName"] = paymentMethod.name;
      }
    }

    if (payload.lat && payload.lng) {
      payload.location = {
        type: "Point",
        coordinates: [payload.lng, payload.lat],
      };
    }

    const update = await this.storeModel.updateOne({ _id: id }, payload).exec();

    if (!update) {
      throw "Update data failed.";
    }

    await this.storeGroupService.refreshAllStore();
    return this.storeModel.findById(id);
  }

  async remove(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const deleting = await this.storeModel.deleteOne({ _id: id }).exec();
    await this.storeGroupService.refreshAllStore();
    return deleting;
  }

  async syncStoreGold() {
    const ppn = await this._getConfig("store.ppn", 11);
    const goldStores = await this.goldStoreModel.find();
    console.log(`Running sync store : ${goldStores.length}`);
    for (const goldStore of goldStores) {
      try {
        const lat = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[0].trim()) : 0.0;
        const lng = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[1].trim()) : 0.0;
        const payload = {
          storeCode: goldStore.storeCode,
          storeClass: goldStore.storeClass,
          name: goldStore.name,
          address: `${(goldStore.address1, goldStore.address2)}`,
          phone: goldStore.phone,
          email: goldStore.email,
          countryId: "ID",
          city: goldStore.city,
          lat: lat,
          lng: lng,
          status: dayjs().isAfter(dayjs(goldStore.closingDate)) ? 0 : 1,
          vat: goldStore.vat,
          ppn: goldStore.vat ? ppn : 0,
          location: {
            type: "Point",
            coordinates: [lng, lat],
          },
          pkpNo: goldStore.pkpNo,
          pkpDate: dayjs(goldStore.pkpDate).tz("Asia/Jakarta").format("DD/MM/YYYY"),
          ptkpName: goldStore.ptkpName,
          ptkpAddress: goldStore.ptkpAddress,
          closingDate: goldStore.closingDate,
        };

        await this.storeModel.findOneAndUpdate({ storeCode: goldStore.storeCode }, payload, {
          new: true,
          upsert: true,
        });
        await this.storeGroupService.refreshAllStore();
      } catch (e) {
        console.log(`Error sync store :${goldStore.storeCode} - ${goldStore.name}, Error : ${e}`);
      }
    }
  }

  async syncSingleStore(storeCode: string) {
    const ppn = this._getConfig("store.ppn", 11);
    const goldStore = await this.goldStoreModel.findOne({ storeCode: storeCode });
    const lat = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[0].trim()) : 0.0;
    const lng = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[1].trim()) : 0.0;
    const payload = {
      storeCode: goldStore.storeCode,
      storeClass: goldStore.storeClass,
      name: goldStore.name,
      address: `${(goldStore.address1, goldStore.address2)}`,
      phone: goldStore.phone,
      email: goldStore.email,
      countryId: "ID",
      city: goldStore.city,
      lat: lat,
      lng: lng,
      status: dayjs().isAfter(dayjs(goldStore.closingDate)) ? 0 : 1,
      vat: goldStore.vat,
      ppn: goldStore.vat ? ppn : 0,
      location: {
        type: "Point",
        coordinates: [lng, lat],
      },
      pkpNo: goldStore.pkpNo,
      pkpDate: dayjs(goldStore.pkpDate).tz("Asia/Jakarta").format("DD/MM/YYYY"),
      ptkpName: goldStore.ptkpName,
      ptkpAddress: goldStore.ptkpAddress,
      closingDate: goldStore.closingDate,
    };

    const store = await this.storeModel.findOneAndUpdate({ storeCode: goldStore.storeCode }, payload, {
      new: true,
      upsert: true,
    });
    await this.storeGroupService.refreshAllStore();
    console.info(`Sync update store : ${store.storeCode} - ${store.name}`);
  }

  async findAllStorePricings() {
    return await this.storeModel.find({ status: 1, pricing_type: { $exists: true } }, "pricing_type storeCode name");
  }

  async getAllStoreCodes() {
    const result = [];
    const contents = await this.storeModel.find({ status: 1 }, { _id: 0, storeCode: 1 }).exec();
    contents.map((k) => {
      result.push(k.storeCode);
    });

    const specialChannel = Object.keys(ChannelHeaderReverse);
    specialChannel.map((ss) => {
      result.push(ChannelHeaderReverse[ss]);
    });

    return result;
  }

  private async _getConfig(key: string, _default: any) {
    let result: any;
    try {
      const config = await this.siteConfigService.get(key);
      result = JSON.parse(config).value;
    } catch (err) {
      result = _default;
    }

    return result;
  }

  async findAllV2(filter: FilterStoreDto, page = 1, limit = 10, sort: string, radius: number) {
    const maxDistanceInMeters = radius ? radius : 10000;
    let filterLocation = {};
    let filterKeyword = {};
    let filterService = {};

    // Keyword filter (existing logic)
    if (filter.keyword) {
      filterKeyword = {
        $or: [
          { name: { $regex: filter.keyword, $options: "i" } },
          { storeCode: filter.keyword },
          { city: { $regex: filter.keyword, $options: "i" } },
        ],
      };
    }

    // Location filter (existing logic)
    if (filter.lat && filter.lng) {
      filterLocation = {
        location: {
          $near: {
            $geometry: {
              type: "Point",
              coordinates: [filter.lng, filter.lat],
            },
            $maxDistance: maxDistanceInMeters,
          },
        },
      };
    }

    // Service filter (new logic)
    if (filter.serviceId) {
      filterService = {
        inStoreServices: filter.serviceId,
      };
    }

    if (filter.serviceIds?.length) {
      filterService = {
        inStoreServices: { $all: filter.serviceIds },
      };
    }

    const filterStoreClass = { storeClass: { $nin: [StoreClass.BAZAAR, StoreClass.OUTLET, StoreClass.TestMarket] } };
    const filterStatus = { status: 1 };

    const query = Object.assign({}, filterKeyword, filterLocation, filterService, filterStoreClass, filterStatus);

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
      populate: "inStoreServices", // Populate the services
    };

    const result = await this.storeModel.paginate(query, options);

    // Calculate distance in km (existing logic)
    if (filter.lat && filter.lng) {
      result.docs.forEach((e) => {
        Object.assign(e, {
          distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
        });
      });
    }

    return result;
  }

  async findAllAdminV2(filter: FilterStoreDto, page = 1, limit = 10, sort: string, radius: number) {
    const maxDistanceInMeters = radius ? radius : 10000;
    let filterLocation = {};
    let filterKeyword = {};
    let filterService = {};

    // Keyword filter (existing logic)
    if (filter.keyword) {
      filterKeyword = {
        $or: [
          { name: { $regex: filter.keyword, $options: "i" } },
          { city: { $regex: filter.keyword, $options: "i" } },
          { state: { $regex: filter.keyword, $options: "i" } },
          { storeCode: filter.keyword },
        ],
      };
    }

    // Location filter (existing logic)
    if (filter.lat && filter.lng) {
      filterLocation = {
        location: {
          $near: {
            $geometry: {
              type: "Point",
              coordinates: [filter.lng, filter.lat],
            },
            $maxDistance: maxDistanceInMeters,
          },
        },
      };
    }

    // Service filter (new logic)
    if (filter.serviceId) {
      filterService = {
        inStoreServices: filter.serviceId,
      };
    }

    if (filter.serviceIds?.length) {
      filterService = {
        inStoreServices: { $all: filter.serviceIds },
      };
    }

    const query = Object.assign({}, filterKeyword, filterLocation, filterService);

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
      populate: "inStoreServices", // Populate the services
    };

    const result = await this.storeModel.paginate(query, options);

    // Calculate distance in km (existing logic)
    if (filter.lat && filter.lng) {
      result.docs.forEach((e) => {
        Object.assign(e, {
          distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
        });
      });
    }

    return result;
  }
}
