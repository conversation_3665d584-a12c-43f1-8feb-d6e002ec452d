import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsOptional, IsString } from "class-validator";

export enum sortEnum {
  name_asc = "name_asc",
  name_dsc = "name_dsc",
  store_code_asc = "store_code_asc",
  store_code_dsc = "store_code_dsc",
  store_class_asc = "store_class_asc",
  store_class_dsc = "store_class_dsc",
  city_asc = "city_asc",
  city_dsc = "city_dsc",
  state_asc = "state_asc",
  state_dsc = "state_dsc",
}

export const sortQuery = {
  name_asc: { name: 1 },
  name_dsc: { name: -1 },
  store_code_asc: { storeCode: 1 },
  store_code_dsc: { storeCode: -1 },
  store_class_asc: { storeClass: 1 },
  store_class_dsc: { storeClass: -1 },
  city_asc: { city: 1 },
  city_dsc: { city: -1 },
  state_asc: { state: 1 },
  state_dsc: { state: -1 },
};

export class SortStoreDto {
  @ApiProperty({ required: false, enum: sortEnum })
  @IsOptional()
  @IsString()
  @IsEnum(sortEnum)
  sort?: string;
}
