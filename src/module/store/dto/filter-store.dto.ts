import { ApiProperty } from "@nestjs/swagger";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { IsOptional, IsString, IsArray } from "class-validator";

export class FilterStoreDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  lat?: number;

  @ApiProperty({ required: false })
  lng?: number;

  @ApiProperty({ required: false })
  keyword: string;

  @ApiProperty({ required: false, default: 10000 })
  radius: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  serviceId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  serviceIds?: string[];
}
