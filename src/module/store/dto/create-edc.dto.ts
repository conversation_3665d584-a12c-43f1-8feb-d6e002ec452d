import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class CreateEdcDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  paymentMethodCode: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  merchantName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  tid: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  mid: string;
}
