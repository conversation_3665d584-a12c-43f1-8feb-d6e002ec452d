import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsArray,
  IsBoolean,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";
import { IsImageUrlRegistered } from "src/decorator/image-url-validator";
import { StoreClass } from "src/module/enum/store-class.enum";
import { CreateEdcDto } from "./create-edc.dto";

export class CreateStoreDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  position: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  storeCode: string;

  @ApiProperty({
    description: "Enum Store Class",
    enum: StoreClass,
  })
  @IsEnum(StoreClass)
  storeClass: StoreClass;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @IsImageUrlRegistered()
  image: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  address: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  phone: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  email: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  countryId: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  city: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  state: string;

  @ApiProperty()
  @IsNumber()
  lat: number;

  @ApiProperty()
  @IsNumber()
  lng: number;

  @ApiProperty()
  @IsNumber()
  status: number;

  @ApiProperty()
  @IsBoolean()
  vat: boolean;

  @ApiProperty({ type: () => [CreateEdcDto] })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @IsDefined()
  @Type(() => CreateEdcDto)
  edc: CreateEdcDto[];

  @ApiProperty({ required: false })
  pricing_type: string;

  location: object;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  inStoreServices?: string[]; // Array of InStoreService IDs
}
