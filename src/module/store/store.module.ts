import { Module, forwardRef } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { PaymentModule } from "../microservices/payments/payment.module";
import { AdminStoreController } from "./admin-store.controller";
import { GoldStoreSchema } from "./schema/gold-store.schema";
import { Store, StoreSchema } from "./schema/store.schema";
import { StoreController } from "./store.controller";
import { StoreService } from "./store.service";
import { StoreGroupModule } from "../store-group/store-group.module";

@Module({
  controllers: [StoreController, AdminStoreController],
  providers: [StoreService],
  imports: [
    MongooseModule.forFeature([
      {
        name: Store.name,
        schema: StoreSchema,
      },
      {
        name: "GoldStore",
        schema: GoldStoreSchema,
      },
    ]),
    ApmModule.register(),
    PaymentModule,
    forwardRef(() => StoreGroupModule),
  ],
  exports: [StoreService],
})
export class StoreModule {}
