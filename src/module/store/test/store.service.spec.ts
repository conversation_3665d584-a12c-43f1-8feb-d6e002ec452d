import { HttpException } from "@nestjs/common";
import { getModelToken } from "@nestjs/mongoose";
import { Test, TestingModule } from "@nestjs/testing";
import { MongoMemoryServer } from "mongodb-memory-server";
import { connect, Connection, Model } from "mongoose";
import { UpdateStoreDto } from "../../carbon/carbon-quiz/dto/update-quiz.dto";
import { StoreService } from "../store.service";
import { Store, StoreSchema } from "../../carbon/schema/carbon-quiz.schema

const mockStore = {
  name: "TBS Sun Plaza Medan",
  description: "Opening hours (10.00-22.00) Every day",
  image: "https://magento-2.tbsgroup.co.id/media/magestore/storepickup/images/store/gallery/t/o/toko3.jpg",
  firstAddress: "Sun Plaza Ground Floor, Unit B 25, Jl. H. Zainul Arifin No.7 Medan",
  secondAddress: "Indonesia",
  city: "Medan",
  lat: 3.582519,
  lng: 98.671697,
  status: 1,
};

describe("StoreService", () => {
  let service: StoreService;
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let StoreModel: Model<Store>;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;
    StoreModel = mongoConnection.model(Store.name, StoreSchema);
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoreService,
        {
          provide: getModelToken(Store.name),
          useValue: StoreModel,
        },
      ],
    }).compile();

    service = module.get<StoreService>(StoreService);
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  afterEach(async () => {
    const collections = mongoConnection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  });

  it("should create Store", async () => {
    const result = await service.create(mockStore);

    expect(result.name).toEqual(mockStore.name);

    expect(result.description).toEqual(mockStore.description);
  });

  it("should throw error when Store exist", async () => {
    await new StoreModel(mockStore).save();

    try {
      await service.create(mockStore);
    } catch (e: any) {
      expect(e).toBeInstanceOf(HttpException);
      expect(e.message).toBe("Store is already exist");
    }
  });

  it("should get all Stores", async () => {
    const initial = await service.findAll();

    expect(initial.length).toEqual(0);

    await new StoreModel(mockStore).save();

    const result = await service.findAll();

    expect(result.length).toEqual(1);
  });

  it("should find Store by ID", async () => {
    const data = await new StoreModel(mockStore).save();

    const StoreExist = await service.findById(data.id);

    expect(StoreExist?.name).toEqual(data.name);
  });

  it("should update Store", async () => {
    const data = await new StoreModel(mockStore).save();

    const updateStore: UpdateStoreDto = {
      name: "update name",
    };

    const result = await service.update(data.id, updateStore);

    expect(result?.name).toEqual(updateStore.name);
  });

  it("should delete Store by ID", async () => {
    const data = await new StoreModel(mockStore).save();

    const deleting = await service.remove(data.id);

    expect(deleting.deletedCount).toEqual(1);
  });
});
