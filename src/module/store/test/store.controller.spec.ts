import { Test, TestingModule } from "@nestjs/testing";

import { StoreController } from "../store.controller";
import { StoreService } from "../store.service";

const mockStore = {
  name: "TBS Sun Plaza Medan",
  description: "Opening hours (10.00-22.00) Every day",
  image: "https://magento-2.tbsgroup.co.id/media/magestore/storepickup/images/store/gallery/t/o/toko3.jpg",
  firstAddress: "Sun Plaza Ground Floor, Unit B 25, Jl. H. Zainul Arifin No.7 Medan",
  secondAddress: "Indonesia",
  city: "Medan",
  lat: 3.582519,
  lng: 98.671697,
  status: 1,
};

jest.mock("../Store.service.ts");

describe("StoreController", () => {
  let controller: StoreController;
  let service: StoreService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [StoreService],
      controllers: [StoreController],
    }).compile();

    service = module.get<StoreService>(StoreService);
    controller = module.get<StoreController>(StoreController);
  });

  it("StoreService should defined", () => {
    expect(service).toBeDefined();
  });
  it("StoreController should defined", () => {
    expect(controller).toBeDefined();
  });

  describe("Create Store", () => {
    it("should call Store service", async () => {
      controller.create(mockStore);

      expect(service.create).toHaveBeenCalled();
    });
  });

  describe("Get All Store", () => {
    it("should call Store service", () => {
      controller.findAll();
      expect(service.findAll).toBeCalled();
    });
  });

  describe("Get Store", () => {
    it("should call Store service", () => {
      const id = "mongoId";
      controller.findOne(id);
      expect(service.findById).toBeCalled();
    });
  });

  describe("Update Store", () => {
    it("should call Store service", () => {
      controller.update("1", {});
      expect(service.update).toBeCalled();
    });
  });

  describe("Delete Store", () => {
    it("should call Store service", () => {
      controller.remove("1");
      expect(service.remove).toBeCalled();
    });
  });
});
