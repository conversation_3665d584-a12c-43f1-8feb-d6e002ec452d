import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { StoreClass } from "src/module/enum/store-class.enum";
@Schema({
  timestamps: true,
})
export class GoldStore extends Document {
  @Prop()
  storeCode: string;

  @Prop({ type: String, enum: StoreClass })
  storeClass: StoreClass;

  @Prop()
  name: string;

  @Prop()
  city: string;

  @Prop()
  address1: string;

  @Prop()
  address2: string;

  @Prop()
  longitude: string;

  @Prop()
  phone: string;

  @Prop()
  email: string;

  @Prop({ default: true })
  vat: boolean;

  @Prop()
  pkpNo: string;

  @Prop()
  pkpDate: Date;

  @Prop()
  ptkpName: string;

  @Prop()
  ptkpAddress: string;

  @Prop()
  midBca: string;

  @Prop()
  merchantName: string;

  @Prop()
  terminalId: string;

  @Prop()
  closingDate: Date;
}

export type GoldStoreDocument = GoldStore & Document;

export const GoldStoreSchema = SchemaFactory.createForClass(GoldStore);

GoldStoreSchema.plugin(mongoosePaginate);
