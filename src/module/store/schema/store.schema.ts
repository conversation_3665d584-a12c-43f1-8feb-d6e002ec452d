import { Prop, raw, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { StoreClass } from "src/module/enum/store-class.enum";
import { ConvertPathToUrl, ConvertUrlToPath } from "src/utils/function.util";
import { InStoreService } from "src/module/in-store-service/schema/in-store-service.schema";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class Store extends Document {
  @Prop({ required: true, unique: true, type: String, trim: true })
  storeCode: string;

  @Prop()
  name: string;

  @Prop()
  position: number;

  @Prop()
  description: string;

  @Prop({ get: ConvertPathToUrl, set: ConvertUrlToPath })
  image: string;

  @Prop()
  address: string;

  @Prop()
  phone: string;

  @Prop()
  email: string;

  @Prop()
  countryId: string;

  @Prop()
  city: string;

  @Prop()
  state: string;

  @Prop()
  lat: number;

  @Prop()
  lng: number;

  @Prop({ default: true })
  vat: boolean;

  @Prop()
  ppn: number;

  @Prop({ default: 1 })
  status: number;

  @Prop({ type: String, enum: StoreClass })
  storeClass: StoreClass;

  @Prop(
    raw({
      type: { type: String, default: "Point" },
      coordinates: { type: [Number] },
    }),
  )
  location: Record<string, any>;

  @Prop()
  pkpNo: string;

  @Prop()
  pkpDate: string;

  @Prop()
  ptkpName: string;

  @Prop()
  ptkpAddress: string;

  @Prop()
  midBca: string;

  @Prop()
  merchantName: string;

  @Prop()
  terminalId: string;

  @Prop()
  closingDate: Date;

  @Prop(
    raw({
      paymentMethodCode: { type: String },
      paymentMethodName: { type: String },
      merchantName: { type: String },
      tid: { type: String },
      mid: { type: String },
    }),
  )
  edc: Record<string, any> = [];

  @Prop()
  pricing_type: string;

  @Prop([{ type: MongooseSchema.Types.ObjectId, ref: InStoreService.name }])
  inStoreServices: InStoreService[];
}

export type StoreDocument = Store & Document;

export const StoreSchema = SchemaFactory.createForClass(Store);

StoreSchema.plugin(mongoosePaginate);
StoreSchema.index({ location: "2dsphere" });
StoreSchema.index({ inStoreServices: 1 }); // Add index for inStoreServices
