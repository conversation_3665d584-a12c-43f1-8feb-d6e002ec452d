import { Controller, Get, Param, Query } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON>erAuth, ApiTags, ApiOperation } from "@nestjs/swagger";
import { Public, InternalAccess } from "keycloak-connect-tbs";
import { FilterStoreDto } from "./dto/filter-store.dto";
import { StoreService } from "./store.service";

@ApiTags("Stores")
@ApiBearerAuth("access-token")
@Controller("store")
export class StoreController {
  constructor(private readonly storeService: StoreService) {}

  @Get()
  @Public()
  @InternalAccess()
  findAll(@Query() filter: FilterStoreDto) {
    return this.storeService.findAll(filter, filter.page, filter.limit, filter.sort, filter.radius);
  }

  @Get("v2")
  @Public()
  @InternalAccess()
  @ApiOperation({
    description:
      "Get stores with support for in-store service filtering. Can filter by single serviceId or multiple serviceIds",
  })
  findAllV2(@Query() filter: FilterStoreDto) {
    return this.storeService.findAllV2(filter, filter.page, filter.limit, filter.sort, filter.radius);
  }

  @Get("/pricings")
  @InternalAccess()
  findAllStorePricings() {
    return this.storeService.findAllStorePricings();
  }

  @ApiTags("rate-limit:strict")
  @Get(":id")
  @Public()
  findOne(@Param("id") id: string) {
    return this.storeService.findById(id);
  }
}
