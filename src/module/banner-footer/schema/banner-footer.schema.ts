import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { SchemaTypes } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";

@Schema({ timestamps: { createdAt: true, updatedAt: true } })
export class BannerFooter {
  @Prop({
    type: SchemaTypes.Mixed,
    raw: {
      web: { type: String },
      apps: { type: String },
    },
  })
  image: Record<string, any>;

  @Prop({ type: SchemaTypes.String })
  url: string;

  @Prop({ type: SchemaTypes.Date })
  startDate;

  @Prop({ type: SchemaTypes.Date })
  endDate;

  @Prop({ type: SchemaTypes.Boolean })
  status;
}

export type BannerFooterDocument = BannerFooter & Document;

export const BannerFooterSchema = SchemaFactory.createForClass(BannerFooter);

BannerFooterSchema.plugin(mongoosePaginate);
