import { HttpException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { CreateBannerFooterDto } from "./dto/create-banner-footer.dto";
import { GetAllBannerFooterDto } from "./dto/get-all-banner-footer.dto";
import { UpdateBannerFooterDto } from "./dto/update-banner-footer.dto";
import { BannerFooter } from "./schema/banner-footer.schema";

@Injectable()
export class BannerFooterService {
  constructor(
    @InjectModel(BannerFooter.name)
    private readonly bannerFooterModel: PaginateModel<BannerFooter>,
  ) {}
  async create(createBannerFooterDto: CreateBannerFooterDto, imagePayload) {
    return await this.bannerFooterModel.create({ ...createBannerFooterDto, ...imagePayload });
  }

  async findAll(params: GetAllBannerFooterDto) {
    const { page = 1, limit = 10, sort } = params;

    let filter: Record<string, any> = {};

    if (params.url) filter.url = new RegExp(params.url, "i");
    if (params.status) filter.status = params.status;
    if (params.keyword) {
      filter = {
        $or: [
          { "image.app": { $regex: filter.keyword, $options: "i" } },
          { "image.web": { $regex: filter.keyword, $options: "i" } },
          { url: { $regex: filter.keyword, $options: "i" } },
        ],
      };
    }

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    return await this.bannerFooterModel.paginate(filter, options);
  }

  async findOne(id) {
    return await this.bannerFooterModel.findById(id);
  }

  async update(id: string, updateBannerFooterDto: UpdateBannerFooterDto, webUrl = "", appUrl = "") {
    const data = await this.bannerFooterModel.findById(id);

    if (!data) {
      throw new HttpException("Data not found", 400);
    }

    const image = {
      web: data?.image?.web || "",
      apps: data?.image?.apps || "",
    };

    if (webUrl) {
      image.web = webUrl;
    }

    if (appUrl) {
      image.apps = appUrl;
    }

    console.log({ image });

    return await this.bannerFooterModel.findByIdAndUpdate(id, { ...updateBannerFooterDto, image });
  }

  async remove(id) {
    return await this.bannerFooterModel.findByIdAndDelete(id);
  }

  async getBannerForHome() {
    const result = await this.bannerFooterModel.find(
      {
        status: true,
        startDate: { $lte: new Date() },
        endDate: { $gte: new Date() },
      },
      {},
      { sort: { createdAt: -1 } },
    );
    return !result.length ? {} : result[0];
  }
}
