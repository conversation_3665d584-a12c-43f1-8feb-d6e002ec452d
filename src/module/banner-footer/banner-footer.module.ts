import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { Amazons3Module } from "src/amazon-s3/amazons3.module";
import { BannerFooterController } from "./banner-footer.controller";
import { BannerFooterService } from "./banner-footer.service";
import { BannerFooter, BannerFooterSchema } from "./schema/banner-footer.schema";

@Module({
  controllers: [BannerFooterController],
  providers: [BannerFooterService],
  imports: [MongooseModule.forFeature([{ name: BannerFooter.name, schema: BannerFooterSchema }]), Amazons3Module],
  exports: [BannerFooterService],
})
export class BannerFooterModule {}
