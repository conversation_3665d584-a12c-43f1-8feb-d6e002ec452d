import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";

export class GetAllBannerFooterDto extends PaginationParamDto {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  keyword;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  url;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  status;
}
