import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsNotEmpty, IsOptional } from "class-validator";

// class ImageData {
//   @ApiProperty({ required: true, type: "string", format: "binary" })
//   @IsNotEmpty()
//   web: string;

//   @ApiProperty({ required: true })
//   @IsNotEmpty()
//   apps: string;
// }

export class CreateBannerFooterDto {
  @ApiProperty({ type: String, required: false, format: "binary" })
  // @ValidateNested({ each: true })
  @IsOptional()
  web_image: string;

  @ApiProperty({ type: String, required: false, format: "binary" })
  // @ValidateNested({ each: true })
  @IsOptional()
  apps_image: string;

  @ApiProperty({ type: String, required: true })
  @IsNotEmpty()
  url: string;

  @ApiProperty({ type: Date, required: true })
  @Transform(({ value }) => new Date(value))
  @IsNotEmpty()
  @IsDate()
  startDate: Date;

  @ApiProperty({ type: Date, required: true })
  @IsNotEmpty()
  @Transform(({ value }) => new Date(value))
  @IsDate()
  endDate: Date;

  @ApiProperty({ type: Boolean, required: true })
  @IsNotEmpty()
  status: boolean;
}
