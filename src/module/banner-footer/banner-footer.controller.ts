import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  UploadedFiles,
  UseInterceptors,
} from "@nestjs/common";
import { FileFieldsInterceptor } from "@nestjs/platform-express";
import { ApiConsumes, ApiTags } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";
import * as path from "path";
import { AmazonS3Services } from "src/amazon-s3/amazons3.service";
import { BannerFooterService } from "./banner-footer.service";
import { CreateBannerFooterDto } from "./dto/create-banner-footer.dto";
import { GetAllBannerFooterDto } from "./dto/get-all-banner-footer.dto";
import { UpdateBannerFooterDto } from "./dto/update-banner-footer.dto";

@Controller("banner-footer")
@ApiTags("Banner Footer")
// @ApiBearerAuth("access-token")
@Public()
export class BannerFooterController {
  constructor(
    private readonly bannerFooterService: BannerFooterService,
    private readonly s3Services: AmazonS3Services,
  ) {}

  @Post()
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: "web_image", maxCount: 1 },
      { name: "apps_image", maxCount: 1 },
    ]),
  )
  async create(
    @Body() body: CreateBannerFooterDto,
    @UploadedFiles() files: { web_image?: Express.Multer.File; apps_image?: Express.Multer.File },
  ) {
    const module = "banner-footer";

    if (!files.web_image || !files.apps_image) {
      throw new HttpException("Web or apps image cannot be empty.", HttpStatus.BAD_REQUEST);
    }

    const [webUrl, appUrl] = await Promise.all([
      await this._uploadFile(module, files.web_image[0]),
      await this._uploadFile(module, files.apps_image[0]),
    ]);

    const imagePayload = {
      image: {
        web: webUrl.Location,
        apps: appUrl.Location,
      },
    };

    return this.bannerFooterService.create(body, imagePayload);
  }

  @Get()
  findAll(@Query() query: GetAllBannerFooterDto) {
    return this.bannerFooterService.findAll(query);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.bannerFooterService.findOne(id);
  }

  @Patch(":id")
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: "web_image", maxCount: 1 },
      { name: "apps_image", maxCount: 1 },
    ]),
  )
  @ApiConsumes("multipart/form-data")
  async update(
    @Param("id") id: string,
    @Body() updateBannerFooterDto: UpdateBannerFooterDto,
    @UploadedFiles() files: { web_image?: Express.Multer.File; apps_image?: Express.Multer.File },
  ) {
    const module = "banner-footer";

    let webUrl = "";
    let appUrl = "";

    if (files.web_image) {
      const res = await this._uploadFile(module, files.web_image[0]);
      webUrl = res.Location;
    }

    if (files.apps_image) {
      const res = await this._uploadFile(module, files.apps_image[0]);
      appUrl = res.Location;
    }

    return await this.bannerFooterService.update(id, updateBannerFooterDto, webUrl, appUrl);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.bannerFooterService.remove(id);
  }

  private async _uploadFile(module: string, file: Express.Multer.File) {
    const ext = path.extname(file.originalname);
    const mimetype = file.mimetype;
    return await this.s3Services.uploadFile(file.buffer, ext, module + "/", mimetype);
  }
}
