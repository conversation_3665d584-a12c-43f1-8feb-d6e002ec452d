export enum PushNotifTransport {
  TOPIC = "topic",
  DEVICE_ID = "device_id",
}

export enum PushNotifType {
  PROMOTION = "promotion",
  ORDER = "order",
  PURCHASE = "purchase",
}

export const PushNotifTopic = {
  PUSH_BY_TOPIC: process.env.KAFKA_TOPIC_PREFIX + "-push.topic",
  PUSH_BY_SENDER: process.env.KAFKA_TOPIC_PREFIX + "-push.sender",
};

export enum CreatedFrom {
  CMS = "cms",
  USER_ACTION = "user-action",
}
