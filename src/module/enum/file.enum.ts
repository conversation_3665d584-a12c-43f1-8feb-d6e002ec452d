export enum AllowedType {
  PRODUCT_REVIEW = "product/review",
  PRODUCT_IMAGE = "product/image",
  PRODUCT_FLASH_SALE = "product/flash-sale",
  USER_PROFILE = "user/profile",
  PAYMENT_METHOD = "payment/method",
  E_RECEIPT = "payment/e-receipt",
  CAROUSEL = "util/carousel",
  POS = "util/pos",
  MEGA_MENU = "util/mega-menu",
  NOTIFICATION = "notification-assets",
}

export enum AllowedExt {
  PRODUCT_REVIEW = ".jpg,.jpeg,.png,.gif",
  PRODUCT_IMAGE = ".jpg,.jpeg,.png,.gif",
  PRODUCT_FLASH_SALE = ".jpg,.jpeg,.png,.gif",
  USER_PROFILE = ".jpg,.jpeg,.png,.gif",
  PAYMENT_METHOD = ".jpg,.jpeg,.png,.gif",
  E_RECEIPT = ".pdf",
  CAROUSEL = ".jpg,.jpeg,.png,.gif",
  POS = ".jpg,.jpeg,.png,.gif,.mp4",
  MEGA_MENU = ".jpg,.jpeg,.png,.gif",
  NOTIFICATION = ".jpg,.jpeg,.png",
}

export enum AllowedMaxSize {
  PRODUCT_REVIEW = "300 kb",
  PRODUCT_IMAGE = "300 kb",
  PRODUCT_FLASH_SALE = "300 kb",
  USER_PROFILE = "300 kb",
  PAYMENT_METHOD = "300 kb",
  E_RECEIPT = "300 kb",
  CAROUSEL = "300 kb",
  POS = "300 kb",
  MEGA_MENU = "300 kb",
  NOTIFICATION = "1 mb",
}
