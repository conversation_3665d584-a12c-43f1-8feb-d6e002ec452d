export enum FcmPublisherType {
  MEMBER_TIER = "MEMBER_TIER",
  CARD_NUMBER = "CARD_NUMBER",
  GENDER = "GENDER",
  DOB = "DOB",
  UNSUBSCRIBE_ALL = "UNSUBSCRIBE_ALL",
  ADD_CART = "ADD_CART",
  REMOVE_CART = "REMOVE_CART",
  REMOVE_ALL_CART = "REMOVE_ALL_CART",
  ADD_WISHLIST = "ADD_WISHLIST",
  REMOVE_WISHLIST = "REMOVE_WISHLIST",
  LAST_PURCHASE_DATE = "LAST_PURCHASE_DATE",
  LAST_PURCHASE_CITY = "LAST_PURCHASE_CITY",
  LAST_PURCHASE_REGION = "LAST_PURCHASE_REGION",
  ALL = "ALL",
}

export enum UserDefinedTopic {
  MEMBER_TIER = "MEMBER_TIER",
  CARD_NUMBER = "CARD_NUMBER",
  GENDER = "GENDER",
  DOB = "DOB",
  ALL = "ALL",
}

export const FcmPublisherTopic = {
  ALL: process.env.KAFKA_TOPIC_PREFIX + "-fcm.all",
  MEMBER_TIER: process.env.KAFKA_TOPIC_PREFIX + "-fcm.member",
  CARD_NUMBER: process.env.KAFKA_TOPIC_PREFIX + "-fcm.card_number",
  GENDER: process.env.KAFKA_TOPIC_PREFIX + "-fcm.gender",
  DOB: process.env.KAFKA_TOPIC_PREFIX + "-fcm.dob",
  UNSUBSCRIBE_ALL: process.env.KAFKA_TOPIC_PREFIX + "-fcm.unsubscribe_all",
  ADD_CART: process.env.KAFKA_TOPIC_PREFIX + "-fcm.add_to_cart",
  REMOVE_CART: process.env.KAFKA_TOPIC_PREFIX + "-fcm.remove_from_cart",
  REMOVE_ALL_CART: process.env.KAFKA_TOPIC_PREFIX + "-fcm.remove_all_cart",
  ADD_WISHLIST: process.env.KAFKA_TOPIC_PREFIX + "-fcm.add_to_wishlist",
  REMOVE_WISHLIST: process.env.KAFKA_TOPIC_PREFIX + "-fcm.remove_from_wishlist",
  LAST_PURCHASE_DATE: process.env.KAFKA_TOPIC_PREFIX + "-fcm.last_purchase_date",
  LAST_PURCHASE_CITY: process.env.KAFKA_TOPIC_PREFIX + "-fcm.last_purchase_city",
  LAST_PURCHASE_REGION: process.env.KAFKA_TOPIC_PREFIX + "-fcm.last_purchase_region",
};

export enum FcmPublisherMongoTopic {
  ALL = "all",
  MEMBER_TIER = "member",
  CARD_NUMBER = "card_number",
  GENDER = "gender",
  DOB = "dob",
  ADD_CART = "add_to_cart",
  REMOVE_CART = "add_to_cart",
  REMOVE_ALL_CART = "add_to_cart",
  ADD_WISHLIST = "add_to_wishlist",
  REMOVE_WISHLIST = "add_to_wishlist",
  LAST_PURCHASE_DATE = "last_purchase_date",
  LAST_PURCHASE_CITY = "last_purchase_city",
  LAST_PURCHASE_REGION = "last_purchase_region",
}

export const FcmPublisherMongoTopicReverse = {
  all: "ALL",
  member: "MEMBER_TIER",
  card_number: "CARD_NUMBER",
  gender: "GENDER",
  dob: "DOB",
  add_to_cart: "ADD_CART",
  add_to_wishlist: "ADD_WISHLIST",
  last_purchase_date: "LAST_PURCHASE_DATE",
  last_purchase_city: "LAST_PURCHASE_CITY",
  last_purchase_region: "LAST_PURCHASE_REGION",
};

/**
 * Is This Topic Must Be Sent to FCM Services With Topic Or Direct FCM.
 * true = sent with Topic
 * false = sent with Direct FCM
 */
export const TopicSendMapping = {
  ALL: true,
  MEMBER_TIER: true,
  CARD_NUMBER: false,
  GENDER: true,
  DOB: false,
  ADD_CART: false,
  ADD_WISHLIST: false,
  LAST_PURCHASE_DATE: false,
  LAST_PURCHASE_CITY: false,
  LAST_PURCHASE_REGION: false,
};

export const KafkaTopic = {
  USER_FIRST_LOGIN_MOBILE: process.env.KAFKA_TOPIC_PREFIX + "-user.first-login-mobile",
};

export const KafkaClientName = "kafka-client";
