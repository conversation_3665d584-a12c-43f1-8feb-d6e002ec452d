export enum Scope {
  POST = "create",
  PUT = "update",
  PATCH = "update",
  GET = "read",
  DELETE = "delete",
}

export enum Controllers {
  BLOG = "res:utils:blog",
  CARBON = "res:utils:carbon",
  CAROUSEL = "res:utils:carousel",
  FILE_UPLOADER = "res:utils:file-uploader",
  HOME = "res:utils:home",
  NEWSLETTER = "res:utils:newsletter",
  OFFERS = "res:utils:offers",
  ADMIN_SITE_CONFIGS = "res:utils:admin-site-configs",
  SITE_CONFIGS = "res:utils:site-configs",
  ADMIN_STORE = "res:utils:admin-store",
  STORE = "res:utils:store",
  ADMIN_SCREEN = "res:utils:admin-screen",
  SCREEN = "res:utils:screen",
  ADMIN_BUILDERIO = "res:utils:admin-builder-io",
  INBOX = "res:utils:inbox",
  BLOG_COLLECTION = "res:utils:blog-collection",
  PUSH_NOTIFICATION = "res:utils:push-push-notif-sender",
  FCM_PUBLISHER = "res:utils:fcm-publisher",
  ADMIN_CRM_HOME_CONFIG = "res:utils:crm-admin",
  ADMIN_NOTIFICATION = "res:utils:admin-notification",
  NOTIFICATION = "res:utils:notification",
  EMAIL_NOTIFICATION = "res:utils:email-notification",
  EMAIL_NOTIFICATION_TEMPLATE = "res:utils:email-template",
  ADMIN_STORE_GROUP = "res:utils:admin-store-group",
  STORE_GROUP = "res:utils:store-group",
  ADMIN_IN_STORE_SERVICE = "res:utils:admin-in-store-service",
  IN_STORE_SERVICE = "res:utils:in-store-service",
  ADMIN_PLAYLIST = "res:utils:admin-playlist",
  PLAYLIST = "res:utils:playlist",
}
