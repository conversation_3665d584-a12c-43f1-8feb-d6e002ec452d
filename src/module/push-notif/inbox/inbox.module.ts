import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { InboxService } from "./inbox.service";
import { InboxController } from "./inbox.controller";
import { ConfigModule } from "@nestjs/config";
import { ElasticSearchModule } from "../../elastic-search/elastic-search.module";
import { MongooseModule } from "@nestjs/mongoose";
import { FcmUserMapper, FcmUserMapperSchema } from "../fcm-user-mapper/schema/fcm-user-mapper.schema";

@Module({
  controllers: [InboxController],
  imports: [
    ConfigModule,
    ElasticSearchModule,
    MongooseModule.forFeature([
      {
        name: FcmUserMapper.name,
        schema: FcmUserMapperSchema,
      },
    ]),
  ],
  providers: [InboxService],
})
export class InboxModule {}
