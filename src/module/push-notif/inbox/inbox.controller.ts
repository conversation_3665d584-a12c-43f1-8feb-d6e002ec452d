import { <PERSON>, Get, Param, Query, Req, HttpException, HttpStatus, Post } from "@nestjs/common";
import { InboxService } from "./inbox.service";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Public, Resource } from "keycloak-connect-tbs";
import { Controllers } from "../../enum";
import { FindAllInboxDto } from "./dto/find-all-inbox.dto";
import { ReadAllInboxDto } from "./dto/read-all-inbox.dto";

@ApiTags("Inbox")
@Controller("inbox")
@ApiBearerAuth("access-token")
@Resource(Controllers.INBOX)
export class InboxController {
  constructor(private readonly inboxService: InboxService) {}

  @Get()
  @Public(false)
  findAll(@Query() query: FindAllInboxDto, @Req() req: Record<string, any>) {
    this._validateUser(req.user, query);

    return this.inboxService.findAll(query);
  }

  @Get(":id")
  @Public(false)
  findOne(@Param("id") id: string) {
    return this.inboxService.findOne(id);
  }

  @Post("read/:id")
  @Public(false)
  readSingle(@Param("id") id: string) {
    return this.inboxService.readOne(id);
  }

  @Post("read-all")
  @Public(false)
  readAll(@Query() query: ReadAllInboxDto, @Req() req: Record<string, any>) {
    this._validateUser(req.user, query);

    return this.inboxService.readAll(query);
  }

  private _validateUser(user: Record<string, any>, query: Record<string, any>) {
    if (user?.uid) query.user_id = user.uid;

    if (!query.user_id) {
      throw new HttpException("Please provide at least user id or valid token", HttpStatus.BAD_REQUEST);
    }
  }
}
