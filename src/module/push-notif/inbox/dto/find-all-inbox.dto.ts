import { PaginationParamDto } from "../../../../common/pagination-param.dto";
import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsOptional, ValidateIf } from "class-validator";
import { PushNotifType } from "../../../enum/push-notif-sender.enum";
import { TransformBoolean } from "../../../../decorator/transform-boolean.decorator";
import { ValidDate } from "../../../../decorator/date-validator.dto";

export class FindAllInboxDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  @IsOptional()
  user_id: string;

  @ApiProperty({ required: false, enum: PushNotifType })
  @ValidateIf((obj) => obj.type)
  @IsEnum(PushNotifType)
  type: PushNotifType;

  @ApiProperty({ required: false, type: <PERSON>olean })
  @TransformBoolean()
  isRead: string;

  @ApiProperty({ required: false, description: 'fill with "YYYY-MM-DD" format' })
  @ValidateIf((obj) => obj.start_date)
  @ValidDate()
  start_date: string;

  @ApiProperty({ required: false, description: 'fill with "YYYY-MM-DD" format' })
  @ValidateIf((obj) => obj.end_date)
  @ValidDate()
  end_date: string;
}
