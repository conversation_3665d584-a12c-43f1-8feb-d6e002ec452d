import { Injectable } from "@nestjs/common";
import { FindAllInboxDto } from "./dto/find-all-inbox.dto";
import { ElasticSearchService } from "../../elastic-search/elastic-search.service";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import { FcmUserMapper, FcmUserMapperDocument } from "../fcm-user-mapper/schema/fcm-user-mapper.schema";
import { Model } from "mongoose";
import { PaginateFormat } from "../../../utils/function.util";
import { ReadAllInboxDto } from "./dto/read-all-inbox.dto";
import * as dayjs from "dayjs";
import * as utc from "dayjs/plugin/utc";
import * as timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export class InboxService {
  constructor(
    @InjectModel(FcmUserMapper.name)
    private readonly fcmMapperModel: Model<FcmUserMapperDocument>,
    private readonly elasticService: ElasticSearchService,
    private readonly configService: ConfigService,
  ) {}

  private readonly _index = this.configService.get<string>("FCM_INBOX");

  async findAll(param: FindAllInboxDto) {
    const { page = 1, type, isRead, limit = 10, start_date, end_date } = param;
    let { sort } = param;
    const userTz = dayjs.tz.guess();

    const user = await this.fcmMapperModel.findOne({ userId: param.user_id }).lean();

    if (!user) {
      return PaginateFormat({
        page: page,
        offset: 0,
        total: 0,
        data: [],
        limit: limit,
      });
    }

    const query: Record<string, any> = { bool: { must: { fcmMapId: { term: { fcmMapId: user._id } } } } };
    const timestamp: Record<string, any> = {};
    const from = (page - 1) * limit;

    if (["true", "false"].includes(isRead)) query.bool.must["isRead"] = { term: { isRead } };
    if (type) query.bool.must[type] = { term: { type } };
    if (start_date) {
      timestamp["gte"] = dayjs(start_date, "YYYY-MM-DD").tz(userTz).format("YYYY-MM-DDTHH:mm:ss");
    }
    if (end_date) {
      timestamp["lte"] = dayjs(end_date, "YYYY-MM-DD").tz(userTz).format("YYYY-MM-DDTHH:mm:ss");
    }

    if (Object.keys(timestamp).length) query.bool.filter = { range: { timestamp } };

    query.bool.must = Object.values(query.bool.must);
    if (!sort) {
      sort = "-timestamp";
    }

    return this.elasticService.find({
      index: this._index,
      _source: ["id", "type", "title", "isRead", "timestamp", "body", "image", "target_url"],
      size: Number(limit),
      sort: sort ? this._formatSort(sort.split(";")) : [],
      paginateResponse: true,
      query,
      from,
    });
  }

  async findOne(id: string) {
    const doc = await this.elasticService.findOne({ index: this._index, id });

    if (doc?.id) {
      await this.elasticService.updateById({ index: this._index, data: { isRead: true }, id });
    }

    return doc;
  }

  async readAll(params: ReadAllInboxDto) {
    const user = await this.fcmMapperModel.findOne({ userId: params.user_id }).lean();

    if (!user) return "There's no inbox can be read";

    await this.elasticService.update({
      index: this._index,
      body: { isRead: true },
      query: { match: { fcmMapId: user._id } },
    });

    return "Successfully read all inbox";
  }

  async readOne(id) {
    await this.elasticService.updateById({ index: this._index, data: { isRead: true }, id });

    return "Successfully read inbox";
  }

  private _formatSort(sorts: Array<string>): Record<string, any> {
    const res = {};

    sorts.map((sort) => {
      let replaced = sort[0] === "-" ? sort.slice(1) : sort;

      if (!["isRead", "timestamp"].includes(replaced)) replaced += ".keyword";

      if (sort[0] === "-") res[replaced] = "desc";
      else res[replaced] = "asc";
    });

    return res;
  }
}
