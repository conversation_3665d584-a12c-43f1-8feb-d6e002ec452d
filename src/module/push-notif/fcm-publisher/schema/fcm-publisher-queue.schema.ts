import { <PERSON><PERSON>, <PERSON>hem<PERSON>, <PERSON>hemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

@Schema({ timestamps: true })
export class FcmPublisherQueue {
  @Prop()
  fcm_token: string;

  @Prop()
  user_id: string;

  @Prop({ index: true })
  topic: string;

  @Prop()
  value: Array<string>;

  @Prop()
  type: string;

  @Prop()
  subscribeToFcm: boolean;

  @Prop()
  replaceValue: boolean;
}

export type FcmPublisherQueueDocument = FcmPublisherQueue & Document;

export const FcmPublisherQueueSchema = SchemaFactory.createForClass(FcmPublisherQueue);
