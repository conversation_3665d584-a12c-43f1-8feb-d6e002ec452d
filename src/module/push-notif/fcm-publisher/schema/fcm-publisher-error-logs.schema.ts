import { <PERSON><PERSON>, <PERSON>hem<PERSON>, <PERSON>hemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

@Schema({ timestamps: true })
export class FcmPublisherErrorLogs {
  @Prop({ index: true })
  fcm_token: string;

  @Prop()
  user_id: string;

  @Prop()
  topic: string;

  @Prop()
  errors: string;

  @Prop()
  type: string;
}

export type FcmPublisherErrorLogsDocument = FcmPublisherErrorLogs & Document;

export const FcmPublisherErrorLogsSchema = SchemaFactory.createForClass(FcmPublisherErrorLogs);
