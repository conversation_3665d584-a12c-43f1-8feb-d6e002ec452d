import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";

@Schema({ timestamps: true })
export class FcmPublisher {
  @Prop({ unique: true })
  fcm_token: string;

  @Prop({ type: SchemaTypes.Mixed })
  topics: Record<string, Array<string>>;
}

export type FcmPublisherDocument = FcmPublisher & Document;

export const FcmPublisherSchema = SchemaFactory.createForClass(FcmPublisher);
