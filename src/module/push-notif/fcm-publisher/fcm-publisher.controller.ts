import { Controller, Get } from "@nestjs/common";
import { FcmPublisherService } from "./fcm-publisher.service";
import { MessagePattern, Payload, Transport } from "@nestjs/microservices";
import { Controllers, FcmPublisherTopic } from "../../enum";
import {
  FcmCardNumberDto,
  FcmCartDto,
  FcmDobDto,
  FcmGenderDto,
  FcmGeneralDto,
  FcmLastPurchaseCityDto,
  FcmLastPurchaseDateDto,
  FcmLastPurchaseRegionDto,
  FcmMemberTierDto,
  FcmUnsubscribeAllDto,
} from "./dto";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { InternalAccess, Resource, RoleMatchingMode, Roles } from "keycloak-connect-tbs";

@ApiTags("FCM Token Subscriber")
@Controller("fcm-publisher")
@ApiBearerAuth("access-token")
@Resource(Controllers.FCM_PUBLISHER)
export class FcmPublisherController {
  constructor(private readonly service: FcmPublisherService) {}

  @MessagePattern(FcmPublisherTopic.ALL, Transport.KAFKA)
  async all(@Payload() payload: FcmGeneralDto) {
    return this.service.all(payload);
  }

  @MessagePattern(FcmPublisherTopic.CARD_NUMBER, Transport.KAFKA)
  async cardNumber(@Payload() payload: FcmCardNumberDto) {
    return this.service.cardNumber(payload);
  }

  @MessagePattern(FcmPublisherTopic.MEMBER_TIER, Transport.KAFKA)
  async memberTier(@Payload() payload: FcmMemberTierDto) {
    return this.service.memberTier(payload);
  }

  @MessagePattern(FcmPublisherTopic.DOB, Transport.KAFKA)
  async dob(@Payload() payload: FcmDobDto) {
    return this.service.dob(payload);
  }

  @MessagePattern(FcmPublisherTopic.GENDER, Transport.KAFKA)
  async gender(@Payload() payload: FcmGenderDto) {
    return this.service.gender(payload);
  }

  @MessagePattern(FcmPublisherTopic.UNSUBSCRIBE_ALL, Transport.KAFKA)
  async unsubscribeAll(@Payload() payload: FcmUnsubscribeAllDto) {
    return this.service.unsubscribeAll(payload);
  }

  @MessagePattern(FcmPublisherTopic.ADD_CART, Transport.KAFKA)
  async addCart(@Payload() payload: FcmCartDto) {
    return this.service.addCart(payload);
  }

  @MessagePattern(FcmPublisherTopic.REMOVE_CART, Transport.KAFKA)
  async removeCart(@Payload() payload: FcmCartDto) {
    return this.service.removeCart(payload);
  }

  @MessagePattern(FcmPublisherTopic.REMOVE_ALL_CART, Transport.KAFKA)
  async cleanUpCart(@Payload() payload: FcmGeneralDto) {
    return this.service.cleanUpCart(payload.user_id);
  }

  @MessagePattern(FcmPublisherTopic.ADD_WISHLIST, Transport.KAFKA)
  async addWishlist(@Payload() payload: FcmCartDto) {
    return this.service.addWishlist(payload);
  }

  @MessagePattern(FcmPublisherTopic.REMOVE_WISHLIST, Transport.KAFKA)
  async removeWishlist(@Payload() payload: FcmCartDto) {
    return this.service.removeWishlist(payload);
  }

  @MessagePattern(FcmPublisherTopic.LAST_PURCHASE_CITY, Transport.KAFKA)
  async lastPurchaseCity(@Payload() payload: FcmLastPurchaseCityDto) {
    return this.service.lastPurchaseCity(payload);
  }

  @MessagePattern(FcmPublisherTopic.LAST_PURCHASE_REGION, Transport.KAFKA)
  async lastPurchaseRegion(@Payload() payload: FcmLastPurchaseRegionDto) {
    return this.service.lastPurchaseRegion(payload);
  }

  @MessagePattern(FcmPublisherTopic.LAST_PURCHASE_DATE, Transport.KAFKA)
  async lastPurchaseDate(@Payload() payload: FcmLastPurchaseDateDto) {
    return this.service.lastPurchaseDate(payload);
  }

  @Get("resync-fcm-topic-subscription")
  @Roles({ roles: ["admin", "realm:app-admin"], mode: RoleMatchingMode.ANY })
  @InternalAccess()
  async reSyncSubscription() {
    return this.service.reSyncSubscription();
  }
}
