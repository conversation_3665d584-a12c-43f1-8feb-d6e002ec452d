import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { FcmPublisher, FcmPublisherDocument } from "./schema/fcm-publisher.schema";
import { Model } from "mongoose";
import {
  FcmCardNumberDto,
  FcmCartDto,
  FcmDobDto,
  FcmGenderDto,
  FcmGeneralDto,
  FcmLastPurchaseCityDto,
  FcmLastPurchaseDateDto,
  FcmLastPurchaseRegionDto,
  FcmMemberTierDto,
  FcmUnsubscribeAllDto,
} from "./dto";
import {
  FcmPublisherMongoTopic,
  FcmPublisherMongoTopicReverse,
  FcmPublisherTopic,
  FcmPublisherType,
  TopicSendMapping,
} from "../../enum";
import { FirebaseAdmin } from "../../../utils/firebase.util";
import { ElasticSearchService } from "../../elastic-search/elastic-search.service";
import { ConfigService } from "@nestjs/config";
import { v4 } from "uuid";
import { FcmUserMapper, FcmUserMapperDocument } from "../fcm-user-mapper/schema/fcm-user-mapper.schema";
import { FcmPublisherErrorLogs, FcmPublisherErrorLogsDocument } from "./schema/fcm-publisher-error-logs.schema";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { FcmPublisherEvent } from "./listener/fcm-publisher.listener";
import { TbsSiteConfigService } from "tbs-site-config";
import { SiteConfig, SiteConfigDocument } from "../../site-configs/schema/site-config.schema";
import { FcmPublisherQueueService } from "./fcm-publisher-queue.service";

interface IUnsubscribe {
  exist: Record<string, any>;
  topic: FcmPublisherType;
  fcm_token: string;
  user_id: string;
  prefix: string;
  value: Array<string> | Array<Array<string>>;
  invalidFcms: Array<string>;
  updateDb?: boolean;
}

interface ISubscribe {
  exist: Record<string, any>;
  topic: string;
  fcm_token: string;
  user_id: string;
  prefix: string;
  value: Array<string> | Array<Array<string>>;
  replaceValue: boolean;
  invalidFcms: Array<string>;
  subscribeToFcm: boolean;
}

@Injectable()
export class FcmPublisherService {
  constructor(
    @InjectModel(FcmPublisher.name)
    private readonly fcmPublisher: Model<FcmPublisherDocument>,
    @InjectModel(FcmUserMapper.name)
    private readonly fcmMapper: Model<FcmUserMapperDocument>,
    @InjectModel(FcmPublisherErrorLogs.name)
    private readonly fcmErrorLogs: Model<FcmPublisherErrorLogsDocument>,
    @InjectModel(SiteConfig.name)
    private configModel: Model<SiteConfigDocument>,
    private readonly elasticService: ElasticSearchService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly siteConfigService: TbsSiteConfigService,
    private readonly queueService: FcmPublisherQueueService,
  ) {}

  private readonly fcmHistoryIndex = this.configService.get<string>("FCM_SUBSCRIPTION_HISTORY");

  private readonly fcmErrorToRemove = [
    "messaging/invalid-registration-token",
    "messaging/registration-token-not-registered",
  ];
  private readonly siteConfigKey = "util.resync-fcm-topic";

  async all(body: FcmGeneralDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, ["true"], FcmPublisherType.ALL, false);
  }

  async cardNumber(body: FcmCardNumberDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.card_number], FcmPublisherType.CARD_NUMBER, true);
  }

  async memberTier(body: FcmMemberTierDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.tier.toLowerCase()], FcmPublisherType.MEMBER_TIER, true);
  }

  async gender(body: FcmGenderDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.gender.toLowerCase()], FcmPublisherType.GENDER, true);
  }

  async dob(body: FcmDobDto) {
    const [day, month, year] = body.dob.split("-");
    const value = [];

    value.push(day + "-" + month);
    value.push(month);
    value.push(year);

    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [value], FcmPublisherType.DOB, true);
  }

  async unsubscribeAll(body: FcmUnsubscribeAllDto) {
    const fcm = await this.fcmPublisher.find({ fcm_token: body.fcmToken }).lean();

    if (!fcm?.length) return;

    const mockElastic = [];
    await Promise.all(
      fcm.map(async (tops) => {
        for (const key of Object.keys(tops.topics)) {
          const topic = FcmPublisherMongoTopicReverse[key];

          for (const item of tops.topics[key]) {
            await FirebaseAdmin.messaging().unsubscribeFromTopic(body.fcmToken, FcmPublisherTopic[topic] + "." + item);
            mockElastic.push({
              id: v4(),
              type: "unsubscribe",
              user_id: body.user_id,
              fcm_token: body.fcmToken,
              topic,
              value: item,
              timestamp: new Date().toISOString(),
            });
          }
        }
      }),
    );

    await this.fcmPublisher.deleteMany({ fcm_token: body.fcmToken });
    await this.fcmMapper.updateMany({ fcm_tokens: body.fcmToken }, { $pull: { fcm_tokens: body.fcmToken } });
    await this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: mockElastic });
  }

  async addCart(body: FcmCartDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.sku], FcmPublisherType.ADD_CART, false);
  }

  async removeCart(body: FcmCartDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.sku], FcmPublisherType.REMOVE_CART, false);
  }

  async cleanUpCart(user_id: string) {
    const fcmMap = await this._getFcmTokens(user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, "all", FcmPublisherType.REMOVE_ALL_CART, false);
  }

  async addWishlist(body: FcmCartDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.sku], FcmPublisherType.ADD_WISHLIST, false);
  }

  async removeWishlist(body: FcmCartDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.sku], FcmPublisherType.REMOVE_WISHLIST, false);
  }

  async lastPurchaseDate(body: FcmLastPurchaseDateDto) {
    const [year, month, day] = body.date.split("-");
    const value = [];

    value.push(month + "-" + day);
    value.push(month);
    value.push(year);

    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, value, FcmPublisherType.LAST_PURCHASE_DATE, true);
  }

  async lastPurchaseCity(body: FcmLastPurchaseCityDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(fcmMap, [body.city.toLowerCase()], FcmPublisherType.LAST_PURCHASE_CITY, true);
  }

  async lastPurchaseRegion(body: FcmLastPurchaseRegionDto) {
    const fcmMap = await this._getFcmTokens(body.user_id);

    if (!fcmMap?._id) return;

    return this._doSubscribeUnSubscribe(
      fcmMap,
      [body.region.toLowerCase()],
      FcmPublisherType.LAST_PURCHASE_REGION,
      true,
    );
  }

  async reSyncSubscription() {
    const status = await this.siteConfigService.get(this.siteConfigKey);
    if (status && JSON.parse(status)?.value === "active") {
      throw new HttpException("Process already running", HttpStatus.TOO_MANY_REQUESTS);
    }

    await Promise.all([
      this.siteConfigService.updateValue(this.siteConfigKey, "active"),
      this.configModel.updateOne({ key: this.siteConfigKey }, { value: "active" }),
    ]);

    this.eventEmitter.emit(FcmPublisherEvent.re_sync_fcm_topic);

    return "Re Sync All Fcm Tokens to FCM Topics in progress";
  }

  private async _getFcmTokens(user_id: string) {
    return await this.fcmMapper.findOne({ userId: user_id }).exec();
  }

  private async _doSubscribeUnSubscribe(
    fcmMap: FcmUserMapperDocument,
    value: Array<string> | Array<Array<string>> | "all",
    type: FcmPublisherType,
    replaceLastValue: boolean,
  ) {
    const invalidFcms = [];

    await Promise.all(
      fcmMap.fcm_tokens.map(async (fcm_token) => {
        const exist = await this.fcmPublisher.findOne({ fcm_token }).lean();

        const topic = FcmPublisherTopic[type];
        const topicForMongo = FcmPublisherMongoTopic[type];

        if (!/REMOVE/.test(type)) {
          return await this._subscribe({
            exist,
            topic,
            fcm_token,
            user_id: fcmMap.userId,
            prefix: topic + ".",
            value: value === "all" ? [value] : value,
            replaceValue: replaceLastValue,
            invalidFcms,
            subscribeToFcm: TopicSendMapping[type],
          });
        } else {
          return await this._unsubscribe({
            exist: exist,
            topic: type,
            fcm_token: fcm_token,
            user_id: fcmMap.userId,
            prefix: topic + ".",
            value: value !== "all" ? value : exist?.topics?.[topicForMongo],
            invalidFcms: invalidFcms,
          });
        }
      }),
    );

    if (invalidFcms.length) {
      await this.fcmMapper.findByIdAndUpdate(fcmMap._id, { $pull: { fcm_tokens: { $in: invalidFcms } } });
    }
  }

  private async _subscribe(params: ISubscribe) {
    const { fcm_token, subscribeToFcm, user_id, replaceValue, value } = params;
    let { topic } = params;

    topic = topic.replace(process.env.KAFKA_TOPIC_PREFIX + "-", "");
    const topicForMongo = topic.replace(/.*\./g, "");

    const subscribes = [];

    const formattedTopic = topic.split(".");
    formattedTopic.splice(0, 1);

    await Promise.all(
      value.map(async (item) => {
        if (TopicSendMapping[topicForMongo.toUpperCase()]) {
          await this.queueService.addQueue({
            fcm_token,
            user_id,
            topic: topicForMongo,
            value: item,
            type: "subscribe",
            subscribeToFcm,
            replaceValue,
          });
        } else {
          const exist = await this.fcmPublisher.findOne({ fcm_token }).lean();
          await this.queueService.insertToMongo(
            "subscribe",
            exist,
            {
              topic: topicForMongo,
              value: item,
              fcm_token,
            },
            replaceValue,
          );
        }
      }),
    );

    // if (exist) {
    //   const lastValue = exist.topics[topicForMongo];
    //
    //   if (lastValue && lastValue.length && replaceValue) {
    //     await Promise.all(
    //       lastValue.map(async (item) => {
    //         if (subscribeToFcm) {
    //           await this.queueService.addQueue({
    //             fcm_token,
    //             user_id,
    //             topic: topicForMongo,
    //             value: item,
    //             type: "unsubscribe",
    //             subscribeToFcm,
    //           });
    //         }
    //       }),
    //     );
    //   }
    // }

    return subscribes;
  }

  private async _unsubscribe(params: IUnsubscribe) {
    const { invalidFcms, fcm_token, exist, user_id, prefix, value, topic, updateDb = true } = params;

    const topicForMongo = FcmPublisherMongoTopic[topic];
    if (exist) {
      const lastValue = exist.topics[topicForMongo];
      const pullValue = [];

      if (lastValue) {
        const topicsToUnsubscribe = [];
        lastValue.map((item, i) => {
          if (value.includes(item)) topicsToUnsubscribe.push({ data: item, index: i });
        });
        let errorSubscribe = [];
        const bulkElastic = [];
        await Promise.all(
          topicsToUnsubscribe.map(async (item, i) => {
            const unsubscribe = await FirebaseAdmin.messaging().unsubscribeFromTopic(
              fcm_token,
              prefix + (item?.data || item),
            );

            if (unsubscribe.errors.length && this.fcmErrorToRemove.includes(unsubscribe.errors[0].error?.code)) {
              invalidFcms.push(fcm_token);
            }

            if (unsubscribe.failureCount > 0) {
              errorSubscribe = errorSubscribe.concat(unsubscribe.errors.map((err) => err.error.message));
            } else {
              bulkElastic.push({
                id: v4(),
                type: "unsubscribe",
                user_id,
                fcm_token,
                topic,
                value: item,
                timestamp: new Date().toISOString(),
              });
            }
            await FirebaseAdmin.messaging().unsubscribeFromTopic(fcm_token, prefix + item.data);

            pullValue.push(item.data);

            exist.topics[topicForMongo].splice(item.index, 1 - i);
            bulkElastic.push({
              id: v4(),
              type: "unsubscribe",
              user_id,
              fcm_token,
              topic,
              value: item,
              timestamp: new Date().toISOString(),
            });
          }),
        );

        if (pullValue.length && updateDb) {
          await this.fcmPublisher.updateOne(
            { _id: exist._id },
            { $pull: { [`topics.${topicForMongo}`]: { $in: pullValue } } },
          );
        }
        if (bulkElastic.length) {
          await this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: bulkElastic });
        }
      }
    }
  }
}
