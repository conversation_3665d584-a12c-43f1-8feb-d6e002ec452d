import { RedisModule } from "@liaoliaots/nestjs-redis";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { ElasticSearchModule } from "../../elastic-search/elastic-search.module";
import { SiteConfig, SiteConfigSchema } from "../../site-configs/schema/site-config.schema";
import { FcmUserMapper, FcmUserMapperSchema } from "../fcm-user-mapper/schema/fcm-user-mapper.schema";
import { FcmPublisherQueueService } from "./fcm-publisher-queue.service";
import { FcmPublisherController } from "./fcm-publisher.controller";
import { FcmPublisherService } from "./fcm-publisher.service";
import { FcmPublisherListener } from "./listener/fcm-publisher.listener";
import { FcmPublisherErrorLogs, FcmPublisherErrorLogsSchema } from "./schema/fcm-publisher-error-logs.schema";
import { FcmPublisherQueue, FcmPublisherQueueSchema } from "./schema/fcm-publisher-queue.schema";
import { FcmPublisher, FcmPublisherSchema } from "./schema/fcm-publisher.schema";

@Module({
  controllers: [FcmPublisherController],
  providers: [FcmPublisherService, FcmPublisherQueueService, FcmPublisherListener],
  imports: [
    MongooseModule.forFeature([
      {
        name: FcmPublisher.name,
        schema: FcmPublisherSchema,
      },
      {
        name: FcmUserMapper.name,
        schema: FcmUserMapperSchema,
      },
      {
        name: FcmPublisherErrorLogs.name,
        schema: FcmPublisherErrorLogsSchema,
      },
      {
        name: SiteConfig.name,
        schema: SiteConfigSchema,
      },
      {
        name: FcmPublisherQueue.name,
        schema: FcmPublisherQueueSchema,
      },
    ]),
    ElasticSearchModule,
    ConfigModule,
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const host = configService.get<string>("REDIS_HOST");
        const port = configService.get<number>("REDIS_FCM_PORT");
        const db = configService.get<number>("REDIS_FCM_DB");
        return { config: { host, port, db } };
      },
    }),
  ],
  exports: [FcmPublisherService],
})
export class FcmPublisherModule {}
