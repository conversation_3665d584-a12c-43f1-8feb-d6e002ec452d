import { InjectRedis } from "@liaoliaots/nestjs-redis";
import { OnApplicationShutdown } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import { Interval } from "@nestjs/schedule";
import { randomUUID } from "crypto";
import Redis from "ioredis";
import { Model } from "mongoose";
import { TbsSiteConfigService } from "tbs-site-config";
import { FirebaseAdmin } from "../../../utils/firebase.util";
import { ElasticSearchService } from "../../elastic-search/elastic-search.service";
import { FcmPublisherMongoTopic, FcmPublisherMongoTopicReverse, FcmPublisherTopic, TopicSendMapping } from "../../enum";
import {
  availableQueueKey,
  lockKey,
  processing<PERSON>ey,
  queue<PERSON>ist<PERSON><PERSON>,
  workerList<PERSON>ey,
} from "../../enum/fcm-publisher-queue.enum";
import { FcmPublisherErrorLogs, FcmPublisherErrorLogsDocument } from "./schema/fcm-publisher-error-logs.schema";
import { FcmPublisherQueue, FcmPublisherQueueDocument } from "./schema/fcm-publisher-queue.schema";
import { FcmPublisher, FcmPublisherDocument } from "./schema/fcm-publisher.schema";

interface IAddQueue {
  fcm_token: string;
  user_id: string;
  topic: string;
  value: string | Array<string>;
  type: "subscribe" | "unsubscribe";
  subscribeToFcm: boolean;
  replaceValue: boolean;
}

export class FcmPublisherQueueService implements OnApplicationShutdown {
  constructor(
    @InjectModel(FcmPublisherQueue.name)
    private readonly queueModel: Model<FcmPublisherQueueDocument>,
    @InjectModel(FcmPublisherErrorLogs.name)
    private readonly fcmErrorLogs: Model<FcmPublisherErrorLogsDocument>,
    @InjectModel(FcmPublisher.name)
    private readonly fcmPublisher: Model<FcmPublisherDocument>,
    @InjectRedis()
    private readonly redisService: Redis,
    private readonly configService: ConfigService,
    private readonly siteConfigService: TbsSiteConfigService,
    private readonly elasticService: ElasticSearchService,
  ) {}

  private readonly fcmHistoryIndex = this.configService.get<string>("FCM_SUBSCRIPTION_HISTORY");
  private readonly fcmErrorToRemove = [
    "messaging/invalid-registration-token",
    "messaging/registration-token-not-registered",
  ];
  // private processedKey = "";
  // private lockKey = "";
  private processing = false;

  async onApplicationShutdown() {
    // if (this.processedKey) {
    //   try {
    //     const script = `
    //     local lockKey = KEYS[1]
    //     local batchKey = KEYS[2]
    //     local processedKey = ARGV[1]
    //     local queueKey = redis.call('GET', lockKey)
    //     redis.call('LREM', batchKey, 1, processedKey)
    //     redis.call('DEL', lockKey)
    //     redis.call('LPUSH', queueListKey, queueKey)
    //   `;
    //     await this.redisService.eval(script, 2, this.lockKey, workerListKey, this.processedKey);
    //   } catch (err) {
    //     console.log("Error on cleansing lock", err);
    //     process.exit(0);
    //   }
    // }
  }

  async addQueue(payload: IAddQueue) {
    let redisKey = "";
    let id = "";
    try {
      const script = `
        -- set keys from params
        local queueListKey = KEYS[1]
        local availableQueueKey = KEYS[2]
        local newKey = KEYS[3]
        
       -- set values from params
        local limit = tonumber(ARGV[1])
        local data = ARGV[2]
        
        -- get the available key of processed topic
        local availableQueue = redis.call('GET', availableQueueKey)
        local finalKey = ''
        
        -- if available topic exists or not equal to null
        if availableQueue then
          -- then push current queue to existing key
          redis.call('RPUSH', availableQueue, data)
          finalKey = availableQueue
        else
          -- else push current queue to new key
          redis.call('RPUSH', newKey, data)
          finalKey = newKey
        end
        
        -- get final key from queue list
        redis.call('LREM', queueListKey, 0, finalKey)
        redis.call('RPUSH', queueListKey, finalKey)
        
        -- get the count of final key value
        local count = redis.call('LLEN', finalKey)
        
        -- if count greater than or equal to limit
        if count >= limit then
          -- then delete the key, so next queue will create new key
          redis.call('DEL', availableQueueKey)
        else
          redis.call('SET', availableQueueKey, finalKey)
        end
        
        return {finalKey, availableQueueKey, availableQueue}
    `;

      const limit = await this._getConfig("utils.fcm-topic-subscribe-limit", 3);
      // const limit = 1;

      const typeAndTopic = payload.type + "-" + payload.topic;
      const prefix = processingKey + typeAndTopic;

      if (typeof payload.value === "string") payload.value = [payload.value];

      if (typeof payload.value === "string") payload.value = [payload.value];

      const insert = await this.queueModel.create(payload);
      id = insert._id;

      redisKey = (await this.redisService.eval(
        script,
        3,
        `${queueListKey}${typeAndTopic}`,
        `${availableQueueKey}${typeAndTopic}`,
        `${prefix}-${randomUUID()}`,
        +limit,
        id,
      )) as string;

      if (!redisKey) return "failed";
    } catch (err) {
      console.log(err);
      if (redisKey) await this.redisService.lrem(redisKey, 1, id);
    }
  }

  @Interval(FcmPublisherQueueService.name, 1000)
  async processQueue() {
    let processedKey = "";
    let lastTopicProceed = "";
    try {
      // return immediately when process already running
      if (this.processing) return;

      this.processing = true;

      await this._cleansingQueueLock();

      const scriptGetProcessedKey = `
        local queueListKey = KEYS[1]
        local lockKey = KEYS[2]
        local availableKey = KEYS[3]
        
        local queueKeyLen = redis.call('LLEN', queueListKey)
        if queueKeyLen == 0 then
          return nil
        end

        local queueKey = redis.call('LPOP', queueListKey)
        local available = redis.call('GET', availableKey)
        
        if queueKey then
          redis.call('SET', lockKey, queueKey)
          
          if available == queueKey then
            redis.call('DEL', availableKey)
          end
          
          return queueKey
        end
        
        return nil
      `;

      const rawTopic = Object.keys(TopicSendMapping)
        .filter((key) => !!TopicSendMapping[key])
        .map((key) => FcmPublisherMongoTopic[key]);

      // Generate list of topic with subscribe and unsubscribe prefix for every topic
      const topics = rawTopic.flatMap((topic) => ["unsubscribe-" + topic, "subscribe-" + topic]);

      for (const topicMongo of topics) {
        const _lockKey = lockKey + randomUUID();
        const [type] = topicMongo.split("-");
        let redisMock = "";
        // let processedKeyTemp;

        try {
          // Get the processed batch key from redis
          processedKey = (await this.redisService.eval(
            scriptGetProcessedKey,
            3,
            `${queueListKey}${topicMongo}`,
            _lockKey,
            `${availableQueueKey}${topicMongo}`,
          )) as string;

          // processedKey = processedKeyTemp[0];
          // if there's no data for this topic then continue to next topic
          if (!processedKey) continue;

          redisMock = JSON.stringify({
            processedKey: processedKey,
            lockKey: _lockKey,
            timestamp: Date.now(),
          });
        } catch (err) {
          console.log(err);
          console.log({ redisMock, processedKey });
        }

        lastTopicProceed = topicMongo;

        // Get the queue mongo id
        const idsList = await this.redisService.lrange(processedKey, 0, -1);
        if (redisMock) {
          await this.redisService.rpush(workerListKey, redisMock);
        }

        // const [idsList] = await Promise.all([
        //   this.redisService.lrange(processedKey, 0, -1),
        //   this.redisService.rpush(workerListKey, redisMock || "{}"),
        // ]);

        // // Get queue data
        const data = await this.queueModel.find({ _id: idsList }).lean();

        if (!data?.length) {
          // Delete locking data and processed batch key from redis
          await this._cleanUpLock(redisMock, _lockKey, processedKey);
          continue;
        }

        const reservedTopic = FcmPublisherMongoTopicReverse[data[0].topic];
        const topic = FcmPublisherTopic[reservedTopic] + "." + data[0].value;
        const replaceLastValue = data[0].replaceValue;
        const fcmTopics = data.map((item) => item.fcm_token);
        // Subscribe to topic
        let subscribe;

        let successData = [];
        const errorsIndex = [];
        const errorsMsg = [];
        const bulkElastic = [];

        if (data[0].subscribeToFcm) {
          if (type === "subscribe") {
            subscribe = await FirebaseAdmin.messaging().subscribeToTopic(fcmTopics, topic);
          }
          if (type === "unsubscribe") {
            subscribe = await FirebaseAdmin.messaging().unsubscribeFromTopic(fcmTopics, topic);
          }

          // Filter subscribe to FCM error data
          subscribe.errors.map((err) => {
            if (this.fcmErrorToRemove.includes(err.error.code)) {
              errorsIndex.push(err.index);
              errorsMsg.push(err.error.code);
            }
          });

          // Prepare data to send subscription logs to es
          data.map((item, i) => {
            if (!errorsIndex.includes(i)) {
              successData.push(item);
              bulkElastic.push({
                id: randomUUID(),
                type: type,
                user_id: item.user_id,
                fcm_token: item.fcm_token,
                topic: topic,
                value: item.value,
                timestamp: new Date().toISOString(),
              });
            }
          });
        } else {
          successData = data;
        }

        const unsubscribe = {};
        await Promise.all(
          successData.map(async (item) => {
            const check = await this.fcmPublisher.findOne({ fcm_token: item.fcm_token }).lean();

            if (replaceLastValue && data[0].subscribeToFcm) {
              const topic = check.topics[item.topic]?.[0];

              if (topic && topic !== data[0].value[0]) {
                unsubscribe[topic] ||= { fcm: [], topic: FcmPublisherTopic[data[0].topic.toUpperCase()] + "." + topic };
                unsubscribe[topic].fcm.push(item.fcm_token);
              }
            }

            await this.insertToMongo(type, check, item, replaceLastValue);
          }),
        );

        await Promise.all(
          Object.values(unsubscribe).map(async (unsub: Record<string, any>) => {
            await FirebaseAdmin.messaging().unsubscribeFromTopic(unsub.fcm, unsub.topic);
          }),
        );

        // Delete this queue batch data from db
        await this.queueModel.deleteMany({ _id: idsList });

        // Insert errors FCM data subscription
        const mockErrors = errorsIndex.map((dataIndex, i) => ({
          ...(data[dataIndex] || {}),
          topic,
          errors: errorsMsg[i],
        }));
        await this.fcmErrorLogs.insertMany(mockErrors);

        if (bulkElastic.length) {
          await this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: bulkElastic });
        }

        // Delete locking data and processed batch key from redis
        await this._cleanUpLock(redisMock, _lockKey, processedKey);
        // this.lockKey = "";
        // this.processedKey = "";
      }
    } catch (err) {
      if (processedKey && lastTopicProceed) {
        const key = queueListKey + lastTopicProceed;
        try {
          await this.redisService.lpush(key, processedKey);
        } catch (error) {
          console.log("processing queue FCM", error);
          throw error;
        }
      }
      console.log("processing queue FCM", err);
      throw err;
    } finally {
      this.processing = false;
    }
  }

  async insertToMongo(type: string, exist: Record<string, any>, queueData: Record<string, any>, replaceValue: boolean) {
    const value = typeof queueData.value === "string" ? [queueData.value] : queueData.value;

    if (type === "subscribe") {
      if (exist) {
        if (exist.topics?.[queueData.topic]?.length && !replaceValue) {
          const add = typeof queueData.value === "string" ? queueData.value : { $each: queueData.value };
          await this.fcmPublisher.updateOne(
            { fcm_token: queueData.fcm_token },
            { $addToSet: { ["topics." + queueData.topic]: add } },
          );
        } else {
          await this.fcmPublisher.updateOne(
            { fcm_token: queueData.fcm_token },
            { ["topics." + queueData.topic]: value },
          );
        }
      } else {
        try {
          await this.fcmPublisher.create({
            fcm_token: queueData.fcm_token,
            topics: { [queueData.topic]: value },
          });
        } catch (err) {
          if (err.code === 11000) {
            await this.fcmPublisher.updateOne(
              { fcm_token: queueData.fcm_token },
              { [`topics.${queueData.topic}`]: value },
            );
          }
        }
      }
    }

    if (type === "unsubscribe") {
      await this.fcmPublisher.updateOne(
        { fcm_token: queueData.fcm_token },
        { $pull: { ["topics." + queueData.topic]: queueData.value } },
      );
    }
  }

  private async _cleansingQueueLock() {
    const timeToLock = await this._getConfig("utils.fcm-topic-lock-time", 60);
    const processedWorker = await this.redisService.lrange(workerListKey, 0, -1);

    await Promise.all(
      processedWorker.map(async (item) => {
        const parsed = JSON.parse(item);
        const key = parsed.key || parsed.lockKey;
        if (Math.round((Date.now() - parsed.timestamp) / 1000 / 60) >= +timeToLock) {
          const script = `
            local workerListKey = KEYS[1]
            local lockKey = KEYS[2]
            local queueListKey = KEYS[3]
            local workerListVal = ARGV[1]
            
            local queueKey = redis.call('GET', lockKey)
            
            redis.call('LREM', workerListKey, 1, workerListVal)
            redis.call('DEL', lockKey)
            redis.call('LPUSH', queueListKey, queueKey)
          `;
          await this.redisService.eval(script, 3, workerListKey, key, queueListKey, item);
        }
      }),
    );
  }

  private async _cleanUpLock(workerListMock: string, lockKey, processedKey) {
    // console.log({
    //   workerListKey,
    //   workerListMock,
    //   lockKey: lockKey,
    //   prodessKEy: processedKey,
    // });

    return await Promise.all([
      this.redisService.lrem(workerListKey, 1, workerListMock),
      this.redisService.del(lockKey),
      this.redisService.del(processedKey),
    ]);
  }

  private async _getConfig(key: string, _default: any) {
    let result: any;
    try {
      const config = await this.siteConfigService.get(key);
      result = JSON.parse(config).value;
    } catch (err) {
      result = _default;
    }

    return result;
  }
}
