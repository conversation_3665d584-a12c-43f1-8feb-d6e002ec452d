import { OnEvent } from "@nestjs/event-emitter";
import { FcmPublisherMongoTopicReverse, FcmPublisherTopic, TopicSendMapping } from "../../../enum";
import { FirebaseAdmin } from "../../../../utils/firebase.util";
import { InjectModel } from "@nestjs/mongoose";
import { FcmPublisher, FcmPublisherDocument } from "../schema/fcm-publisher.schema";
import { Model, PaginateModel } from "mongoose";
import { FcmUserMapper, FcmUserMapperDocument } from "../../fcm-user-mapper/schema/fcm-user-mapper.schema";
import { ElasticSearchService } from "../../../elastic-search/elastic-search.service";
import { ConfigService } from "@nestjs/config";
import { SearchHitsMetadata } from "@elastic/elasticsearch/lib/api/types";
import { randomUUID } from "crypto";
import { OnApplicationShutdown } from "@nestjs/common";
import { TbsSiteConfigService } from "tbs-site-config";
import { SiteConfig, SiteConfigDocument } from "../../../site-configs/schema/site-config.schema";

export enum FcmPublisherEvent {
  re_sync_fcm_topic = "re_sync_fcm_topic",
}

export class FcmPublisherListener implements OnApplicationShutdown {
  constructor(
    @InjectModel(FcmPublisher.name)
    private readonly fcmPublisher: Model<FcmPublisherDocument>,
    @InjectModel(FcmUserMapper.name)
    private readonly fcmMapper: Model<FcmUserMapperDocument>,
    @InjectModel(SiteConfig.name)
    private configModel: PaginateModel<SiteConfigDocument>,
    private readonly elasticService: ElasticSearchService,
    private readonly configService: ConfigService,
    private readonly siteConfigService: TbsSiteConfigService,
  ) {}

  private readonly fcmHistoryIndex = this.configService.get<string>("FCM_SUBSCRIPTION_HISTORY");

  private readonly fcmErrorToRemove = [
    "messaging/invalid-registration-token",
    "messaging/invalid-registration-token",
    "messaging/registration-token-not-registered",
  ];

  private processSync = false;
  private readonly siteConfigKey = "util.resync-fcm-topic";

  async onApplicationShutdown() {
    await this._releaseLock();
  }

  @OnEvent(FcmPublisherEvent.re_sync_fcm_topic)
  async reSyncFcmTopic() {
    this.processSync = true;
    console.time("Execution Time Re sync FCM Topic");
    const count = await this.fcmPublisher.count();
    const limit = 1000;
    const batch = Math.ceil(count / limit);

    let i = -1;
    while (++i < batch) {
      console.time(`Re sync FCM Topic Subscription ${i + 1}/${batch}`);
      const data = await this.fcmPublisher
        .find()
        .skip(i * limit)
        .limit(limit)
        .sort("_id");

      const mapTopic = new Map();

      await Promise.all(
        data.map((item) => {
          const topics = Object.keys(item.topics);

          for (const topic of topics) {
            const reverse = FcmPublisherMongoTopicReverse[topic];
            const prefix = FcmPublisherTopic[reverse];

            if (TopicSendMapping[reverse]) {
              for (const val of item.topics[topic]) {
                const key = prefix + "." + val;
                if (!mapTopic.has(key)) mapTopic.set(key, []);

                mapTopic.set(key, [...mapTopic.get(key), item.fcm_token]);
              }
            }
          }
        }),
      );

      for (const [key, val] of mapTopic.entries()) {
        const fcmMapper = await this.fcmMapper.find({ fcm_tokens: { $in: val } });

        const hashingFcmMapper = {};
        for (const mapper of fcmMapper) {
          mapper.fcm_tokens.map((token) => (hashingFcmMapper[token] = mapper.userId));
        }

        const subscribe = await FirebaseAdmin.messaging().subscribeToTopic(val, key);

        const errorsIndex = [];
        if (subscribe.errors.length && this.fcmErrorToRemove.includes(subscribe.errors[0].error?.code)) {
          subscribe.errors.map((err) => {
            if (this.fcmErrorToRemove.includes(err.error.code)) errorsIndex.push(err.index);
          });
        }

        const bulkElastic = [];
        await Promise.all(
          val.map(async (fcm_token, i) => {
            const userId = hashingFcmMapper[fcm_token];
            if (!errorsIndex.includes(i) && userId) {
              await this._checkAndSetEs(userId, fcm_token, key, "subscribe", bulkElastic);
            }
          }),
        );

        if (errorsIndex.length) {
          await Promise.all(
            errorsIndex.map(async (i) => {
              const userMap = hashingFcmMapper[val[i]];

              if (userMap) {
                await this._checkAndSetEs(userMap, val[i], key, "unsubscribe", bulkElastic);
                await this.fcmMapper.updateOne({ userId: userMap }, { $pull: { fcm_tokens: val[i] } });
                await this.fcmPublisher.deleteOne({ fcm_token: val[i] });
              }
            }),
          );
        }

        if (bulkElastic.length) {
          await this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: bulkElastic });
        }
      }
      console.timeEnd(`Re sync FCM Topic Subscription ${i + 1}/${batch}`);
    }
    console.timeEnd("Execution Time Re sync FCM Topic");
    await Promise.all([
      this.siteConfigService.updateValue(this.siteConfigKey, "inactive"),
      this.configModel.updateOne({ key: this.siteConfigKey }, { value: "inactive" }),
    ]);
    this.processSync = false;
  }

  private async _releaseLock() {
    await Promise.all([this.siteConfigService.updateValue(this.siteConfigKey, "inactive")]);
    this.processSync = false;
  }

  private async _checkAndSetEs(
    userId: string,
    fcm_token: string,
    key: string,
    type: "subscribe" | "unsubscribe",
    arrRes: Array<Record<string, any>>,
  ) {
    const check = (await this.elasticService.find({
      query: {
        bool: {
          must: [
            { match: { user_id: userId } },
            { match: { fcm_token: fcm_token } },
            { match: { type: type } },
            { match: { topic: key } },
            { match: { value: key.split(".")[1] } },
          ],
        },
      },
    })) as SearchHitsMetadata<any>;

    if (!check?.hits?.length) {
      arrRes.push({
        id: randomUUID(),
        type: type,
        user_id: userId,
        fcm_token: fcm_token,
        topic: key,
        value: key.split(".")[1],
        timestamp: new Date().toISOString(),
        subscribed_on_fcm: true,
      });
    }
  }
}
