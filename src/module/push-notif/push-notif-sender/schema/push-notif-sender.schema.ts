import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { CreatedFrom, PushNotifTransport, PushNotifType } from "../../../enum/push-notif-sender.enum";
import { SchemaTypes } from "mongoose";

@Schema({ timestamps: true })
export class PushNotifSender {
  @Prop()
  title: string;

  @Prop()
  body: string;

  @Prop()
  image: string;

  @Prop()
  target_url: string;

  @Prop({ enum: PushNotifType })
  type: PushNotifType;

  @Prop({ enum: PushNotifTransport })
  transport: PushNotifTransport;

  @Prop({ type: SchemaTypes.Mixed })
  topic: Record<string, any>;

  @Prop({ index: true, enum: CreatedFrom })
  createdFrom: CreatedFrom;

  @Prop({ default: false, index: true })
  isSent: boolean;

  @Prop()
  err_message: string;
}

export type PushNotifSenderDocument = PushNotifSender & Document;

export const PushNotifSenderSchema = SchemaFactory.createForClass(PushNotifSender);

PushNotifSenderSchema.plugin(mongoosePaginate);
