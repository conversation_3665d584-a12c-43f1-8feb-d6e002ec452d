import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { PushNotifSender, PushNotifSenderDocument } from "./schema/push-notif-sender.schema";
import { Model, PaginateModel } from "mongoose";
import { SenderCreateTopicDto } from "./dto";
import { ConfigService } from "@nestjs/config";
import { PushNotifTransport, PushNotifType } from "../../enum/push-notif-sender.enum";
import { FirebaseAdmin } from "../../../utils/firebase.util";
import { FcmPublisherMongoTopic, FcmPublisherTopic, TopicSendMapping } from "../../enum";
import { PushNotifSenderLog, PushNotifSenderLogDocument } from "./schema/push-notif-sender-log.schema";
import { FcmPublisher, FcmPublisherDocument } from "../fcm-publisher/schema/fcm-publisher.schema";
import { ElasticSearchService } from "../../elastic-search/elastic-search.service";
import { v4 } from "uuid";
import { FcmUserMapper, FcmUserMapperDocument } from "../fcm-user-mapper/schema/fcm-user-mapper.schema";
import { SenderCreateApiDto } from "./dto/sender-create-api.dto";
import * as dayjs from "dayjs";
import { TbsSiteConfigService } from "tbs-site-config";

@Injectable()
export class PushNotifSenderService {
  constructor(
    @InjectModel(PushNotifSender.name) private readonly pushNotifSenderModel: PaginateModel<PushNotifSenderDocument>,
    @InjectModel(PushNotifSenderLog.name) private readonly logModel: Model<PushNotifSenderLogDocument>,
    @InjectModel(FcmPublisher.name) private readonly fcmPublisherModel: Model<FcmPublisherDocument>,
    @InjectModel(FcmUserMapper.name) private readonly fcmUserMapperModel: Model<FcmUserMapperDocument>,
    private readonly configService: ConfigService,
    private readonly elasticService: ElasticSearchService,
    private readonly siteConfigService: TbsSiteConfigService,
  ) {}

  private readonly fcmInboxIndex = this.configService.get<string>("FCM_INBOX");

  async createApi(mock: SenderCreateApiDto) {
    mock["transport"] = PushNotifTransport.TOPIC;

    if (!mock.send_date) {
      return await this.create({
        body: mock.body,
        image: mock.image,
        target_url: mock.target_url,
        type: mock.type,
        title: mock.title,
        topic: mock.topic as Array<Record<string, any>>,
        transport: mock["transport"],
        device_id: [],
        isSent: true,
      });
      // mock.send_date = dayjs().add(10, "second").toISOString();
      // mock["isSent"] = true;
    }

    return this.pushNotifSenderModel.create(mock);
  }

  async sendPushNotification() {
    const count = await this.pushNotifSenderModel.count({ isSent: false });
    let config = await this.siteConfigService.get("pn.max_parallel_send");

    if (config) {
      config = JSON.parse(config).value;
    } else {
      config = "10";
    }

    const limit = +config;
    const batchTotal = Math.ceil(count / limit);

    let i = -1;
    while (++i < batchTotal) {
      const data = await this.pushNotifSenderModel.find(
        { isSent: false },
        {},
        { sort: "_id", skip: i * limit, lean: true, limit },
      );

      await Promise.all(
        data.map(async (item: Record<string, any>) => {
          await this.pushNotifSenderModel.updateOne({ _id: item._id }, { isSent: true });
          await this.create(item as SenderCreateTopicDto);
        }),
      );
    }

    return "successfully send all pending";
  }

  async create(mock: SenderCreateTopicDto) {
    let data;

    if (!mock?._id) {
      data = await this.pushNotifSenderModel.create(mock);
    } else {
      data = await this.pushNotifSenderModel.findById(mock._id);
    }

    let validate;

    if (mock.transport === PushNotifTransport.TOPIC) {
      validate = await this._validateCreateTopic(mock);
    } else if (mock.transport === PushNotifTransport.DEVICE_ID) {
      validate = await this._validateCreateSender(mock);
    }

    if (!validate.status) {
      data.err_message = validate.error;
    } else {
      if (mock.transport === PushNotifTransport.TOPIC) {
        await this._sendByTopic(
          mock.topic,
          mock.type,
          mock.title,
          mock.body,
          mock.image,
          mock.target_url || "",
          data._id,
        );
      } else if (mock.transport === PushNotifTransport.DEVICE_ID) {
        await this._sendBySender(mock.device_id, mock.title, mock.body, mock.image, mock.target_url || "");
      }
    }

    // await data.save();
    // const record = await data.save();

    // if (validate.status) {
    //   if (mock.transport === PushNotifTransport.TOPIC) {
    //     await this._saveInboxByTopic(mock.topic);
    //   }
    // }

    return data;
  }

  private async _saveInboxByTopic(topics: Array<Record<string, any>>) {
    const filter = { topics: {} };

    topics.map((topic) => (filter.topics[FcmPublisherTopic[topic.type]] = topic.value));

    await this.fcmPublisherModel.find(filter);
  }

  private async _validateGeneral(mock: SenderCreateTopicDto) {
    const imageBaseUrls = [
      process.env.IMAGE_BASE_URL,
      process.env.IMAGE_BASE_URL_POS,
      process.env.AWS_S3_BASE_URL,
      ...process.env.CUSTOM_IMAGE_URL.split(","),
    ];
    const error = [];

    if (!mock.body) error.push("Body is required");
    if (mock.image && !imageBaseUrls.includes(mock.image)) {
      let allowed = false;
      for (const baseUrl of imageBaseUrls) {
        if (new RegExp(`^${baseUrl}`, "g").test(mock.image)) {
          allowed = true;
          break;
        }
      }

      if (!allowed) error.push("Image url is not allowed");
    }

    if (error.length) return error;

    return error;
  }

  private async _validateCreateTopic(mock: SenderCreateTopicDto) {
    const maxTopicCombination = this.configService.get<number>("MAX_TOPIC_COMBINATION");

    const error = await this._validateGeneral(mock);

    if (!mock.topic) error.push("Topic is required");
    if (mock.topic && Object.keys(mock.topic).length > maxTopicCombination) {
      error.push(`Topic max ${maxTopicCombination} combinations`);
    }
    if (mock.topic) {
      mock.topic.map((topic) => {
        if (!FcmPublisherTopic[topic.type]) error.push(`Topic type ${topic.type} not found`);
      });
    }

    if (error.length) return { status: false, error: error.join(", ") };

    return { status: true, error: "" };
  }

  private async _validateCreateSender(mock: SenderCreateTopicDto) {
    const error = await this._validateGeneral(mock);

    if (!mock.device_id.length) error.push("Sender ID is required");

    if (error.length) return { status: false, error: error.join(", ") };

    return { status: true, error: "" };
  }

  private async _sendByTopic(
    topics: Array<Record<string, any>>,
    type: PushNotifType,
    title: string,
    body: string,
    image: string,
    target_url: string,
    notifLogId: string,
  ) {
    // const condition = [];
    const filter: Record<string, any> = {};

    // topics.map((topic) => {
    //   topic.value = topic.value.toString();
    //   const fcmTopic = FcmPublisherTopic[topic.type] + '.';
    //   condition.push(`'${fcmTopic + topic.value.toLowerCase()}' in topics`);
    //
    //   const prefix = FcmPublisherTopic[topic.type];
    //   filter["topics." + prefix.replace(/.*\./g, "")] = topic.value.toLowerCase();
    // });

    const data = await this._getTargetData(topics, filter);

    const notifMock: Record<string, any> = { body, title };

    if (image) notifMock.imageUrl = image;

    if (typeof data === "string") {
      return this._sendToFcmByTopic(data, notifMock, target_url, type, title, body, image, filter, notifLogId);
    } else {
      // check kalo di topics,
      if (!data?.length) {
        await this.pushNotifSenderModel.findByIdAndUpdate(notifLogId, { $set: { isSent: false } });
      }
      return this._sendToFcmByToken(data, notifMock, target_url, type, title, body, image, filter, notifLogId);
    }
  }

  private async _getTargetData(topics: Array<Record<string, any>>, filter: Record<string, any>) {
    const fcmCondition = [];
    const mongoFilter = {};

    topics.map((topic) => {
      topic.value = String(topic.value)
        .split(",")
        ?.map((item) => String(item).trim().toLowerCase());
      topic.value = [...new Set(topic.value || [])];
      if (TopicSendMapping[topic.type]) {
        const fcmTopic = FcmPublisherTopic[topic.type];
        for (const value of topic.value) {
          fcmCondition.push(`'${fcmTopic + value}' in topics`);
        }
      } else {
        mongoFilter["topics." + FcmPublisherMongoTopic[topic.type]] = topic.value;
      }

      const prefix = FcmPublisherTopic[topic.type];
      filter["topics." + prefix.replace(/.*\./g, "")] = topic.value.map((item) => String(item).replace(".", ""));
    });

    if (fcmCondition.length) return fcmCondition.join(" && ");
    if (Object.keys(mongoFilter).length) {
      return await this.fcmPublisherModel.find(mongoFilter).lean().exec();
    }

    return [];
  }

  private async _sendToFcmByTopic(
    condition: string,
    notifMock: Record<string, any>,
    target_url: string,
    type: PushNotifType,
    title: string,
    body: string,
    image: string,
    filter: Record<string, any>,
    notifLogId: string,
  ) {
    const fcmData = { target_url };

    if (image) fcmData["image"] = image;

    await FirebaseAdmin.messaging().send({
      condition: condition,
      notification: notifMock,
      apns: { payload: { aps: { sound: "default" } } },
      android: { notification: { sound: "default" } },
      data: fcmData,
    });
    await this._sendToElastic(filter, type, title, body, image, target_url, notifLogId);
  }

  private async _sendToFcmByToken(
    data: Array<Record<string, any>>,
    notifMock: Record<string, any>,
    target_url: string,
    type: PushNotifType,
    title: string,
    body: string,
    image: string,
    filter: Record<string, any>,
    notifLogId: string,
  ) {
    const limit = 1000;
    const batch = Math.ceil(data.length / limit);

    const fcmData = { target_url };

    if (image) fcmData["image"] = image;

    let i = -1;
    while (++i < batch) {
      const tokens = data.splice(0, limit).map((item) => item.fcm_token);

      await FirebaseAdmin.messaging().sendMulticast({
        tokens,
        notification: notifMock,
        apns: { payload: { aps: { sound: "default" } } },
        android: { notification: { sound: "default" } },
        data: fcmData,
      });
    }
    await this._sendToElastic(filter, type, title, body, image, target_url, notifLogId);
  }

  private async _sendBySender(
    device_id: Array<string>,
    title: string,
    body: string,
    image: string,
    target_url: string,
  ) {
    const batch = Math.ceil(device_id.length / 1000);

    for (let i = 0; i < batch; i++) {
      const fcm = device_id.splice(0, 1000);

      const result = await FirebaseAdmin.messaging().sendToDevice(fcm, {
        notification: { title, body },
        data: { target_url, image },
      });
      await this.logModel.create({ result });
    }
  }

  private async _sendToElastic(
    fcmFilter: Record<string, any>,
    type: PushNotifType,
    title: string,
    body: string,
    image: string,
    target_url: string,
    notifLogId: string,
  ) {
    const timestamp = new Date().toISOString();
    const total = await this.fcmPublisherModel.count(fcmFilter);
    let config = await this.siteConfigService.get("pn.populate_limit");

    if (config) {
      config = JSON.parse(config).value;
    } else {
      config = "1000";
    }

    const limit = +config;
    const totalBatch = Math.ceil(total / limit);
    let i = -1;

    while (++i < totalBatch) {
      const fcmTokens = await this.fcmPublisherModel.find(fcmFilter, "fcm_token", {
        sort: "_id",
        skip: limit * i,
        limit,
      });

      const users = await this.fcmUserMapperModel.find({
        fcm_tokens: { $in: fcmTokens.map((item) => item.fcm_token) },
      });

      const data = users.map((item) => {
        return {
          id: notifLogId + "-" + item._id,
          type,
          fcmMapId: item._id,
          title,
          body,
          image,
          target_url,
          isRead: false,
          timestamp,
        };
      });

      if (data.length) await this.elasticService.bulkInsertDocument({ index: this.fcmInboxIndex, data });
    }
  }
}
