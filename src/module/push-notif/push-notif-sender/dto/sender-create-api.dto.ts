import { ApiProperty } from "@nestjs/swagger";
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsUrl,
  ValidateIf,
  ValidateNested,
} from "class-validator";
import { IsImageUrlRegistered } from "../../../../decorator/image-url-validator";
import { Type } from "class-transformer";
import { CreatedFrom, PushNotifType } from "../../../enum/push-notif-sender.enum";
import { FcmPublisherTopic } from "../../../enum";

class TopicDto {
  @ApiProperty({ enum: Object.keys(FcmPublisherTopic) })
  @IsNotEmpty()
  @IsEnum(Object.keys(FcmPublisherTopic))
  type: string;

  @ApiProperty()
  @IsNotEmpty()
  value: string;
}

export class SenderCreateApiDto {
  @ApiProperty()
  @IsNotEmpty()
  title: string;

  @ApiProperty()
  @IsNotEmpty()
  body: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.image)
  @IsUrl()
  @IsImageUrlRegistered({
    message: "unknown image url: $value",
  })
  image: string;

  @ApiProperty()
  @IsNotEmpty()
  target_url: string;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  @Type(() => TopicDto)
  topic: Array<TopicDto>;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.send_date)
  @IsDateString()
  send_date: string;

  @ApiProperty({ required: false })
  @ValidateIf((obj) => obj.type)
  @IsEnum(Object.values(PushNotifType))
  type: PushNotifType;

  @ApiProperty({ required: false, enum: Object.keys(CreatedFrom) })
  @ValidateIf((obj) => obj.createdFrom)
  @IsEnum(Object.values(CreatedFrom))
  createdFrom: CreatedFrom;
}
