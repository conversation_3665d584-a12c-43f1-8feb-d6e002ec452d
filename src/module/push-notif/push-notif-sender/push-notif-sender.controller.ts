import { Body, Controller, Get, Post } from "@nestjs/common";
import { PushNotifSenderService } from "./push-notif-sender.service";
import { SenderCreateTopicDto } from "./dto";
import { CreatedFrom, PushNotifTopic, PushNotifTransport, PushNotifType } from "../../enum/push-notif-sender.enum";
import { MessagePattern, Payload, Transport } from "@nestjs/microservices";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { SenderCreateApiDto } from "./dto/sender-create-api.dto";
import { InternalAccess, Resource, Scopes } from "keycloak-connect-tbs";
import { <PERSON>s, Scope } from "../../enum";

@ApiTags("Push Notif Sender")
@Controller("push-sender")
@ApiBearerAuth("access-token")
@Resource(Controllers.PUSH_NOTIFICATION)
export class PushNotifSenderController {
  constructor(private readonly pushNotifSenderService: PushNotifSenderService) {}

  @MessagePattern(PushNotifTopic.PUSH_BY_TOPIC, Transport.KAFKA)
  createByTopic(@Payload() payload: SenderCreateTopicDto) {
    payload.transport = PushNotifTransport.TOPIC;
    payload.isSent = true;
    return this.pushNotifSenderService.create(payload);
  }

  @Post("by-topic")
  @InternalAccess()
  @Scopes(Scope.POST)
  pushByTopic(@Body() body: SenderCreateApiDto) {
    if (!body.createdFrom) body.createdFrom = CreatedFrom.CMS;
    if (!body.type) body.type = PushNotifType.PROMOTION;

    return this.pushNotifSenderService.createApi(body);
  }

  @InternalAccess()
  @Get("send-pending-push-notification")
  sendPendingPushNotification() {
    return this.pushNotifSenderService.sendPushNotification();
  }
}
