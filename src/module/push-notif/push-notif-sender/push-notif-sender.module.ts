import { Module } from "@nestjs/common";
import { PushNotifSenderController } from "./push-notif-sender.controller";
import { PushNotifSenderService } from "./push-notif-sender.service";
import { MongooseModule } from "@nestjs/mongoose";
import { PushNotifSender, PushNotifSenderSchema } from "./schema/push-notif-sender.schema";
import { PushNotifSenderLog, PushNotifSenderLogSchema } from "./schema/push-notif-sender-log.schema";
import { ConfigModule } from "@nestjs/config";
import { ElasticSearchModule } from "../../elastic-search/elastic-search.module";
import { FcmPublisher, FcmPublisherSchema } from "../fcm-publisher/schema/fcm-publisher.schema";
import { FcmUserMapper, FcmUserMapperSchema } from "../fcm-user-mapper/schema/fcm-user-mapper.schema";

@Module({
  controllers: [PushNotifSenderController],
  providers: [PushNotifSenderService],
  imports: [
    MongooseModule.forFeature([
      {
        name: PushNotifSender.name,
        schema: PushNotifSenderSchema,
      },
      {
        name: PushNotifSenderLog.name,
        schema: PushNotifSenderLogSchema,
      },
      {
        name: FcmPublisher.name,
        schema: FcmPublisherSchema,
      },
      {
        name: FcmUserMapper.name,
        schema: FcmUserMapperSchema,
      },
    ]),
    ConfigModule,
    ElasticSearchModule,
  ],
  exports: [PushNotifSenderService, MongooseModule, ConfigModule, ElasticSearchModule],
})
export class PushNotifSenderModule {}
