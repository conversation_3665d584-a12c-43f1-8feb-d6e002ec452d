import { IsNotEmpty, IsOptional } from "class-validator";

export class FcmUserMapperCreateDto {
  @IsNotEmpty()
  fcm_tokens: string;

  @IsNotEmpty()
  userId: string;

  @IsOptional()
  dob: string;

  @IsOptional()
  gender: string;

  @IsOptional()
  cardNumber: string;

  @IsOptional()
  tier: string;

  userIdNew: string; // filled only when user already registered

  @IsOptional()
  isNewMobileUser: boolean;
}
