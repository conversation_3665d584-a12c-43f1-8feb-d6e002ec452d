import { Controller } from "@nestjs/common";
import { FcmUserMapperService } from "./fcm-user-mapper.service";
import { MessagePattern, Payload, Transport } from "@nestjs/microservices";
import { FcmUserMapperTopic } from "../../enum/fcm-user-mapper.enum";
import { FcmUserMapperCreateDto } from "./dto/fcm-user-mapper-create.dto";

@Controller()
export class FcmUserMapperController {
  constructor(private readonly fcmUserMapperService: FcmUserMapperService) {}

  @MessagePattern(FcmUserMapperTopic.USER_LOGIN, Transport.KAFKA)
  async login(@Payload() payload: FcmUserMapperCreateDto) {
    if (!payload.userId && payload.userIdNew) {
      payload.userId = payload.userIdNew;
    }

    return this.fcmUserMapperService.createOrUpdate(payload);
  }
}
