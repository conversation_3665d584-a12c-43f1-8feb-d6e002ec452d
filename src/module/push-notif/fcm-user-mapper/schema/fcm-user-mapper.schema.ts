import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";

@Schema({ timestamps: true })
export class FcmUserMapper {
  @Prop({ unique: true })
  userId: string;

  @Prop({ type: SchemaTypes.Array })
  fcm_tokens: Array<string>;
}

export type FcmUserMapperDocument = FcmUserMapper & Document;

export const FcmUserMapperSchema = SchemaFactory.createForClass(FcmUserMapper);
