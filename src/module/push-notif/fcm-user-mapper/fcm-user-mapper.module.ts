import { Modu<PERSON> } from "@nestjs/common";
import { FcmUserMapperController } from "./fcm-user-mapper.controller";
import { FcmUserMapperService } from "./fcm-user-mapper.service";
import { MongooseModule } from "@nestjs/mongoose";
import { FcmUserMapper, FcmUserMapperSchema } from "./schema/fcm-user-mapper.schema";
import { FcmPublisherModule } from "../fcm-publisher/fcm-publisher.module";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { KafkaClientName } from "src/module/enum";
import { SiteConfigsModule } from "src/module/site-configs/site-configs.module";

@Module({
  controllers: [FcmUserMapperController],
  providers: [FcmUserMapperService],
  imports: [
    FcmPublisherModule,
    MongooseModule.forFeature([
      {
        name: FcmUserMapper.name,
        schema: FcmUserMapperSchema,
      },
    ]),
    ClientsModule.register([
      {
        name: KafkaClientName,
        transport: Transport.KAFKA,
        options: {
          producer: {
            allowAutoTopicCreation: true,
          },
          producerOnlyMode: true,
          client: {
            brokers: process.env.KAFKA_HOST.split(","),
          },
        },
      },
    ]),
  ],
})
export class FcmUserMapperModule {}
