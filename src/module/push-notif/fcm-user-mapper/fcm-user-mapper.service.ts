import { Inject, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { FcmUserMapper, FcmUserMapperDocument } from "./schema/fcm-user-mapper.schema";
import { FcmUserMapperCreateDto } from "./dto/fcm-user-mapper-create.dto";
import { Model } from "mongoose";
import { FcmPublisherService } from "../fcm-publisher/fcm-publisher.service";
import * as dayjs from "dayjs";
import { ClientKafka } from "@nestjs/microservices";
import { KafkaClientName, KafkaTopic } from "src/module/enum";

@Injectable()
export class FcmUserMapperService {
  constructor(
    @InjectModel(FcmUserMapper.name) private readonly fcmUserMapperModel: Model<FcmUserMapperDocument>,
    private readonly fcmPublisherService: FcmPublisherService,
    @Inject(KafkaClientName) private readonly kafkaClient: ClientKafka,
  ) {}

  async createOrUpdate(params: FcmUserMapperCreateDto): Promise<any> {
    const mock = { userId: params.userId, $addToSet: { fcm_tokens: params.fcm_tokens } };

    if (params.userIdNew) mock.userId = params.userIdNew;

    let register;
    try {
      register = await this.fcmUserMapperModel.updateOne({ userId: params.userId }, mock, {
        new: true,
        upsert: true,
      });
    } catch (err) {
      if (err.code === 11000 && params.userIdNew) {
        register = await this.fcmUserMapperModel.updateOne({ userId: params.userIdNew }, mock, {
          new: true,
          upsert: true,
        });
      }
    }

    if (params.gender) {
      await this.fcmPublisherService.gender({ user_id: mock.userId, gender: params.gender, type: "subscribe" });
    }
    if (params.dob) {
      await this.fcmPublisherService.dob({
        user_id: mock.userId,
        dob: dayjs(params.dob).format("DD-MM-YYYY"),
        type: "subscribe",
      });
    }
    if (params.cardNumber) {
      await this.fcmPublisherService.cardNumber({
        user_id: mock.userId,
        card_number: params.cardNumber,
        type: "subscribe",
      });
    }
    if (params.tier) {
      await this.fcmPublisherService.memberTier({
        user_id: mock.userId,
        tier: params.tier,
        type: "subscribe",
      });
    }

    await this.fcmPublisherService.all({ user_id: mock.userId, type: "subscribe" });

    if (params.isNewMobileUser) {
      const delayInMs = 10000;
      setTimeout(() => {
        this.kafkaClient.emit(KafkaTopic.USER_FIRST_LOGIN_MOBILE, { cardNumber: params.cardNumber });
      }, Number(delayInMs));
    }

    return register;
  }
}
