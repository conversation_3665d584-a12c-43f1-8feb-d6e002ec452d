import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common"; //main lib
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from "@nestjs/swagger"; //swagger required
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs"; //acl
import { Role } from "../../enum/role.enum";
import { <PERSON><PERSON>, Scope } from "../../enum/rbac.enum";
import { NotificationService } from "./notification.service";
import { FileInterceptor } from "@nestjs/platform-express";
import { CreateNotificationDto } from "./dto/create-notification.dto";
import { BypassFieldsValidation } from "global-body-validator-tbs";
import { CreateNotificationTargetDto, ImportNotificationDto } from "./dto/create-notification-target.dto";
import { UpdateNotificationTargetDto } from "./dto/update-notification-target.dto";
import { GetNotificationTargetDto } from "./dto/get-notification-target.dto";
import { GetNotificationDto } from "./dto/get-notification.dto";

@ApiTags("Admin - Push Notif") //module name
@Controller("admin/notif-management")
@ApiBearerAuth("access-token") //swagger
@Resource(Controllers.ADMIN_NOTIFICATION)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
export class AdminNotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  /**
   * create a notification
   */
  @Post("create-notification")
  @ApiConsumes("multipart/form-data")
  @ApiBody({ type: CreateNotificationDto })
  @UseInterceptors(FileInterceptor("file"))
  @Scopes(Scope.POST)
  @BypassFieldsValidation("title", "body")
  createnNotification(
    @Body() createNotificationDto: CreateNotificationDto,
    @UploadedFile("file") file: Express.Multer.File,
  ) {
    return this.notificationService.createNotification(createNotificationDto, file);
  }

  /**
   * get a notification
   * @param {id}
   */
  @Get("get-notification/:id")
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.notificationService.findNotificationById(id);
  }

  /**
   * update a notification
   */
  @Post("update-notification/:id")
  @ApiConsumes("multipart/form-data")
  @ApiBody({ type: CreateNotificationDto })
  @UseInterceptors(FileInterceptor("file"))
  @Scopes(Scope.POST)
  @BypassFieldsValidation("title", "body")
  updateNotification(
    @Param("id") id: string,
    @Body() createNotificationDto: CreateNotificationDto,
    @UploadedFile("file") file: Express.Multer.File,
  ) {
    return this.notificationService.updateNotification(id, createNotificationDto, file);
  }

  /**
   * get list of notification
   */
  @Get("get-notification")
  @Scopes(Scope.GET)
  find(@Query() pagination: GetNotificationDto) {
    return this.notificationService.findAllNotification(pagination);
  }

  /**
   * send now notification
   */
  @Get("send-now/:id")
  @Scopes(Scope.GET)
  sendNow(@Param("id") id: string) {
    return this.notificationService.sendNow(id);
  }

  /**
   * send now notification
   */
  @Get("reset/:id")
  @Scopes(Scope.GET)
  resetNotification(@Param("id") id: string) {
    return this.notificationService.resetNotification(id);
  }

  /**
   * delete a notification target
   * @param {id}
   */
  @Delete("/delete-notification/:id")
  @Scopes(Scope.DELETE)
  removeNotification(@Param("id") id: string) {
    return this.notificationService.removeNotification(id);
  }

  /**
   * create a notification target
   */
  @Post("create-target")
  @Scopes(Scope.POST)
  @BypassFieldsValidation("name")
  createnNotificationTarget(@Body() createNotificationTargetDto: CreateNotificationTargetDto) {
    return this.notificationService.createNotificationTarget(createNotificationTargetDto);
  }

  /**
   * get a notification target
   * @param {id}
   */
  @Get("get-target/:id")
  @Scopes(Scope.GET)
  findTargetOne(@Param("id") id: string) {
    return this.notificationService.findTargetById(id);
  }

  /**
   * get list of notification target
   */
  @Get("get-target")
  @Scopes(Scope.GET)
  findTarget(@Query() pagination: GetNotificationTargetDto) {
    return this.notificationService.findAllTarget(pagination);
  }

  /**
   * update a notification target
   * @param {id}
   */
  @Patch("/update-target/:id")
  @Scopes(Scope.PATCH)
  @BypassFieldsValidation("name")
  updateNotificationTarget(@Param("id") id: string, @Body() updateNotificationTargetDto: UpdateNotificationTargetDto) {
    return this.notificationService.updateTarget(id, updateNotificationTargetDto);
  }
  /**
   * delete a notification target
   * @param {id}
   */
  @Delete("/delete-target/:id")
  @Scopes(Scope.DELETE)
  removeNotificationTarget(@Param("id") id: string) {
    return this.notificationService.removeTarget(id);
  }

  /**
   * import notification target
   * @param {id}
   * @param {file}
   * @param {body}
   */
  @Post("import-target/:id")
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("file"))
  @Scopes(Scope.POST)
  importTarget(@Param("id") id: string, @UploadedFile("file") file: any, @Body() body: ImportNotificationDto) {
    body.file = file;
    return this.notificationService.importTarget(id, file, body.operation);
  }

  /**
   * download target template
   */
  @Get("download-template-target")
  @Scopes(Scope.GET)
  downloadTargetTemplate() {
    return this.notificationService.downloadTargetTemplate();
  }
}
