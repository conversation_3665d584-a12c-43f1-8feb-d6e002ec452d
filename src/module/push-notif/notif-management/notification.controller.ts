/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Get } from "@nestjs/common";
import { Public } from "keycloak-connect-tbs"; //acl
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Resource } from "keycloak-connect-tbs";
import { Controllers } from "../../enum/rbac.enum";
import { NotificationService } from "./notification.service";

@Controller("notif-management")
@ApiTags("Push Notif Management")
@Resource(Controllers.NOTIFICATION)
@ApiBearerAuth("access-token") //swagger
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  //cron
  @Get("queue")
  @Public()
  async queueNotif() {
    return this.notificationService.queueingNotif();
  }
}
