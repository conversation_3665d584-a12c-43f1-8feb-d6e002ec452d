import { ApiProperty } from "@nestjs/swagger";
import { IsBooleanString, ValidateIf, IsOptional, IsString } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";

export class GetNotificationDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ required: false, type: Boolean })
  @ValidateIf((obj) => obj.status)
  @IsOptional()
  @IsBooleanString()
  status?: boolean;

  @ApiProperty({ required: false, type: <PERSON><PERSON>an })
  @ValidateIf((obj) => obj.isSent)
  @IsOptional()
  @IsBooleanString()
  isSent?: boolean;
}
