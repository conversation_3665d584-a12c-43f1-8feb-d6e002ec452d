import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsNotEmpty, IsString } from "class-validator";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class CreateNotificationDto {
  @ApiProperty({ required: true })
  @IsString()
  title: string;

  @ApiProperty({ required: true })
  @IsString()
  body: string;

  @ApiProperty({ required: false, example: "my-account/my-voucher" })
  @IsString()
  target_url: string;

  @ApiProperty({ required: false, type: Array, example: '["650020cfcac870f5a13b9ac7","65011e92f248a5c6fcf72042"]' })
  @IsString()
  target: Record<string, any>;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  @TransformBoolean()
  status: boolean;

  @ApiProperty({ required: true, example: "2023-09-12 16:30" })
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  image: string;

  @ApiProperty({
    required: false,
    type: "string",
    format: "binary",
  })
  file: any;
}
