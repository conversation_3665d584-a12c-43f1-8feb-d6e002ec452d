import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>A<PERSON>y, <PERSON>Enum, IsOptional, IsString } from "class-validator";
import { UserDefinedTopic } from "src/module/enum";
import { Operation } from "src/module/enum/operation.enum";

export class CreateNotificationTargetDto {
  @ApiProperty({ required: true, example: "Woman below 30 yo" })
  @IsString()
  name: string;

  @ApiProperty({ required: true, enum: UserDefinedTopic, example: UserDefinedTopic.CARD_NUMBER })
  @IsEnum(UserDefinedTopic)
  topic: UserDefinedTopic;

  @ApiProperty({ required: false, example: ["1234567", "8769087"] })
  @IsArray()
  @IsOptional()
  identifier: Record<string, any>;
}

export class ImportNotificationDto {
  @ApiProperty({ required: true, enum: Operation })
  @IsEnum(Operation)
  operation: Operation;

  @ApiProperty({
    required: true,
    type: "string",
    format: "binary",
  })
  file: any;
}
