import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsEnum, IsOptional, ValidateIf } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { UserDefinedTopic } from "src/module/enum";
import { ScreenTypeEnum } from "src/module/screen/enum/screen-type-enum";

export class GetNotificationTargetDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false, enum: UserDefinedTopic, example: UserDefinedTopic.CARD_NUMBER })
  @ValidateIf((obj) =>
    [
      UserDefinedTopic.CARD_NUMBER,
      UserDefinedTopic.MEMBER_TIER,
      UserDefinedTopic.GENDER,
      UserDefinedTopic.DOB,
      UserDefinedTopic.ALL,
    ].includes(obj.type),
  )
  @IsEnum(UserDefinedTopic)
  @IsOptional()
  topic?: UserDefinedTopic;
}
