import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { UserDefinedTopic } from "src/module/enum/fcm-publisher.enum";

@Schema({ timestamps: true })
export class NotificationTarget {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, type: String, enum: UserDefinedTopic })
  topic: string;

  @Prop({ type: Array })
  identifier: Array<any>;
}

export type NotificationTargetDocument = NotificationTarget & Document;

export const NotificationTargetSchema = SchemaFactory.createForClass(NotificationTarget);

NotificationTargetSchema.plugin(mongoosePaginate);
