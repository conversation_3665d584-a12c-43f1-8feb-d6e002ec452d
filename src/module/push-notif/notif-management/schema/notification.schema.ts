import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { ConvertPathToUrl, ConvertToDate, ConvertUrlToPath, HeadingSlash } from "src/utils/function.util";
import { NotificationTarget } from "./notification-target.schema";
import { SchemaTypes, Types, Document } from "mongoose";

@Schema({ timestamps: true, toJSON: { getters: true }, toObject: { getters: true } })
export class Notification {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  @Prop()
  body: string;

  @Prop({ required: false, type: String, get: ConvertPathToUrl, set: ConvertUrlToPath })
  image: string;

  @Prop({ set: HeadingSlash })
  target_url: string;

  @Prop({ type: [SchemaTypes.ObjectId], ref: NotificationTarget.name, default: [] })
  target: Types.ObjectId[];

  @Prop({ required: true, type: Boolean })
  status: boolean;

  @Prop({ required: false, type: Date, set: ConvertToDate })
  startDate: Date;

  @Prop({ default: false, index: true })
  isSent: boolean;
}

export type NotificationDocument = Notification & Document;

export const NotificationSchema = SchemaFactory.createForClass(Notification);

NotificationSchema.plugin(mongoosePaginate);
