import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Notification, NotificationDocument } from "./schema/notification.schema";
import { NotificationTarget, NotificationTargetDocument } from "./schema/notification-target.schema";
import mongoose, { PaginateModel } from "mongoose";
import { CreateNotificationDto } from "./dto/create-notification.dto";
import * as path from "path";
import { AmazonS3Services } from "src/amazon-s3/amazons3.service";
import { CreateNotificationTargetDto } from "./dto/create-notification-target.dto";
import { UpdateNotificationTargetDto } from "./dto/update-notification-target.dto";
import { GetNotificationTargetDto } from "./dto/get-notification-target.dto";
import { GetNotificationDto } from "./dto/get-notification.dto";
import readXlsxFile from "read-excel-file/node";
import writeXlsxFile from "write-excel-file/node";
import { Operation } from "src/module/enum/operation.enum";
import { TopicSendMapping, UserDefinedTopic } from "src/module/enum";
import { CreatedFrom, PushNotifTransport, PushNotifType } from "src/module/enum/push-notif-sender.enum";
import { PushNotifSenderService } from "../push-notif-sender/push-notif-sender.service";

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(Notification.name) private readonly notificationModel: PaginateModel<NotificationDocument>,
    @InjectModel(NotificationTarget.name)
    private readonly notificationTargetModel: PaginateModel<NotificationTargetDocument>,
    private readonly s3Services: AmazonS3Services,
    private readonly pushNotifSenderService: PushNotifSenderService,
  ) {}

  /**
   * create notification
   * @param {CreateNotificationDto} createNotificationDto
   * @param {File} file
   */
  async createNotification(createNotificationDto: CreateNotificationDto, file: Express.Multer.File) {
    if (file) {
      const uploaded = await this._upload(file);
      createNotificationDto.image = uploaded.Location;
    }

    if (!Array.isArray(createNotificationDto.target)) {
      createNotificationDto.target = createNotificationDto.target.split(",");
    }

    const newNotification = new this.notificationModel(createNotificationDto);
    return newNotification.save();
  }

  /**
   * update notification
   * @param {string} id
   * @param {UpdateScreenDto} params
   * @param {File} file
   */
  async updateNotification(id: string, params: CreateNotificationDto, file: Express.Multer.File) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    if (file) {
      const uploaded = await this._upload(file);
      params.image = uploaded.Location;
    }

    if (!Array.isArray(params.target)) {
      params.target = params.target.split(",");
    }

    const update = await this.notificationModel.findOneAndUpdate({ _id: id }, params, { new: true });

    if (!update) {
      throw "Update notification failed.";
    }
  }

  /**
   * get notification by id
   * @param {string} id
   */
  async findNotificationById(id: string) {
    try {
      const notification = await this.notificationModel
        .findById(id)
        .populate({ path: "target", select: { topic: 1, identifier: 1, name: 1 } });
      if (!notification) {
        throw new HttpException("notification is not found", HttpStatus.BAD_REQUEST);
      }

      return notification;
    } catch (e) {
      throw new HttpException("notification is not found", HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * get all notification
   * @param GetNotificationTargetDto params
   */
  async findAllNotification(params: GetNotificationDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort } = params;
    if (params.title) {
      filter.title = { $regex: new RegExp(params.title), $options: "i" };
    }
    if (params.status) {
      filter.status = params.status;
    }
    if (params.isSent) {
      filter.isSent = params.isSent;
    }

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
      populate: [{ path: "target", select: { _id: 1, name: 1, topic: 1 } }],
    };

    const result = await this.notificationModel.paginate(filter, options);

    return result;
  }

  /**
   * delete a notification
   * @param {string} id
   */
  async removeNotification(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const deleting = await this.notificationModel.deleteOne({ _id: id }).exec();

    return deleting;
  }

  /**
   * create notification target
   * @param {CreateNotificationTargetDto} createNotificationTargetDto
   * @param {File} file
   */
  async createNotificationTarget(createNotificationTargetDto: CreateNotificationTargetDto) {
    const newAnswer = new this.notificationTargetModel(createNotificationTargetDto);
    return await newAnswer.save();
  }

  /**
   * find a notification target
   * @param {string} id
   */
  async findTargetById(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const target = await this.notificationTargetModel.findById(id);

    if (!target) {
      throw new HttpException("Notification target is not found", HttpStatus.BAD_REQUEST);
    }

    return target;
  }

  /**
   * update notification target
   * @param {string} id
   * @param {UpdateNotificationTargetDto} params
   */
  async updateTarget(id: string, params: UpdateNotificationTargetDto) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const target = await this.notificationTargetModel.findOneAndUpdate({ _id: id }, params, { new: true });
    if (!target) {
      throw "Update data failed.";
    }
    return target;
  }

  /**
   * get all notification target
   * @param GetNotificationTargetDto params
   */
  async findAllTarget(params: GetNotificationTargetDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort } = params;
    if (params.name) {
      filter.name = { $regex: new RegExp(params.name), $options: "i" };
    }
    if (params.topic) {
      filter.topic = params.topic;
    }

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    const result = await this.notificationTargetModel.paginate(filter, options);

    return result;
  }

  /**
   * delete a notification target
   * @param {string} id
   */
  async removeTarget(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const deleting = await this.notificationTargetModel.deleteOne({ _id: id }).exec();

    return deleting;
  }

  /**
   * import target
   * @param {string} id
   * @param {Buffer} file
   * @param {string} type
   */
  async importTarget(id: string, file: Buffer, type: string) {
    try {
      const rows = await readXlsxFile(Buffer.from(file.buffer));

      rows.shift();
      const identifier = [];
      rows.map((d) => {
        if (d[0]) {
          identifier.push(d[0].toString());
        }
      });
      if (identifier.length < 0) {
        throw new HttpException("Invalid format file", HttpStatus.BAD_REQUEST);
      }
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
      }
      const target = await this.notificationTargetModel.findById(id);

      if (!target) {
        throw new HttpException("Notification target is not found", HttpStatus.BAD_REQUEST);
      }
      const exIdentifier =
        type == Operation.Replace || target.identifier.length <= 0 ? identifier : [...target.identifier, ...identifier];
      target.identifier = exIdentifier;
      return await target.save();
    } catch (e) {
      console.error(e);
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * download notification target template
   * @param {params} download
   * @returns
   */
  async downloadTargetTemplate() {
    const header = [{ value: "Identifier", fontWeight: "bold", align: "left" }];

    const row: Array<Array<Record<string, any>>> = [header];
    row.push([{ value: "51694666510000081" }]);
    row.push([{ value: "51694666510000082" }]);
    row.push([{ value: "51694666510000083" }]);

    return await writeXlsxFile(row, {
      columns: [{ width: 30 }],
      buffer: true,
    });
  }

  /**
   * send now by id
   * @param id
   * @returns
   */
  async sendNow(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const notification = await this.notificationModel
      .findById(id)
      .populate({ path: "target", select: { topic: 1, identifier: 1 } });
    if (!notification) {
      throw new HttpException("notification is not found", HttpStatus.BAD_REQUEST);
    }
    return await this._processQueueItem(notification, true);
  }

  /**
   * reset notification by id
   * @param id
   * @returns
   */
  async resetNotification(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const notification = await this.notificationModel.findById(id);
    if (!notification) {
      throw new HttpException("notification is not found", HttpStatus.BAD_REQUEST);
    }
    notification.isSent = false;
    await notification.save();
  }

  /**
   * process queue notification
   */
  async queueingNotif() {
    const filter = {
      $and: [
        { status: true },
        { isSent: false },
        {
          $or: [
            {
              startDate: { $lte: new Date() },
            },
          ],
        },
      ],
    };
    const result = await this.notificationModel
      .find(filter)
      .populate({ path: "target", select: { topic: 1, identifier: 1 } });

    if (result.length <= 0) {
      return;
    }
    let items = [];

    for (let i = 0; i < result.length; i++) {
      const part = await this._processQueueItem(result[i], false);
      items = [...items, ...part];
    }
    return items;
  }

  /**
   * process item notification
   * @param item
   */
  private async _processQueueItem(notification, force) {
    let items = [];

    for (let i = 0; i < notification.target.length; i++) {
      const parts = await this._splitQueueItem(notification, notification.target[i], force);
      await this._sendToPublisher(notification, parts);
      items = [...items, ...parts];
    }
    return items;
  }

  /**
   * split notifications into several targets
   * @param item
   * @param target
   */
  private async _splitQueueItem(item, target, force) {
    const sitems = [];
    const itopic = [];
    if (target["topic"] == UserDefinedTopic.ALL) {
      itopic.push({ type: UserDefinedTopic.ALL, value: ".true" });
    } else {
      const listIds = await this.chunkArrayInGroups(target["identifier"], 1000);
      listIds.map((d) => {
        itopic.push({ type: target["topic"], value: this._addDot(target["topic"], d) });
      });
    }
    itopic.map((topic) => {
      sitems.push({
        title: item.title,
        body: item.body,
        image: item.image,
        target_url: item.target_url,
        type: PushNotifType.PROMOTION,
        transport: PushNotifTransport.TOPIC,
        topic: [topic],
        createdFrom: CreatedFrom.CMS,
        isSent: false,
        send_date: !force ? item.startDate : false,
      });
    });
    return sitems;
  }

  /**
   * add dot
   * @param type
   * @param arr
   * @returns  array
   */
  private _addDot(type, arr) {
    const vals = [],
      dot = TopicSendMapping[type];
    arr.map((item) => {
      vals.push(dot ? "." + item : item);
    });
    return vals.join(",");
  }

  /**
   * send to publisher
   * @param {notification}
   * @param {items}
   */
  private async _sendToPublisher(notification, items) {
    for (let i = 0; i < items.length; i++) {
      await this._addToQueue(items[i]);
    }

    /*items.map(async (item) => {
      await this._addToQueue(item);
    });*/

    notification.isSent = true;
    await notification.save();
  }

  /**
   *save to publisher collection
   * @param item
   */
  private async _addToQueue(item) {
    try {
      await this.pushNotifSenderService.createApi(item);
    } catch (e) {
      console.error(e);
    }
  }

  /**
   * chunk array
   * @param arr
   * @param size
   */
  private async chunkArrayInGroups(arr, size) {
    const myArray = [];
    for (let i = 0; i < arr.length; i += size) {
      myArray.push(arr.slice(i, i + size));
    }
    return myArray;
  }

  /**
   * upload file
   * @param {Express.Multer.File} file
   */
  private async _upload(file: Express.Multer.File) {
    if (!file) {
      throw new HttpException("File cannot be empty.", HttpStatus.BAD_REQUEST);
    }
    const ext = path.extname(file.originalname);
    const mimetype = file.mimetype;
    if (!ext.match(/(jpg|jpeg|png)$/)) {
      throw new HttpException("Invalid file format. Allowed file format: jpg, jpeg or png", HttpStatus.BAD_REQUEST);
    }
    return await this.s3Services.uploadFile(file.buffer, ext, "notification-assets/", mimetype);
  }
}
