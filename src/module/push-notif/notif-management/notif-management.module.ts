import { Modu<PERSON> } from "@nestjs/common";
import { NotificationService } from "./notification.service";
import { MongooseModule } from "@nestjs/mongoose";
import { NotificationSchema } from "./schema/notification.schema";
import { AdminNotificationController } from "./admin-notification.controller";
import { AmazonS3Services } from "src/amazon-s3/amazons3.service";
import { NotificationTargetSchema } from "./schema/notification-target.schema";
import { NotificationController } from "./notification.controller";
import { PushNotifSenderService } from "../push-notif-sender/push-notif-sender.service";
import { PushNotifSenderModule } from "../push-notif-sender/push-notif-sender.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: "Notification",
        schema: NotificationSchema,
      },
      {
        name: "NotificationTarget",
        schema: NotificationTargetSchema,
      },
    ]),
    PushNotifSenderModule,
  ],
  controllers: [AdminNotificationController, NotificationController],
  providers: [AmazonS3Services, NotificationService, PushNotifSenderService],
})
export class NotifManagementModule {}
