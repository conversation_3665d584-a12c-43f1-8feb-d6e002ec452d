import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { <PERSON>A<PERSON>y, IsMongoId, ValidateNested } from "class-validator";
import { CreateMegaMenuDto } from "./create-mega-menu.dto";

export class MegaMenuDto {
  @ApiProperty({ type: () => [CreateMegaMenuDto], required: false })
  @IsArray()
  @Type(() => CreateMegaMenuDto)
  @ValidateNested()
  data: CreateMegaMenuDto[];

  @ApiProperty()
  @IsMongoId({ each: true })
  delete: string[];
}
