import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayMaxSize,
  IsArray,
  IsBoolean,
  IsDefined,
  IsEnum,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from "class-validator";
import { MegaMenuAlignEnum, MegaMenuType } from "../enum/mega-menu-align-enum";
import { MegaMenuChildrenDto } from "./mega-menu-children.dto";
import { MegaMenuImageDto } from "./mega-menu-image.dto";
import mongoose from "mongoose";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class CreateMegaMenuDto {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  menu_id: string;

  @ApiProperty({ type: mongoose.Schema.Types.ObjectId, required: false })
  @ValidateIf((obj) => obj.product_group_id)
  @IsOptional()
  @IsMongoId()
  product_group_id: string;

  @ApiProperty({ type: String, required: false })
  @ValidateIf((obj) => obj.type === MegaMenuType.custom)
  @IsDefined({ message: "Type should be custom if you want to add url_key" })
  @IsString()
  url_key: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: "Mega Menu Tyoe",
    enum: MegaMenuType,
    required: true,
  })
  @IsEnum(MegaMenuType)
  type: MegaMenuType;

  @ApiProperty({ type: String, required: true })
  @IsString()
  customName: string;

  @ApiProperty({
    description: "Enum Align Mega Menu",
    enum: MegaMenuAlignEnum,
  })
  @IsEnum(MegaMenuAlignEnum)
  align: MegaMenuAlignEnum;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  @TransformBoolean()
  status: boolean;

  @ApiProperty({ type: () => [MegaMenuImageDto] })
  @IsArray()
  @IsOptional()
  @Type(() => MegaMenuImageDto)
  @ValidateNested()
  @ArrayMaxSize(2, { message: "Maximum number for image is 2" })
  image: MegaMenuImageDto[];

  @ApiProperty({ type: () => [MegaMenuChildrenDto], required: false })
  @IsArray()
  @Type(() => MegaMenuChildrenDto)
  @ValidateNested()
  children: MegaMenuChildrenDto[];
}
