import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsDefined, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateIf } from "class-validator";
import mongoose from "mongoose";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";
import { MegaMenuType } from "../enum/mega-menu-align-enum";

export class MegaMenuSecondChildrenDto {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  menu_id: string;

  @ApiProperty({ type: mongoose.Schema.Types.ObjectId, required: false })
  @ValidateIf((obj) => obj.product_group_id != "")
  @IsOptional()
  @IsMongoId()
  product_group_id: string;

  @ApiProperty({ type: String, required: false })
  @ValidateIf((obj) => obj.type === MegaMenuType.custom)
  @IsDefined({ message: "Type should be custom if you want to add url_key" })
  @IsString()
  url_key?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: "Mega Menu Tyoe",
    enum: MegaMenuType,
    required: true,
  })
  @IsEnum(MegaMenuType)
  type: MegaMenuType;

  @ApiProperty({ type: String, required: true })
  @IsString()
  customName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  @TransformBoolean()
  status: boolean;
}
