import { Prop, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";
import { MegaMenuAlignEnum, MegaMenuType } from "../enum/mega-menu-align-enum";
import { IMegaMenuImageInterface } from "../interface/image-interface";

@Schema({
  timestamps: true,
})
export class MegaMenu extends Document {
  @Prop({ required: true, type: String, unique: true })
  menu_id: string;

  @Prop({ type: String })
  product_group_id: string;

  @Prop({ required: true, type: String, enum: MegaMenuType })
  type: string;

  @Prop({ required: false, type: String })
  url_key: string;

  @Prop({ required: true, type: String })
  customName: string;

  @Prop({ required: false, type: String, enum: MegaMenuAlignEnum, default: MegaMenuAlignEnum.left })
  align: MegaMenuAlignEnum;

  @Prop({ required: true, type: Boolean })
  status: boolean;

  @Prop({
    required: false,
    type: [
      {
        img_url: {
          type: String,
          required: true,
        },
        path_url: {
          type: String,
          required: true,
        },
      },
    ],
    _id: false,
  })
  image: IMegaMenuImageInterface[];

  @Prop({ required: true, type: Number, default: 1 })
  position: number;

  @Prop({ required: false, type: SchemaTypes.Mixed, ref: MegaMenu.name })
  children: Array<Record<string, any>>;
}

export type MegaMenuDocument = MegaMenu & Document;

export const MegaMenuSchema = SchemaFactory.createForClass(MegaMenu);
