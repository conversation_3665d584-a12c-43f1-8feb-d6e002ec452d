import { <PERSON>du<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { MegaMenuController } from "./mega-menu.controller";
import { MegaMenuService } from "./mega-menu.service";
import { MegaMenuSchema } from "./schema/mega-menu.schema";
import { ProductGroupModule } from "../microservices/product/product-group.module";
import { ConfigModule } from "@nestjs/config";
import { RedisModule } from "@liaoliaots/nestjs-redis";
import { HttpModule, HttpService } from "@nestjs/axios";

@Module({
  controllers: [MegaMenuController],
  providers: [MegaMenuService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "MegaMenu",
        schema: MegaMenuSchema,
      },
    ]),
    ProductGroupModule,
    ConfigModule,
    RedisModule.forRoot({ config: { url: process.env.REDIS_HOST + ":" + process.env.REDIS_PORT_PUBLIC } }),
    HttpModule,
  ],
})
export class MegaMenuModule {}
