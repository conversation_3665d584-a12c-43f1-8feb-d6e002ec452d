import { Body, Controller, Get, Post, Req } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiHeader, ApiTags } from "@nestjs/swagger";
import { Public, RoleMatchingMode, Roles } from "keycloak-connect-tbs";
import { Role } from "../enum/role.enum";
import { MegaMenuDto } from "./dto/mega-menu.dto";
import { MegaMenuService } from "./mega-menu.service";
import { PurgeEnum } from "../home/<USER>/purge.enum";
import { EventEmitter2 } from "@nestjs/event-emitter";

@ApiTags("Mega Menu")
@ApiBearerAuth("access-token")
@Controller("mega-menu")
export class MegaMenuController {
  constructor(private readonly megaMenuService: MegaMenuService, private readonly eventEmitter: EventEmitter2) {}

  @Post("/admin")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @ApiBody({ type: MegaMenuDto, description: "Create / Edit Mega Menu" })
  async create(@Body() payload: MegaMenuDto) {
    const megaMenu = await this.megaMenuService.create(payload);
    this.eventEmitter.emit("rebuild-home");
    return megaMenu;
  }

  @Get("/admin")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  getAdminMegaMenu() {
    return this.megaMenuService.getMegaMenuAdmin();
  }

  @Get()
  @Public()
  @ApiHeader({ name: "purge", enum: PurgeEnum })
  getWebMegaMenu(@Req() req) {
    return this.megaMenuService.getMegaMenuCustomer(req);
  }
}
