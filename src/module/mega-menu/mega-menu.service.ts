import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { ProductGroupMSService } from "../microservices/product/product-group.service";
import { MegaMenuDto } from "./dto/mega-menu.dto";
import { MegaMenuDocument } from "./schema/mega-menu.schema";
import { InjectRedis } from "@liaoliaots/nestjs-redis";
import Redis from "ioredis";
import { lastValueFrom, map } from "rxjs";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
import { PurgeEnum } from "../home/<USER>/purge.enum";

@Injectable()
export class MegaMenuService {
  constructor(
    @InjectModel("MegaMenu") private model: Model<MegaMenuDocument>,
    private readonly productGroupMs: ProductGroupMSService,
    @InjectRedis()
    private readonly redisService: Redis,
    private configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async create(payload: MegaMenuDto) {
    const megaMenu = [];
    try {
      for (const data of payload.data) {
        if (data.image) {
          for (const image of data.image) {
            image.img_url = image.img_url.split(process.env.AWS_S3_BASE_URL)[1];
          }
        }
        megaMenu.push(
          await this.model.findOneAndUpdate({ menu_id: data.menu_id }, data, { new: true, upsert: true }).exec(),
        );
      }
      await this.model.deleteMany({ _id: { $in: payload.delete } }).exec();
    } catch (e) {
      console.log(e);
      throw new HttpException("Failed to create / edit mega menu", HttpStatus.INTERNAL_SERVER_ERROR);
    }
    return megaMenu;
  }

  async getMegaMenuCustomer(req) {
    const purge = req.headers.purge || PurgeEnum.keep;
    const cacheKey = `megamenu-home`;
    if (purge == PurgeEnum.keep) {
      const home = await this.redisService.get(cacheKey);
      if (home) return JSON.parse(home);
    }

    const result = await this.model.find({ status: true }).sort({ position: 1 }).lean();

    if (result.length <= 0) {
      return [];
    }

    const product_group_id = [];

    for (const data of result) {
      if (data.product_group_id != "") {
        product_group_id.push(data.product_group_id);
      }
      for (const firstChildren of data.children) {
        if (firstChildren.product_group_id != "") {
          product_group_id.push(firstChildren.product_group_id);
        }
        for (const secondChildren of firstChildren.children) {
          if (secondChildren.product_group_id != "") {
            product_group_id.push(secondChildren.product_group_id);
          }
        }
      }
    }

    const multipleId = product_group_id.toString().replace(/,/g, ";");

    const categories = await this.productGroupMs.getAllResources(multipleId);
    // const productRange = await this._getAllRange(req);
    const productRange = [];

    const categoryMap = categories.data.reduce((final, tmp) => {
      final[tmp._id] = {
        url_key: tmp.url_key,
        name: tmp.name,
      };
      return final;
    }, {});

    const finalResult = result.map((menu1) => {
      // eslint-disable-next-line prefer-const
      let { product_group_id: cat1, children: child1, ...rest1 } = menu1;
      if (menu1.image) {
        for (const data of menu1.image) {
          data.img_url = process.env.AWS_S3_BASE_URL + data.img_url;
        }
      }
      const category1 = categoryMap[cat1];
      child1 = child1.map((menu2) => {
        // eslint-disable-next-line prefer-const
        let { product_group_id: cat2, children: child2, ...rest2 } = menu2;
        const category2 = categoryMap[cat2];
        child2 = child2.map((menu3) => {
          const { product_group_id: cat3, ...rest3 } = menu3;
          const category3 = categoryMap[cat3];
          return {
            product_group_id: cat3,
            ...category3,
            ...rest3,
          };
        });
        return {
          product_group_id: cat2,
          ...category2,
          ...rest2,
          children: child2,
        };
      });
      return {
        product_group_id: cat1,
        ...category1,
        ...rest1,
        children: child1,
      };
    });

    finalResult.map((item) => {
      if (item.url_key == "range") {
        item.children = productRange.map((range) => {
          range["menu_id"] = "111";
          range["position"] = "1";
          range["type"] = "product_group";
          range["customName"] = "";
          range["children"] = [];
          range["status"] = true;
          // samakan isi dengan mega menu yang lain
          return range;
        });
      }

      return item;
    });

    await this.redisService.set(
      cacheKey,
      JSON.stringify(finalResult),
      "EX",
      process.env.REDIS_URL_CHECKER_CACHED_LIFETIME,
    );

    return finalResult;
  }

  async getMegaMenuAdmin() {
    const result = await this.model.find().sort({ position: 1 }).lean();

    if (result.length <= 0) {
      return [];
    }

    const product_group_id = [];

    for (const data of result) {
      if (data.product_group_id != "") {
        product_group_id.push(data.product_group_id);
      }
      for (const firstChildren of data.children) {
        if (firstChildren.product_group_id != "") {
          product_group_id.push(firstChildren.product_group_id);
        }
        for (const secondChildren of firstChildren.children) {
          if (secondChildren.product_group_id != "") {
            product_group_id.push(secondChildren.product_group_id);
          }
        }
      }
    }

    const multipleId = product_group_id.toString().replace(/,/g, ";");

    // multipleId = "mongoId;mongoId;mongoId;mongoId;mongoId;"

    const categories = await this.productGroupMs.getAllResources(multipleId);

    // categories = {statusCode, data}

    const categoryMap = categories.data.reduce((final, tmp) => {
      final[tmp._id] = {
        url_key: tmp.url_key,
        name: tmp.name,
      };
      return final;
    }, {});
    // categoryMap = {
    //   _id1: {},
    //   _id2: {}
    // }

    // for (const data of result) {
    //   Object.assign(data, {
    //     url_path: categories.data.find((element) => element._id.toString() == data.category_product_id).url_path,
    //     name: categories.data.find((element) => element._id.toString() == data.category_product_id).name,
    //   });
    //   console.log(categories.data.find((element) => element._id.toString() == data.category_product_id).name);
    //   console.log(categories.data.find((element) => element._id.toString() == data.category_product_id).url_path);
    //   for (const firstChildren of data.children) {
    //     Object.assign(firstChildren, {
    //       url_path: categories.data.find((element) => element._id.toString() == firstChildren.category_product_id)
    //         .url_path,
    //       name: categories.data.find((element) => element._id.toString() == firstChildren.category_product_id).name,
    //     });
    //     for (const secondChildren of firstChildren.children) {
    //       Object.assign(secondChildren, {
    //         url_path: categories.data.find((element) => element._id.toString() == secondChildren.category_product_id)
    //           .url_path,
    //         name: categories.data.find((element) => element._id.toString() == secondChildren.category_product_id).name,
    //       });
    //     }
    //   }
    // }

    const finalResult = result.map((menu1) => {
      // eslint-disable-next-line prefer-const
      let { product_group_id: cat1, children: child1, ...rest1 } = menu1;
      if (menu1.image) {
        for (const data of menu1.image) {
          data.img_url = process.env.AWS_S3_BASE_URL + data.img_url;
        }
      }
      const category1 = categoryMap[cat1];

      child1 = child1.map((menu2) => {
        // eslint-disable-next-line prefer-const
        let { product_group_id: cat2, children: child2, ...rest2 } = menu2;
        const category2 = categoryMap[cat2];
        child2 = child2.map((menu3) => {
          const { product_group_id: cat3, ...rest3 } = menu3;
          const category3 = categoryMap[cat3];
          return {
            product_group_id: cat3,
            ...category3,
            ...rest3,
          };
        });
        return {
          product_group_id: cat2,
          ...category2,
          ...rest2,
          children: child2,
        };
      });
      return {
        product_group_id: cat1,
        ...category1,
        ...rest1,
        children: child1,
      };
    });

    return finalResult;
  }

  private async _getAllRange(req) {
    const host = this.configService.get<string>("OMS_HOST");
    const url = `${host}/api/v1/product-group?page=1&limit=100&is_range=true&is_lean=true`;
    try {
      const requestUrl = url;
      const requestConfig = {
        headers: {
          Authorization: req.headers.authorization,
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData.data.docs;
    } catch (e) {
      console.error(e);
      throw new HttpException(e, e.response.status);
    }
    return [];
  }
}
