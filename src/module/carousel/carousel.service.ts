import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { CreateCarouselDto } from "./dto/create-carousel.dto";
import { GetCarouselDto } from "./dto/get-carousel-dto";
import { UpdateCarouselDto } from "./dto/update-carousel.dto";
import { CarouselDocument } from "./schema/carousel.schema";

@Injectable()
export class CarouselService {
  constructor(@InjectModel("Carousel") private carouselModel: PaginateModel<CarouselDocument>) {}

  async findAllAdmin(params: GetCarouselDto) {
    const filter: Record<string, any> = {};

    const { page = 1, limit = 10, sort } = params;

    if (params.title) filter.title = new RegExp(params.title, "i");
    if (params.description) filter.description = new RegExp(params.description, "i");
    if (typeof params.status === "boolean") filter.is_active = params.status;

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    const result = await this.carouselModel.paginate(filter, options);

    return result;
  }

  async findOneAdmin(id: string) {
    const result = await this.carouselModel.findById(id);

    return result;
  }

  async create(payload: CreateCarouselDto) {
    return await this.carouselModel.create(Object.assign({}, payload));
  }

  async update(_id: string, data: UpdateCarouselDto) {
    return await this.carouselModel.findOneAndUpdate({ _id }, data, { new: true }).exec();
  }

  async remove(_id: string) {
    return await this.carouselModel.findOneAndDelete({ _id }).exec();
  }

  async findAllPublic() {
    const result = await this.carouselModel
      .find({
        is_active: true,
        $and: [{ start_date: { $lte: Date.now() } }, { end_date: { $gte: Date.now() } }],
      })
      .sort({ position: 1 })
      .exec();

    for (const data of result) {
      if (!data.mobile_image) {
        data.mobile_image = data.web_image;
      }
    }

    return result;
  }

  async carouselStatusDeactivator(): Promise<any> {
    /** Auto deactivate carousel */
    await this.carouselModel
      .updateMany({ end_date: { $lt: new Date() }, status: true }, { status: false }, { new: true })
      .exec();

    /** Auto activate carousel */
    await this.carouselModel
      .updateMany(
        { $and: [{ start_date: { $lte: Date.now() } }, { end_date: { $gte: Date.now() } }], status: false },
        { status: true },
        { new: true },
      )
      .exec();
  }
}
