import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsHexColor, IsNotEmpty, IsOptional, IsString, IsUrl } from "class-validator";
import { IsImageUrlRegistered } from "src/decorator/image-url-validator";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class CreateCarouselDto {
  @ApiProperty({ example: "TBS Carousel", required: false })
  @IsOptional()
  @IsString()
  title: string;

  @ApiProperty({
    example: "https://icarus-s3.s3.ap-southeast-3.amazonaws.com/carousels/c2b8bffb-30cf-4827-a7ff-35dd6d4f2ec8.jpeg",
  })
  @IsNotEmpty()
  @IsString()
  @IsUrl()
  @IsImageUrlRegistered()
  web_image: string;

  @ApiProperty({
    example: "https://icarus-s3.s3.ap-southeast-3.amazonaws.com/carousels/c2b8bffb-30cf-4827-a7ff-35dd6d4f2ec8.jpeg",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUrl()
  @IsImageUrlRegistered()
  mobile_image: string;

  @ApiProperty({ example: "Tbs Promo New Year", required: false })
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  @TransformBoolean()
  status: boolean;

  @ApiProperty({ example: "any-2-edt-for-899k", required: false, description: "expected value is url_key" })
  @IsOptional()
  @IsString()
  link: string;

  @ApiProperty({ example: "Belanja minimal 100k", required: false })
  @IsOptional()
  @IsString()
  tnc: string;

  @ApiProperty()
  @Transform(({ value }) => new Date(value))
  @IsNotEmpty()
  @IsDate()
  start_date: Date;

  @ApiProperty()
  @Transform(({ value }) => new Date(value))
  @IsNotEmpty()
  @IsDate()
  end_date: Date;

  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @Type(() => Number)
  position: number;

  @ApiProperty({ required: false, type: String })
  @IsOptional()
  @IsString()
  @IsHexColor()
  title_color: string;

  @ApiProperty({ required: false, type: String })
  @IsOptional()
  @IsString()
  @IsHexColor()
  desc_color: string;

  @ApiProperty({ required: false, type: String })
  @IsOptional()
  @IsString()
  @IsHexColor()
  button_color: string;

  @ApiProperty({ required: false, type: String })
  @IsOptional()
  @IsString()
  @IsHexColor()
  button_txt_color: string;
}
