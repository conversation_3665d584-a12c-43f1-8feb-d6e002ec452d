import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsOptional, IsString } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class GetCarouselDto extends PaginationParamDto {
  @ApiProperty({ required: false, type: String })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ required: false, type: String })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false, type: Boolean })
  @IsOptional()
  @IsBoolean()
  @TransformBoolean()
  status?: boolean;
}
