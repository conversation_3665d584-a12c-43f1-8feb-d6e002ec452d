import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Public, RoleMatchingMode, Roles } from "keycloak-connect-tbs";
import { Role } from "../enum";
import { CarouselService } from "./carousel.service";
import { CreateCarouselDto } from "./dto/create-carousel.dto";
import { GetCarouselDto } from "./dto/get-carousel-dto";
@ApiTags("Carousel")
@ApiBearerAuth("access-token")
@Controller("carousel")
// TODO : resources
export class CarouselController {
  constructor(private readonly carouselService: CarouselService, private readonly eventEmitter: EventEmitter2) {}

  @Get("/admin")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  find(@Query() pagination: GetCarouselDto) {
    return this.carouselService.findAllAdmin(pagination);
  }

  @Get("/admin/:id")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  findOne(@Param("id") _id: string) {
    return this.carouselService.findOneAdmin(_id);
  }

  @Post("/admin")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  create(@Body() payload: CreateCarouselDto) {
    this.eventEmitter.emit("rebuild-home");
    return this.carouselService.create(payload);
  }

  @Patch("/admin/:id")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  update(@Param("id") id: string, @Body() payload: CreateCarouselDto) {
    this.eventEmitter.emit("rebuild-home");
    return this.carouselService.update(id, payload);
  }

  @Delete("/admin/:id")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  remove(@Param("id") _id: string) {
    this.eventEmitter.emit("rebuild-home");
    return this.carouselService.remove(_id);
  }

  @Get()
  @Public()
  findAllPublic() {
    return this.carouselService.findAllPublic();
  }

  @Get("/deactivate")
  @Public()
  carouselStatusDeactivator(): Promise<any> {
    this.eventEmitter.emit("rebuild-home");
    return this.carouselService.carouselStatusDeactivator();
  }
}
