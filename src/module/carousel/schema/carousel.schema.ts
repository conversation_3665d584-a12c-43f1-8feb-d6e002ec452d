import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { ConvertPathToUrl, ConvertUrlToPath } from "src/utils/function.util";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class Carousel extends Document {
  @Prop({ required: false, type: String })
  description: string;

  @Prop({ required: true, type: String, get: ConvertPathToUrl, set: ConvertUrlToPath })
  web_image: string;

  @Prop({ required: false, type: String, get: ConvertPathToUrl, set: ConvertUrlToPath })
  mobile_image: string;

  @Prop({ required: false, type: String })
  title: string;

  @Prop({ required: true, type: Boolean })
  status: boolean;

  @Prop({ required: false, type: String })
  link: string;

  @Prop({ required: false, type: String })
  tnc: string;

  @Prop({ required: true, type: Date })
  start_date: Date;

  @Prop({ required: true, type: Date })
  end_date: Date;

  @Prop({ required: true, type: Number })
  position: number;

  @Prop({ required: false, type: String })
  title_color: string;

  @Prop({ required: false, type: String })
  desc_color: string;

  @Prop({ required: false, type: String })
  button_color: string;

  @Prop({ required: false, type: String })
  button_txt_color: string;
}

export type CarouselDocument = Carousel & Document;

export const CarouselSchema = SchemaFactory.createForClass(Carousel);

CarouselSchema.plugin(mongoosePaginate);
