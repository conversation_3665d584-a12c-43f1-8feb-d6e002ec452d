import { Modu<PERSON> } from "@nestjs/common";
import { CarouselService } from "./carousel.service";
import { CarouselController } from "./carousel.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { CarouselSchema } from "./schema/carousel.schema";
import { ApmModule } from "modules/nestjs-elastic-apm";

@Module({
  controllers: [CarouselController],
  providers: [CarouselService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "Carousel",
        schema: CarouselSchema,
      },
    ]),
    ApmModule.register(),
  ],
  exports: [CarouselService],
})
export class CarouselModule {}
