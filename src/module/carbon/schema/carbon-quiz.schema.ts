import { <PERSON><PERSON>, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Injectable } from "@nestjs/common";
import mongoose from "mongoose";
@Schema({
  timestamps: true,
})
@Injectable()
export class CarbonQuiz {
  @Prop({ required: true, trim: true })
  title: string;

  @Prop()
  description: string;

  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: "CarbonAnswer" }] })
  answers: mongoose.Schema.Types.ObjectId[];
}

export type CarbonQuizDocument = CarbonQuiz & Document;

export const CarbonQuizSchema = SchemaFactory.createForClass(CarbonQuiz);
