import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
@Schema({
  timestamps: true,
})
@Injectable()
export class CarbonAnswer {
  @Prop({ required: true, default: 0 })
  score: number;

  @Prop({ required: true, default: "" })
  text: string;

  @Prop()
  is_active: number;
}

export type CarbonAnswerDocument = CarbonAnswer & Document;

export const CarbonAnswerSchema = SchemaFactory.createForClass(CarbonAnswer);
