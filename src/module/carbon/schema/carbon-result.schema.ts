import { Injectable } from "@nestjs/common";
import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
@Schema({
  timestamps: true,
})
@Injectable()
export class CarbonResult {
  @Prop({ required: true, default: 0 })
  score: number;

  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId, ref: "User" })
  user_id: mongoose.Schema.Types.ObjectId;

  @Prop({ required: true, type: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }] })
  answers: mongoose.Schema.Types.ObjectId[];
}

export type CarbonResultDocument = CarbonResult & Document;

export const CarbonResultSchema = SchemaFactory.createForClass(CarbonResult);
