import { CarbonResultService } from "./carbon-result.service";
import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { CarbonAnswerService } from "./carbon-answer.service";
import { CarbonQuizService } from "./carbon-quiz.service";
import { CarbonController } from "./carbon.controller";
import { CarbonAnswerSchema } from "./schema/carbon-answer.schema";
import { CarbonQuizSchema } from "./schema/carbon-quiz.schema";
import { CarbonResultSchema } from "./schema/carbon-result.schema";
import { ApmModule } from "modules/nestjs-elastic-apm";
@Module({
  controllers: [CarbonController],
  providers: [CarbonQuizService, CarbonAnswerService, CarbonResultService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "CarbonAnswer",
        schema: CarbonAnswerSchema,
      },
      {
        name: "CarbonQuiz",
        schema: CarbonQuizSchema,
      },
      {
        name: "CarbonResult",
        schema: CarbonResultSchema,
      },
    ]),
    ApmModule.register(),
  ],
})
export class CarbonModule {}
