import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model } from "mongoose";
import { CarbonAnswer, CarbonAnswerDocument } from "./schema/carbon-answer.schema";
import { UpdateAnswerDto } from "./dto/update-answer.dto";
import { CreateAnswerDto } from "./dto/create-answer.dto";

@Injectable()
export class CarbonAnswerService {
  constructor(
    // @InjectModel("CarbonQuiz") private quizModel: Model<CarbonQuizDocument>,
    @InjectModel("CarbonAnswer") private answerModel: Model<CarbonAnswerDocument>,
  ) {}

  async create(dto: CreateAnswerDto): Promise<any> {
    const newData = new this.answerModel(dto);

    return newData.save();
  }

  async findById(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const Store = this.answerModel.findById(id).exec();

    return Store;
  }

  async findAll() {
    return this.answerModel.find({ is_active: 1 }).exec();
  }

  async update(id: string, payload: UpdateAnswerDto): Promise<CarbonAnswer | null> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const update = await this.answerModel.updateOne({ _id: id }, payload).exec();

    if (!update) {
      throw "Update data failed.";
    }

    return this.answerModel.findById(id);
  }

  async remove(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const deleting = await this.answerModel.deleteOne({ _id: id }).exec();

    return deleting;
  }
}
