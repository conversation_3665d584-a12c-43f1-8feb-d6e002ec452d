import { isMongoId } from "class-validator";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { CarbonResultDocument } from "./schema/carbon-result.schema";
import { CreateResultDto } from "./dto/create-result.dto";
import { CarbonAnswerDocument } from "./schema/carbon-answer.schema";

@Injectable()
export class CarbonResultService {
  constructor(
    @InjectModel("CarbonResult") private resultModel: Model<CarbonResultDocument>,
    @InjectModel("CarbonAnswer") private answerModel: Model<CarbonAnswerDocument>,
  ) {}

  async get(userId: string): Promise<any> {
    if (!isMongoId(userId)) {
      throw new HttpException("Invalid user id", HttpStatus.BAD_REQUEST);
    }

    return await this.resultModel.findOne({ user_id: userId }).sort({ createdAt: -1 }).lean().exec();
  }

  async create(dto: CreateResultDto): Promise<any> {
    if (!dto.answers || dto.answers.length === 0) {
      throw new HttpException("Invalid answers", HttpStatus.BAD_REQUEST);
    }

    let total = 0;
    const taskQueue = [];

    dto.answers.forEach(async (item) => {
      taskQueue.push(
        this.answerModel
          .findById(item)
          .exec()
          .then((result) => {
            total += result.score;
          }),
      );
    });
    await Promise.all(taskQueue);

    const newResult = new this.resultModel({
      score: total,
      user_id: dto.userId,
      answers: dto.answers,
    });

    return newResult.save();
  }
}
