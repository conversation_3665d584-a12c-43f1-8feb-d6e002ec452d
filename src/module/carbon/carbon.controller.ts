import { CarbonResultService } from "./carbon-result.service";
import { CreateResultDto } from "./dto/create-result.dto";
import { Body, Controller, Get, Param, Patch, Post } from "@nestjs/common";
import { <PERSON>piBody, ApiTags } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";
import { CarbonAnswerService } from "./carbon-answer.service";
import { CarbonQuizService } from "./carbon-quiz.service";
import { CreateAnswerDto } from "./dto/create-answer.dto";
import { CreateQuizDto } from "./dto/create-quiz.dto";
import { UpdateQuizDto } from "./dto/update-quiz.dto";

@ApiTags("Carbon Footprint")
@Controller("carbon")
// TODO : resources
export class CarbonController {
  constructor(
    private readonly answerService: CarbonAnswerService,
    private readonly quizService: CarbonQuizService,
    private readonly resultService: CarbonResultService,
  ) {}

  @Get("questions")
  @Public()
  getQuestions() {
    return this.quizService.findAll();
  }

  @Post("result")
  @Public()
  createResult(@Body() payload: CreateResultDto) {
    return this.resultService.create(payload);
  }

  @Get("score/:id")
  @Public()
  getMyScore(@Param("id") userId: string) {
    return this.resultService.get(userId);
  }

  @Post("admin/answer")
  @Public()
  @ApiBody({ type: CreateAnswerDto, description: "Create answers" })
  addQuestions(@Body() payload: CreateAnswerDto) {
    return this.answerService.create(payload);
  }

  @Post("admin/quiz")
  @Public()
  @ApiBody({ type: CreateQuizDto, description: "Create questions" })
  addQuiz(@Body() payload: CreateQuizDto) {
    return this.quizService.create(payload);
  }

  @Patch("admin/quiz")
  @Public()
  @ApiBody({ type: UpdateQuizDto, description: "Update questions" })
  updateQuiz(@Body() payload: UpdateQuizDto) {
    return this.quizService.update(payload);
  }
}
