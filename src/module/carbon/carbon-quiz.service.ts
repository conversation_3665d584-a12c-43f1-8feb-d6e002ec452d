import { UpdateQuizDto } from "./dto/update-quiz.dto";
import { CreateQuizDto } from "./dto/create-quiz.dto";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model } from "mongoose";
import { CarbonQuiz, CarbonQuizDocument } from "./schema/carbon-quiz.schema";

@Injectable()
export class CarbonQuizService {
  constructor(@InjectModel("CarbonQuiz") private quizModel: Model<CarbonQuizDocument>) {}

  async create(dto: CreateQuizDto): Promise<any> {
    const newStore = new this.quizModel(dto);

    return newStore.save();
  }

  async findAll() {
    return this.quizModel.find().populate("answers").lean().exec();
  }

  async findById(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const Store = this.quizModel.findById(id).exec();

    return Store;
  }

  async update(payload: UpdateQuizDto): Promise<CarbonQuiz | null> {
    if (!mongoose.Types.ObjectId.isValid(payload.id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const update = await this.quizModel.updateOne({ _id: payload.id }, payload).exec();

    if (!update) {
      throw "Update data failed.";
    }

    return this.quizModel.findById(payload.id);
  }

  async remove(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const deleting = await this.quizModel.deleteOne({ _id: id }).exec();

    return deleting;
  }
}
