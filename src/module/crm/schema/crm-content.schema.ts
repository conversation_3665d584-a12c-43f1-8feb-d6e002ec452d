import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { ConvertPathToUrl, ConvertUrlToPath } from "src/utils/function.util";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class CRMContent extends Document {
  @Prop({ required: true, type: String })
  title: string;

  @Prop({ required: true, type: String })
  description: string;

  @Prop({ required: true, type: String, get: ConvertPathToUrl, set: ConvertUrlToPath })
  image: string;

  @Prop({ required: true, type: String })
  cta_label: string;

  @Prop({ required: true, type: String })
  url: string;

  @Prop({ required: true, type: Boolean })
  is_active: boolean;

  @Prop({ required: true, type: Number })
  position: number;

  @Prop({ required: false, type: Date, default: null })
  startDate?: Date;

  @Prop({ required: false, type: Date, default: null })
  endDate?: Date;
}

export type CRMContentDocument = CRMContent & Document;

export const CRMContentSchema = SchemaFactory.createForClass(CRMContent);

CRMContentSchema.plugin(mongoosePaginate);
