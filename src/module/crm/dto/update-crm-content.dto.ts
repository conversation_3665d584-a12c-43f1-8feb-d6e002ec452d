import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsOptional, IsN<PERSON>ber, IsString, IsUrl, IsNotEmpty, IsDate } from "class-validator";

export class UpdateCRMContentDto {
  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  image: string;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  url: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  cta_label: string;

  @ApiProperty({ type: String, required: false })
  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  is_active: boolean;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  position: number;

  @ApiProperty({
    required: true,
    description: "Start Date",
  })
  @Transform(({ value }) => value === null ? null : new Date(value))
  @IsNotEmpty()
  @IsDate()
  startDate: Date;

  @ApiProperty({
    required: true,
    description: "End Date",
  })
  @Transform(({ value }) => value === null ? null : new Date(value))
  @IsDate()
  @IsNotEmpty()
  endDate: Date;
}
