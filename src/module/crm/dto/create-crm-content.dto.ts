import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString, IsUrl } from "class-validator";
import { IsImageUrlRegistered } from "src/decorator/image-url-validator";

export class CreateCRMContentDto {
  @ApiProperty({ type: String, required: true })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ type: String, required: true })
  // @IsImageUrlRegistered({
  //     message: "unknown image url: $value",
  //   })
  @IsString()
  @IsNotEmpty()
  image: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({ type: String, required: true })
  @IsString()
  @IsNotEmpty()
  cta_label: string;

  @ApiProperty({ type: Boolean, required: true })
  @IsBoolean()
  @IsNotEmpty()
  @Type(() => Boolean)
  is_active: boolean;

  @ApiProperty({ type: Number, required: true })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  position: number;

  @ApiProperty({
    required: false,
    description: "Start Date",
  })
  @IsOptional()
  @IsDate()
  @Transform(({ value }) => value == null ? null : new Date(value))
  startDate?: Date;

  @ApiProperty({
    required: false,
    description: "End Date",
  })
  @IsDate()
  @IsOptional()
  @Transform(({ value }) => value == null ? null : new Date(value))
  endDate?: Date;
}
