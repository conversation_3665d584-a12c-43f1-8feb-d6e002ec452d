import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsOptional } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class GetManyCRMContentDto extends PaginationParamDto {
    @ApiProperty({ type: String, required: false })
    @IsOptional()
    keyword: string

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @TransformBoolean()
    status: boolean
}