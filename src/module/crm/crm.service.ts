import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { CreateCRMContentDto, GetManyCRMContentDto } from "./dto";
import { UpdateCRMContentDto } from "./dto/update-crm-content.dto";
import { CRMContent, CRMContentDocument } from "./schema";

@Injectable()
export class CrmService {
  constructor(
    @InjectModel(CRMContent.name)
    private crmContentmodel: PaginateModel<CRMContentDocument>,
  ) {}

  async create(payload: CreateCRMContentDto) {
    return await this.crmContentmodel.create(payload);
  }

  async findOne(id: string) {
    return await this.crmContentmodel.findById(id);
  }

  async findMany(queries: GetManyCRMContentDto) {
    try {
      const { status, keyword, limit = 15, page = 1, sort } = queries;

      const filter: Record<string, any> = {};
      if (![undefined, null].includes(status)) filter["is_active"] = status;
      if (keyword) {
        filter["$or"] = [{ title: { $regex: keyword, $options: "i" } }, { url: { $regex: keyword, $options: "i" } }];
      }
      
      const options = {
        page: Number(page),
        limit: Number(limit),
        forceCountFn: true,
        sort: { position: 1 },
      };

      return await this.crmContentmodel.paginate(filter, options);
    } catch (err) {
      throw err;
    }
  }

  async update(id: string, payload: UpdateCRMContentDto): Promise<any> {
    return await this.crmContentmodel.findByIdAndUpdate({ _id: id }, payload);
  }

  async delete(id: string) {
    await this.crmContentmodel.findByIdAndDelete({ _id: id });
  }

  async findActiveContents() {
    return await this.crmContentmodel.find(
      {
        is_active: true,
        $or: [
          {
            $and: [{ startDate: { $exists: false } }, { endDate: { $exists: false } }],
          },
          {
            $and: [{ startDate: null }, { endDate: null }],
          },
          {
            $and: [{ startDate: { $lte: Date.now() } }, { endDate: { $gte: Date.now() } }],
          },
        ],
      },
      {},
      { sort: { position: 1 } },
    );
  }
}
