import { Module } from '@nestjs/common';
import { CrmAdminController } from './crm-admin.controller';
import { CrmService } from './crm.service';
import { MongooseModule } from '@nestjs/mongoose';
import { CRMContent, CRMContentSchema } from "./schema"

@Module({
  controllers: [CrmAdminController],
  providers: [CrmService],
  imports: [
    MongooseModule.forFeature([
      {
        name: CRMContent.name,
        schema: CRMContentSchema,
      },
    ]),
  ],
  exports: [
    CrmService
  ]
})

export class CrmModule {}
