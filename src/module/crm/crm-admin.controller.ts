import { Body, Controller, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../enum/rbac.enum";
import { Role } from "../enum/role.enum";
import { CrmService } from "./crm.service";
import { CreateCRMContentDto, GetManyCRMContentDto, GetOneCRMContentDto } from "./dto";
import { UpdateCRMContentDto } from "./dto/update-crm-content.dto";

@ApiTags("CRM Home Config")
@ApiBearerAuth("access-token")
@Controller("crm/admin")
@Resource(Controllers.ADMIN_CRM_HOME_CONFIG)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
export class CrmAdminController {
  constructor(private readonly crmContentService: CrmService) {}

  @Post()
  @Scopes(Scope.POST)
  create(@Body() payload: CreateCRMContentDto) {
    return this.crmContentService.create(payload);
  }

  @Get("/:id")
  @Scopes(Scope.GET)
  getOne(@Param() param: GetOneCRMContentDto) {
    return this.crmContentService.findOne(param.id);
  }

  @Get()
  @Scopes(Scope.GET)
  getMany(@Query() queries: GetManyCRMContentDto) {
    return this.crmContentService.findMany(queries);
  }

  @Patch("/:id")
  @Scopes(Scope.PATCH)
  updateContent(@Body() payload: UpdateCRMContentDto, @Param() param: GetOneCRMContentDto) {
    return this.crmContentService.update(param.id, payload);
  }

  @Post("/:id")
  delete(@Param() id: string) {
    return this.crmContentService.delete(id);
  }
}
