import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
@Schema({
  timestamps: true,
})
export class Newsletter extends Document {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  name: string;
}

export type NewsletterDocument = Newsletter & Document;

export const NewsletterSchema = SchemaFactory.createForClass(Newsletter);
