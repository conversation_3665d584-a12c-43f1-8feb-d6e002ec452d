import { <PERSON>, Post, Body } from "@nestjs/common";
import { NewsletterService } from "./newsletter.service";
import { CreateNewsletterDto } from "./dto/create-newsletter.dto";
import { ApiTags } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";

@ApiTags("NewsLetter")
@Controller("newsletter")
// TODO : resources
export class NewsletterController {
  constructor(private readonly newsletterService: NewsletterService) {}

  @Post()
  @Public()
  create(@Body() createNewsletterDto: CreateNewsletterDto) {
    return this.newsletterService.create(createNewsletterDto);
  }
  // TODO - admin

  // @Get()
  // findAll() {
  //   return this.newsletterService.findAll();
  // }

  // @Get(":id")
  // findOne(@Param("id") id: string) {
  //   return this.newsletterService.findOne(+id);
  // }

  // @Patch(":id")
  // update(@Param("id") id: string, @Body() updateNewsletterDto: UpdateNewsletterDto) {
  //   return this.newsletterService.update(+id, updateNewsletterDto);
  // }

  // @Delete(":id")
  // remove(@Param("id") id: string) {
  //   return this.newsletterService.remove(+id);
  // }
}
