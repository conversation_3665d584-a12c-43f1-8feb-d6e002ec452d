import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { NewsletterService } from "./newsletter.service";
import { NewsletterController } from "./newsletter.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { NewsletterSchema } from "./schema/newsletter.schema";
import { ApmModule } from "modules/nestjs-elastic-apm";

@Module({
  controllers: [NewsletterController],
  providers: [NewsletterService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "Newsletter",
        schema: NewsletterSchema,
      },
    ]),
    ApmModule.register(),
  ],
})
export class NewsletterModule {}
