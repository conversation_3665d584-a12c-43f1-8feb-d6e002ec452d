import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { CreateNewsletterDto } from "./dto/create-newsletter.dto";
import { UpdateNewsletterDto } from "./dto/update-newsletter.dto";
import { NewsletterDocument } from "./schema/newsletter.schema";

@Injectable()
export class NewsletterService {
  constructor(@InjectModel("Newsletter") private model: Model<NewsletterDocument>) {}

  async create(payload: CreateNewsletterDto) {
    const result = await this.model.findOne({ email: payload.email });

    if (result) {
      return result;
    }
    const model = new this.model(payload);
    return model.save();
  }

  findAll() {
    return `This action returns all newsletter`;
  }

  findOne(id: number) {
    return `This action returns a #${id} newsletter`;
  }

  update(id: number, updateNewsletterDto: UpdateNewsletterDto) {
    return `This action updates a #${id} newsletter`;
  }

  remove(id: number) {
    return `This action removes a #${id} newsletter`;
  }
}
