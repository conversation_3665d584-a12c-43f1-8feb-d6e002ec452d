import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { ElasticsearchModule } from "@nestjs/elasticsearch";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { ElasticSearchService } from "./elastic-search.service";

@Module({
  imports: [
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          node: configService.get<string>("ELASTICSEARCH_URL"),
          auth: {
            username: configService.get<string>("ELASTICSEARCH_USER"),
            password: configService.get<string>("ELASTICSEARCH_PASSWORD"),
          },
          tls: { rejectUnauthorized: false },
        };
      },
    }),
  ],
  providers: [ElasticSearchService],
  exports: [ElasticSearchService],
})
export class ElasticSearchModule {}
