import { Injectable } from "@nestjs/common";
import { ElasticsearchService } from "@nestjs/elasticsearch";
import { IBulkInsert, ISearchData, IUpdateById, IUpdateData } from "./elastic-search.interface";
import { PaginateFormat } from "../../utils/function.util";
import { GetRequest } from "@elastic/elasticsearch/lib/api/types";

@Injectable()
export class ElasticSearchService {
  constructor(private readonly elasticService: ElasticsearchService) {}

  async bulkInsertDocument(params: IBulkInsert) {
    await this._validateId(params.data);
    await this._createIndexNoExist(params.index);

    const operations = params.data.flatMap((doc) => [{ index: { _index: params.index, _id: doc.id } }, doc]);

    return await this.elasticService.bulk({ refresh: true, operations });
  }

  async find(params: ISearchData) {
    const paginateResponse = params.paginateResponse;

    delete params.paginateResponse;

    const data = await this.elasticService.search(params);
    const page = params.from / params.size + 1;

    if (paginateResponse) {
      return PaginateFormat({
        data: data.hits.hits.map((item) => item._source),
        total: data.hits.total?.["value"],
        offset: params.from,
        limit: params.size,
        page,
      });
    }

    return data.hits;
  }

  async findOne(params: GetRequest): Promise<Record<string, any>> {
    const data = await this.elasticService.get(params, { ignore: [404, 400] });

    if (data.found) return data._source;

    return {};
  }

  async update(params: IUpdateData) {
    const script = this._prepareUpdateBody(params.body);
    return await this.elasticService.updateByQuery({ index: params.index, query: params.query, script });
  }

  async updateById(params: IUpdateById) {
    return await this.elasticService.update({ id: params.id, index: params.index, doc: params.data });
  }

  private async _createIndexNoExist(index: string) {
    const check = await this.elasticService.indices.exists({ index });

    if (!check) await this.elasticService.indices.create({ index });
  }

  private _prepareUpdateBody(body: Record<string, any>) {
    const source = [];
    Object.keys(body).map((key) => {
      source.push(`ctx._source.${key}=params.${key}`);
    });

    return { lang: "painless", source: source.join(";"), params: body };
  }

  private async _validateId(data: Array<Record<string, any>>) {
    let isError = false;
    for (const item of data) {
      if (!item.id) {
        isError = true;
        break;
      }
    }

    if (isError) throw Error("Id cannot be empty");

    return true;
  }
}
