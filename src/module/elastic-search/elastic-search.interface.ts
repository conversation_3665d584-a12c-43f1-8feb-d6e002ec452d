import { QueryDslQueryContainer, SearchRequest } from "@elastic/elasticsearch/lib/api/types";

export interface IBulkInsert {
  index: string;
  data: Array<Record<string, any>>;
}

export interface ISearchData extends SearchRequest {
  paginateResponse?: boolean;
}

export interface IUpdateData {
  index: string;
  body: Record<string, any>;
  query: QueryDslQueryContainer;
}

export interface IUpdateById {
  index: string;
  id: string;
  data: Record<string, any>;
}
