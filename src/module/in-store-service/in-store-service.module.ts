import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { AdminInStoreServiceController } from "./admin-in-store-service.controller";
import { InStoreServiceService } from "./in-store-service.service";
import { InStoreService, InStoreServiceSchema } from "./schema/in-store-service.schema";
import { InStoreServiceController } from "./in-store-service.controller";

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: InStoreService.name,
        schema: InStoreServiceSchema,
      },
    ]),
  ],
  controllers: [AdminInStoreServiceController, InStoreServiceController],
  providers: [InStoreServiceService],
  exports: [InStoreServiceService],
})
export class InStoreServiceModule {}
