import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth, <PERSON>pi<PERSON><PERSON> } from "@nestjs/swagger";
import { Resource, Roles, RoleMatchingMode, Scopes } from "keycloak-connect-tbs";
import { Role, Scope, Controllers } from "../enum";
import { InStoreServiceService } from "./in-store-service.service";
import { CreateInStoreServiceDto } from "./dto/create-in-store-service.dto";
import { UpdateInStoreServiceDto } from "./dto/update-in-store-service.dto";
import { FilterInStoreServiceDto } from "./dto/filter-in-store-service.dto";

@ApiTags("Admin - In Store Service")
@ApiBearerAuth("access-token")
@Controller("admin/in-store-service")
@Resource(Controllers.ADMIN_IN_STORE_SERVICE)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
export class AdminInStoreServiceController {
  constructor(private readonly inStoreServiceService: InStoreServiceService) {}

  @Post()
  @Scopes(Scope.POST)
  @ApiBody({ type: CreateInStoreServiceDto })
  create(@Body() createDto: CreateInStoreServiceDto) {
    return this.inStoreServiceService.create(createDto);
  }

  @Get()
  @Scopes(Scope.GET)
  findAll(@Query() filter: FilterInStoreServiceDto) {
    return this.inStoreServiceService.findAll(filter);
  }

  @Get(":id")
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.inStoreServiceService.findOne(id);
  }

  @Patch(":id")
  @Scopes(Scope.PATCH)
  @ApiBody({ type: UpdateInStoreServiceDto })
  update(@Param("id") id: string, @Body() updateDto: UpdateInStoreServiceDto) {
    return this.inStoreServiceService.update(id, updateDto);
  }

  @Delete(":id")
  @Scopes(Scope.DELETE)
  remove(@Param("id") id: string) {
    return this.inStoreServiceService.remove(id);
  }
}
