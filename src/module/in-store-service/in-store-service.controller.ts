import { Controller, Get, Query } from "@nestjs/common";
import { ApiTags, ApiOperation } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";
import { InStoreServiceService } from "./in-store-service.service";
import { FilterInStoreServiceDto } from "./dto/filter-in-store-service.dto";

@ApiTags("In Store Service")
@Public()
@Controller("in-store-service")
export class InStoreServiceController {
  constructor(private readonly inStoreServiceService: InStoreServiceService) {}

  @Get()
  @ApiOperation({
    summary: "Get all in-store services",
    description: "Retrieve all in-store services",
  })
  findAll(@Query() filter: FilterInStoreServiceDto) {
    return this.inStoreServiceService.findAll(filter);
  }
}
