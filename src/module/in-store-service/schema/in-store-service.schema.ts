import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { ConvertPathToUrl, ConvertUrlToPath } from "src/utils/function.util";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class InStoreService extends Document {
  @Prop({ required: true, type: String })
  name: string;

  @Prop({ required: false, type: String })
  description: string;

  @Prop({ required: false, type: String, get: ConvertPathToUrl, set: ConvertUrlToPath })
  image: string;
}

export type InStoreServiceDocument = InStoreService & Document;

export const InStoreServiceSchema = SchemaFactory.createForClass(InStoreService);

InStoreServiceSchema.plugin(mongoosePaginate);
