import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { InStoreService, InStoreServiceDocument } from "./schema/in-store-service.schema";
import { CreateInStoreServiceDto } from "./dto/create-in-store-service.dto";
import { UpdateInStoreServiceDto } from "./dto/update-in-store-service.dto";
import { FilterInStoreServiceDto } from "./dto/filter-in-store-service.dto";

@Injectable()
export class InStoreServiceService {
  constructor(
    @InjectModel(InStoreService.name)
    private inStoreServiceModel: PaginateModel<InStoreServiceDocument>,
  ) {}

  async create(createDto: CreateInStoreServiceDto) {
    const inStoreService = new this.inStoreServiceModel(createDto);
    return inStoreService.save();
  }

  async findAll(filter: FilterInStoreServiceDto) {
    const { page = 1, limit = 10, keyword } = filter;

    const query: any = {};
    if (keyword) {
      query.$or = [{ name: { $regex: keyword, $options: "i" } }, { description: { $regex: keyword, $options: "i" } }];
    }

    return this.inStoreServiceModel.paginate(query, {
      page,
      limit,
      sort: { createdAt: -1 },
    });
  }

  async findOne(id: string) {
    const inStoreService = await this.inStoreServiceModel.findById(id);
    if (!inStoreService) {
      throw new NotFoundException(`In-store service with ID ${id} not found`);
    }
    return inStoreService;
  }

  async update(id: string, updateDto: UpdateInStoreServiceDto) {
    const inStoreService = await this.findOne(id);

    return this.inStoreServiceModel.findByIdAndUpdate(id, updateDto, { new: true });
  }

  async remove(id: string) {
    const inStoreService = await this.findOne(id);

    return this.inStoreServiceModel.findByIdAndDelete(id);
  }
}
