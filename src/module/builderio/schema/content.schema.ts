import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class Content extends Document {
  @Prop({ required: true, type: String })
  content_id: string;

  @Prop({ required: false, type: String })
  thumbnail: string;

  @Prop({
    required: false,
    type: String,
    get: (value: string) => {
      const words = value.split(" ");
      const upperCaseWords = words.map((word) => {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      });

      return upperCaseWords.join(" ");
    },
  })
  title: string;

  @Prop({ required: false, type: String })
  description: string;

  @Prop({ required: false, type: String })
  url: string;

  @Prop({ required: false, type: String })
  published: string;

  @Prop({ required: false, type: String })
  model: string;

  @Prop({ required: false, type: Array })
  tags: Array<any>;

  @Prop({ required: false, type: Date })
  createdDate: Date;

  @Prop({ required: false, type: Date })
  updatedDate: Date;

  @Prop({ required: false, type: Number })
  position: number;

  @Prop({ required: false, type: Boolean })
  is_visible: boolean
}

export type ContentDocument = Content & Document;

export const ContentSchema = SchemaFactory.createForClass(Content);

ContentSchema.plugin(mongoosePaginate);
