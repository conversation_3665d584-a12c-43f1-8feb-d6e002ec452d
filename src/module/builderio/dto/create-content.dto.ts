import { ArrayMinSize, IsArray, IsBoolean, IsDate, IsNumber, IsOptional, IsString } from "class-validator";
import { TransformArray } from "../../../decorator/transform-array.decorator";
import { Transform } from "class-transformer";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class CreateContentDto {
  @IsString()
  content_id: string;

  @IsString()
  @IsOptional()
  thumbnail: string;

  @IsString()
  @IsOptional()
  title: string;

  @IsString()
  @IsOptional()
  description: string;

  @IsString()
  @IsOptional()
  url: string;

  @IsString()
  @IsOptional()
  published: string;

  @IsString()
  @IsOptional()
  model: string;

  @IsArray()
  @IsOptional()
  @TransformArray()
  @ArrayMinSize(1)
  tags: Array<string>;

  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  createdDate: Date;

  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  updatedDate: Date;

  @IsOptional()
  @IsNumber()
  position: number;

  @IsOptional()
  @IsBoolean()
  @TransformBoolean()
  is_visible: boolean
}
