import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsBooleanString, ValidateIf, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { ModelEnum } from "../enum/model-enum";
import { PublishedEnum } from "../enum/published-enum";

export class GetContentDto extends PaginationParamDto {
  @ApiProperty({
    required: false,
    description: "Published Status",
    enum: PublishedEnum,
  })
  @ValidateIf((obj) => Object.values(PublishedEnum).includes(obj.type))
  @IsNotEmpty()
  @IsBooleanString()
  published: PublishedEnum;

  @ApiProperty({
    required: false,
    description: "Model",
    enum: ModelEnum,
  })
  @ValidateIf((obj) => Object.values(ModelEnum).includes(obj.type))
  @IsNotEmpty()
  @IsEnum(ModelEnum)
  model: ModelEnum;

  @ApiProperty({
    required: false,
    description: "Content tags. Comma separated",
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({
    required: false,
    description: "keyword for searching title or url-key",
  })
  @IsOptional()
  @IsString()
  keyword?: string;
}

export class GetContentPublicDto extends PaginationParamDto {
  // @ApiProperty({
  //   required: false,
  //   description: "Model",
  //   enum: ModelEnum,
  // })
  // @ValidateIf((obj) => Object.values(ModelEnum).includes(obj.type))
  // @IsNotEmpty()
  // @IsEnum(ModelEnum)
  // model: ModelEnum;

  @ApiProperty({
    required: false,
    description: "Content tags. Comma separated",
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({
    required: false,
    description: "Keyword",
  })
  @IsOptional()
  @IsString()
  keyword?: string;
}
