import { Body, Controller, Get, Param, Patch, Query } from "@nestjs/common"; //main lib
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger"; //swagger required
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs"; //acl
import { Role } from "../enum/role.enum";
import { <PERSON><PERSON>, Scope } from "../enum/rbac.enum";
import { BuilderIoService } from "./builderio.service";
import { GetContentDto } from "./dto/get-content.dto";
import { UpdateContentVisibilityDto } from "./dto/update-content-visibility.dto";

@ApiTags("Admin - Builder Io") //module name
@Controller("admin/builderio")
@ApiBearerAuth("access-token") //swagger
@Resource(Controllers.ADMIN_BUILDERIO)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
export class AdminBuilderIoController {
  constructor(private readonly builderIoService: BuilderIoService) {}

  /**
   * get list of content
   */
  @Get()
  @Scopes(Scope.GET)
  find(@Query() pagination: GetContentDto) {
    return this.builderIoService.findAllAdmin(pagination);
  }

  @Patch("/:id")
  @Scopes(Scope.PATCH)
  updateVisibility(@Param("id") id: string, @Body() payload: UpdateContentVisibilityDto) {
    return this.builderIoService.updateContentVisibility(id, payload)
  }
}
