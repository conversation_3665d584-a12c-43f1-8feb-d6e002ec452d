import { HttpService } from "@nestjs/axios";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel, ProjectionType } from "mongoose";
import { lastValueFrom, map } from "rxjs";
import { GetContentPathDto } from "./dto/get-content-path.dto";
import { GetContentDto, GetContentPublicDto } from "./dto/get-content.dto";
import { ModelEnum } from "./enum/model-enum";
import { ContentDocument } from "./schema/content.schema";
import { UpdateContentVisibilityDto } from "./dto/update-content-visibility.dto";

@Injectable()
export class BuilderIoService {
  constructor(
    @InjectModel("Content") private contentModel: PaginateModel<ContentDocument>,
    private readonly httpService: HttpService,
  ) {}

  private fromTime = 0;

  /**
   * sync builder io model
   */
  async syncBuilderIoModel(lastnhour?: number) {
    console.time("- sync builder io");
    this.fromTime = 0;
    if (lastnhour > 0) {
      this.fromTime = this._getUnixtimestamp(lastnhour);
    }

    const models = Object.values(ModelEnum);
    for (let index = 0; index < models.length; index++) {
      try {
        const res = await this._syncModelContents(models[index]);
      } catch (err) {
        console.error(`error synch builderio: ${err}`);
      }
    }
    console.timeEnd("- sync builder io");
  }

  /**
   * get unix timestamp
   * @param {number} lastnhour
   */
  private _getUnixtimestamp(lastnhour?: number) {
    if (!lastnhour || lastnhour <= 0) {
      return 0;
    }
    const date = new Date();
    date.setHours(date.getHours() - lastnhour + date.getTimezoneOffset() / 60);
    return date.getTime();
  }

  /**
   * sync model contents to mongo
   * @param {String} model
   */
  private async _syncModelContents(model: string) {
    return new Promise(async (resolve) => {
      let inc = 0;
      let page = true;
      let totalContent = 0;
      do {
        const result = await this._getBuilderIoContent(model, inc);
        if (!result || !result.results.length) {
          page = false;
          break;
        }

        const documents = [];
        for (const doc of result.results) {
          const { blocks, ..._doc } = doc;
          if (_doc.data.thumbnail) {
            documents.push(_doc);
            continue;
          }
          const newThumbnail = this._findFirstImage(doc);
          _doc.data.thumbnail = newThumbnail;
          documents.push(_doc);
        }

        totalContent += documents.length;
        await this._createContent(model, documents);

        inc += Number(process.env.BUILDERIO_CONTENT_LIMIT);
      } while (page);
      resolve(`Sync model ${model} done, imported ${totalContent}`);
    });
  }

  /**
   * create content or update
   * @param {String}     model
   * @param {Array<any>} result
   */
  private async _createContent(model: string, result: Array<any>) {
    result.forEach(async (item) => {
      await this._execute(model, item);
    });
  }

  /**
   * exec query
   * @param {string} model
   * @param {Object} item
   */
  private async _execute(
    model: string,
    item: {
      id: any;
      data: { thumbnail: string; title: string; description: string; url: string; tags: any[]; position: number };
      published: string;
      lastUpdated: string | number | Date;
      createdDate: string | number | Date;
    },
  ) {
    const currentContent = await this.contentModel.findOne({ content_id: item.id });
    if (currentContent) {
      currentContent.published = item.published || "";
      if (item.data) {
        currentContent.thumbnail = item.data.thumbnail || "";
        currentContent.title = item.data.title || "";
        currentContent.description = item.data.description || "";
        currentContent.url = item.data.url || "";
        currentContent.tags = item.data.tags || [];
        currentContent.position = item.data.position || 0;
      }
      if (item.lastUpdated) {
        currentContent.updatedDate = new Date(item.lastUpdated);
      }
      await currentContent.save();
      return;
    }

    const content = {
      model: model[0].toUpperCase() + model.slice(1),
      content_id: item.id,
      published: item.published || "",
      createdDate: new Date(item.createdDate),
      updatedDate: item.lastUpdated ? new Date(item.lastUpdated) : new Date(),
    };

    if (item.data) {
      content["thumbnail"] = item.data.thumbnail || "";
      content["title"] = item.data.title || "";
      content["url"] = item.data.url || "";
      content["tags"] = item.data.tags || [];
      content["position"] = item.data.position || 0;
    }
    const newScreen = new this.contentModel(content);
    return newScreen.save();
  }

  /**
   * get content from builder io api
   * @param {String} model
   * @param {Number} offset
   */
  private async _getBuilderIoContent(model: string, offset: number) {
    try {
      const fields = `id,createdBy,data.thumbnail,data.title,data.description,data.tags,data.url,data.position,published,createdDate,lastUpdated,data.blocks`;
      let url = `${process.env.BUILDERIO_API_URL}/content/${model}?apiKey=${process.env.BUILDERIO_PUBLIC_API}&userAttributes&limit=${process.env.BUILDERIO_CONTENT_LIMIT}&offset=${offset}&fields=${fields}&omit&includeUnpublished=true&includeRefs&cacheSeconds=0&sort`;

      if (this.fromTime > 0) {
        url +=
          "&query.$or[0]={createdDate:{$gte:" +
          this.fromTime +
          "}}&query.$or[1]={lastUpdated:{$gte:" +
          this.fromTime +
          "}}";
      }

      const responseData = await lastValueFrom(
        this.httpService.get(url).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData;
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  /**
   * get list screen for admin
   * @param {GetContentDto} params
   */
  async findAllAdmin(params: GetContentDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort, keyword } = params;

    if (keyword) {
      filter["$or"] = [
        {title: {$regex: keyword, $options: "i"}},
        {url: {$regex: keyword, $options: "i"}}
      ]
    }
    if (params.model) {
      filter.model = params.model[0].toUpperCase() + params.model.slice(1);
    }
    if (params.published) {
      filter.published = params.published;
    }

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    const result = await this.contentModel.paginate(filter, options);

    return result;
  }

  /**
   * get list screen for public
   * @param {GetContentPublicDto} params
   */
  async findAllPublished(params: GetContentPublicDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort } = params;

    // if (params.model) {
    //   filter.model = params.model[0].toUpperCase() + params.model.slice(1);
    // }

    const arrFilter = [];
    if (params.tags) {
      // filter.tags = new RegExp(params.tags.replace(",", "|"), "i");
      arrFilter.push({ tags: new RegExp(params.tags.replace(",", "|"), "i") });
    }

    if (params.keyword) {
      arrFilter.push({ title: new RegExp(params.keyword, "gi") });
    }

    if (arrFilter.length > 0) {
      filter.$and = arrFilter;
    }

    filter.published = "published";
    filter.model = { $in: ["Blog", "Page"] };
    filter.is_visible = { $ne: false };
    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
    };

    const result = await this.contentModel.paginate(filter, options);

    return result;
  }

  async findByPath(param: GetContentPathDto) {
    let path = param.path;
    if (path[0] != "/") {
      path = "/" + path;
    }
    const filter = { url: path, published: "published" };
    const result = await this.contentModel.findOne(filter);
    if (!result) {
      throw new HttpException(`data not found`, HttpStatus.NOT_FOUND);
    }

    return result;
  }

  async findTags() {
    return await this.contentModel.find().distinct("tags");
  }

  async findUrlKeyPublished() {
    try {
      const filter = { published: "published" };
      const project: ProjectionType<ContentDocument> = { url: 1, _id: 0 };
      return await this.contentModel.find(filter, project).lean();
    } catch (error) {
      throw new HttpException(error, HttpStatus.BAD_REQUEST);
    }
  }

  async updateContentVisibility(id: string, payload: UpdateContentVisibilityDto) {
    return await this.contentModel.findByIdAndUpdate(id, payload, {new: true})
  }

  _findFirstImage(blocks: Array<any>) {
    const blockString = JSON.stringify(blocks);
    const imageBlocks = blockString.split(`"image":"`);
    if (imageBlocks.length < 2) return "";

    const [firstImg] = imageBlocks[1].split(`"`);
    return firstImg;
  }
}
