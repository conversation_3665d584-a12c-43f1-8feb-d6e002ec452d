import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { BuilderIoController } from "./builderio.controller";
import { BuilderIoService } from "./builderio.service";
import { ContentSchema } from "./schema/content.schema";
import { HttpModule } from "@nestjs/axios";
import { AdminBuilderIoController } from "./admin-builderio.controller";

@Module({
  controllers: [AdminBuilderIoController, BuilderIoController],
  providers: [BuilderIoService],
  exports: [BuilderIoService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "Content",
        schema: ContentSchema,
      },
    ]),
    HttpModule,
  ],
})
export class BuilderIoModule {}
