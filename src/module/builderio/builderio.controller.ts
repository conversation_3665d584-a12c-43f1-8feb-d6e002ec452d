import { Controller, Get, Query } from "@nestjs/common"; //main lib
import { EventEmitter2 } from "@nestjs/event-emitter";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger"; //swagger required
import { InternalAccess, Public } from "keycloak-connect-tbs"; //acl
import { BuilderIoService } from "./builderio.service";
import { GetContentPathDto } from "./dto/get-content-path.dto";
import { GetContentPublicDto } from "./dto/get-content.dto";
import { GetLastNHourDto } from "./dto/get-last-n-hour.dto";
@ApiTags("Builder Io") //module name
@ApiBearerAuth("access-token") //swagger
@Controller("builderio")
export class BuilderIoController {
  constructor(private readonly builderIoService: BuilderIoService, private readonly eventEmitter: EventEmitter2) {}

  /**
   * sync content from builder io to mongo
   */
  @Get("/sync/")
  @Public()
  sync(@Query() params: GetLastNHourDto) {
    return this.builderIoService.syncBuilderIoModel(params.lastnhour);
  }

  /**
   * sync content from mongo
   */
  @Get("/content/")
  @Public()
  find(@Query() pagination: GetContentPublicDto) {
    return this.builderIoService.findAllPublished(pagination);
  }

  /**
   * sync content from mongo
   */
  @Get("/tags/")
  @Public()
  findTags() {
    return this.builderIoService.findTags();
  }

  @Get("/search-path")
  @Public()
  @InternalAccess()
  findPath(@Query() param: GetContentPathDto) {
    return this.builderIoService.findByPath(param);
  }

  @Get("/url-key/published")
  @Public()
  @InternalAccess()
  findUrlKeyPublished() {
    return this.builderIoService.findUrlKeyPublished();
  }
}
