import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { ShowForEnum } from "../enum/kibana-dashboard.enum";

@Schema({
  timestamps: true,
})
export class KibanaDashboard extends Document {
  @Prop({ required: true, type: String })
  url: string;

  @Prop({ required: true, type: String })
  title: string;

  @Prop({ required: true, type: String, enum: ShowForEnum })
  show_for: ShowForEnum;
}

export type KibanaDashboardDocument = KibanaDashboard & Document;

export const KibanaDashboardSchema = SchemaFactory.createForClass(KibanaDashboard);
