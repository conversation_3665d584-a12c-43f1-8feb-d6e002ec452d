import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { KibanaDashboard, KibanaDashboardSchema } from "./schema/kibana-dashboard.schema";
import { KibanaDashboardController } from "./kibana-dashboard.controller";
import { KibanaDashboardService } from "./kibana-dashboard.service";
import { KibanaDashboardPosController } from "./kibana-dashboard-pos.controller";

@Module({
  controllers: [KibanaDashboardController, KibanaDashboardPosController],
  providers: [KibanaDashboardService],
  imports: [
    MongooseModule.forFeature([
      {
        name: KibanaDashboard.name,
        schema: KibanaDashboardSchema,
      },
    ]),
  ],
})
export class KibanaDashboardModule {}
