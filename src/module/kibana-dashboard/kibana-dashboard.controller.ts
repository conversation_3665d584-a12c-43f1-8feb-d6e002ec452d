import { Body, Controller, Delete, Get, Param, Patch, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiTags } from "@nestjs/swagger";
import { RoleMatchingMode, Roles } from "keycloak-connect-tbs";
import { Role } from "../enum/role.enum";
import { KibanaDashboardService } from "./kibana-dashboard.service";
import { CreateKibanaDashboardDto } from "./dto/create-kibana-dashboard.dto";
import { EditKibanaDashBoardDto } from "./dto/edit-kibana-dashboard.dto";

@ApiTags("Kibana Dashboard")
@ApiBearerAuth("access-token")
@Roles({
  roles: [Role.Admin, `realm:app-${Role.Admin}`],
  mode: RoleMatchingMode.ANY,
})
@Controller("kibana-dashboard")
export class KibanaDashboardController {
  constructor(private readonly kibanaDashboardService: KibanaDashboardService) {}

  @Post("/")
  @ApiBody({ type: CreateKibanaDashboardDto, description: "Create Kibana Dashboard" })
  create(@Body() payload: CreateKibanaDashboardDto) {
    return this.kibanaDashboardService.create(payload);
  }

  @Get("/cms")
  async getDashboardCms() {
    return this.kibanaDashboardService.getDashboardCms();
  }

  @Get("/")
  async get() {
    return this.kibanaDashboardService.getAll();
  }

  @Get("/:id")
  async getDashboardByid(@Param("id") kibanaDashboardId: string) {
    return this.kibanaDashboardService.getDashboardById(kibanaDashboardId);
  }

  @Patch("/:id")
  async editDashboardDetail(@Body() payload: EditKibanaDashBoardDto, @Param("id") kibanaDashboardId: string) {
    return this.kibanaDashboardService.editDashboard(payload, kibanaDashboardId);
  }

  @Delete("/:id")
  async deleteDashboard(@Param("id") kibanaDashboardId: string) {
    return this.kibanaDashboardService.deleteDashbaord(kibanaDashboardId);
  }
}
