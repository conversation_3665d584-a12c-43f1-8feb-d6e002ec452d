import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { KibanaDashboardDocument } from "./schema/kibana-dashboard.schema";
import { Body, Injectable, Param } from "@nestjs/common";
import { CreateKibanaDashboardDto } from "./dto/create-kibana-dashboard.dto";
import { ShowForEnum } from "./enum/kibana-dashboard.enum";
import { EditKibanaDashBoardDto } from "./dto/edit-kibana-dashboard.dto";

@Injectable()
export class KibanaDashboardService {
  constructor(@InjectModel("KibanaDashboard") private kibanaDashboardModel: Model<KibanaDashboardDocument>) {}

  async create(payload: CreateKibanaDashboardDto) {
    return await this.kibanaDashboardModel.create(payload);
  }

  async getAll() {
    return await this.kibanaDashboardModel.find();
  }

  async getDashboardPos() {
    return await this.kibanaDashboardModel.find({ show_for: { $in: [ShowForEnum.All, ShowForEnum.POS] } });
  }

  async getDashboardCms() {
    return await this.kibanaDashboardModel.find({ show_for: { $in: [ShowForEnum.All, ShowForEnum.CMS] } });
  }

  async editDashboard(@Body() payload: EditKibanaDashBoardDto, @Param() kibanaDashboardId: string) {
    return await this.kibanaDashboardModel.findByIdAndUpdate(kibanaDashboardId, payload, { new: true });
  }

  async getDashboardById(@Param() kibanaDashboardId: string) {
    return await this.kibanaDashboardModel.findById(kibanaDashboardId);
  }

  async deleteDashbaord(@Param() kibanaDashboardId: string) {
    return await this.kibanaDashboardModel.findByIdAndDelete(kibanaDashboardId);
  }
}
