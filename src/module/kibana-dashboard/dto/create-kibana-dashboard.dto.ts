import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsMongoId, IsString } from "class-validator";
import { ShowForEnum } from "../enum/kibana-dashboard.enum";

export class CreateKibanaDashboardDto {
  @ApiProperty({
    type: String,
    required: true,
    example:
      "https://kibana-view.thebodyshop.co.id/app/dashboards#/view/48cbeec0-bcf4-11ed-9694-fde9a9bb4941?embed=true&_g=(filters%3A!()%2CrefreshInterval%3A(pause%3A!f%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-30d%2Fd%2Cto%3Anow))&show-top-menu=true&show-query-input=true&show-time-filter=true",
  })
  @IsString()
  url: string;

  @ApiProperty({ type: String, required: true, example: "Weekly Report" })
  @IsString()
  title: string;

  @ApiProperty({
    description: "Enum For Kibana Dashboard",
    enum: ShowForEnum,
    required: true,
  })
  @IsEnum(ShowForEnum)
  show_for: ShowForEnum;
}
