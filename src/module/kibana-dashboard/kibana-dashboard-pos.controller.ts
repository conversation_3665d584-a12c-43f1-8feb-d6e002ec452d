import { Controller, Get } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { RoleMatchingMode, Roles } from "keycloak-connect-tbs";
import { Role } from "../enum/role.enum";
import { KibanaDashboardService } from "./kibana-dashboard.service";

@ApiTags("Kibana Dashboard - POS")
@ApiBearerAuth("access-token")
@Roles({
  roles: [Role.Manager, `realm:app-${Role.Manager}`, Role.Operator, `realm:app-${Role.Operator}`],
  mode: RoleMatchingMode.ANY,
})
@Controller("kibana-dashboard-pos")
export class KibanaDashboardPosController {
  constructor(private readonly kibanaDashboardService: KibanaDashboardService) {}

  @Get("/")
  async getDashboardPos() {
    return this.kibanaDashboardService.getDashboardPos();
  }
}
