import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
@Schema({
  timestamps: true,
})
export class Home extends Document {
  @Prop()
  id: number;

  @Prop()
  created: Date;

  @Prop()
  max_size: number;

  @Prop()
  modified: Date;

  @Prop()
  param_id: number;

  @Prop()
  position: number;

  @Prop()
  section_type: string;

  @Prop()
  source_url: string;

  @Prop()
  status: string;

  @Prop()
  title: string;

  @Prop()
  android_max_version: string;

  @Prop()
  android_min_version: string;

  @Prop()
  ios_max_version: string;

  @Prop()
  ios_min_version: string;

  @Prop()
  platform: string;

  @Prop()
  section: string;

  @Prop()
  text_color: string;

  @Prop()
  bg_color: string;

  @Prop()
  end_date: Date;
}

export type HomeDocument = Home & Document;

export const HomeSchema = SchemaFactory.createForClass(Home);
