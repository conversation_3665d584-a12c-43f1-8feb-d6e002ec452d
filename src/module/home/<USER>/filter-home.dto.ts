import { ApiProperty } from "@nestjs/swagger";
import { HomePlatformEnum, PlatformEnum } from "../enum/home-platform.enum";
import { HomeSectionEnum } from "../enum/home-section.enum";

export class FilterHomeDto {
  @ApiProperty({ required: false, enum: HomePlatformEnum })
  platform: string;

  @ApiProperty({ required: false, enum: HomeSectionEnum })
  section: string;
}

export class FilterHomeV2Dto {
  @ApiProperty({ required: false, enum: PlatformEnum })
  platform: string;
}
