import { Controller, Get, Query, Request } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>piHeader, ApiTags } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";
import { ChannelHeader } from "../enum";
import { FilterHomeDto, FilterHomeV2Dto } from "./dto/filter-home.dto";
import { PurgeEnum } from "./enum/purge.enum";
import { HomeService } from "./home.service";

@ApiTags("Home")
@Controller("home")
// TODO : resources
export class HomeController {
  constructor(private readonly homeService: HomeService) {}

  @Get()
  @Public()
  findAll(@Query() payload: FilterHomeDto) {
    return this.homeService.findAll(payload);
  }

  @Get("v2")
  @Public()
  @ApiBearerAuth("access-token")
  @ApiHeader({ name: "purge", enum: PurgeEnum })
  @ApiHeader({ name: "channel", enum: ChannelHeader, required: true })
  fetch(@Request() req, @Query() payload: FilterHomeV2Dto) {
    const purge = req.headers.purge || PurgeEnum.keep;
    return this.homeService.getHome(payload, purge, req);
  }

  // @Post()
  // create(@Body() createHomeDto: CreateHomeDto) {
  //   return this.homeService.create(createHomeDto);
  // }

  // @Get(":id")
  // findOne(@Param("id") id: string) {
  //   return this.homeService.findOne(+id);
  // }

  // @Patch(":id")
  // update(@Param("id") id: string, @Body() updateHomeDto: UpdateHomeDto) {
  //   return this.homeService.update(+id, updateHomeDto);
  // }

  // @Delete(":id")
  // remove(@Param("id") id: string) {
  //   return this.homeService.remove(+id);
  // }
}
