import { InjectRedis } from "@liaoliaots/nestjs-redis";
import { HttpService } from "@nestjs/axios";
import { HttpException, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import Redis from "ioredis";
import { Model } from "mongoose";
import { lastValueFrom, map } from "rxjs";
import { BannerFooterService } from "../banner-footer/banner-footer.service";
import { BlogCollectionService } from "../blog/blog-collection.service";
import { BuilderIoService } from "../builderio/builderio.service";
import { CarouselService } from "../carousel/carousel.service";
import { CrmService } from "../crm/crm.service";
import { ProductGroupTag } from "../enum/product-group-tag.enum";
import { MegaMenuDocument } from "../mega-menu/schema/mega-menu.schema";
import { UsersService } from "../microservices/users/users.service";
import { SiteConfigService } from "../site-configs/site-config.service";
import { FilterHomeDto, FilterHomeV2Dto } from "./dto/filter-home.dto";
import { HomePlatformEnum, PlatformEnum } from "./enum/home-platform.enum";
import { PurgeEnum } from "./enum/purge.enum";
import { HomeDocument } from "./schema/home.schema";

@Injectable()
export class HomeService {
  constructor(
    @InjectModel("Home") private model: Model<HomeDocument>,
    @InjectModel("MegaMenu") private megaMenuModel: Model<MegaMenuDocument>,
    private readonly contentService: BuilderIoService,
    private readonly carouselService: CarouselService,
    private readonly httpService: HttpService,
    private readonly blogCollectionService: BlogCollectionService,
    @InjectRedis()
    private readonly redisService: Redis,
    private readonly usersService: UsersService,
    private configService: ConfigService,
    private bannerFooterService: BannerFooterService,
    private crmService: CrmService,
    private readonly siteConfigService: SiteConfigService,
  ) {}

  async findAll(payload: FilterHomeDto) {
    return this.model
      .find({
        status: "Active",
        section: payload.section,
        platform: { $in: [payload.platform, HomePlatformEnum.All] },
      })
      .sort({ position: 1 })
      .lean()
      .exec();
  }

  /**
   * get home content
   * @param {FilterHomeV2Dto} payload
   */
  async getHome(payload: FilterHomeV2Dto, purgeOption, req: any) {
    //get from redis
    const user = await this.getUserInfo(req, purgeOption, false);
    const tier = !user || user?.is_public ? "PUBLIC" : user.customerGroup;

    const cacheKey = `home/${tier}/${payload.platform}`;
    if (purgeOption == PurgeEnum.keep) {
      const home = await this.redisService.get(cacheKey);
      if (home) return JSON.parse(home);
    }
    //get from db
    const homeContent = await this._getLayout(payload.platform, req);

    const res = Object.keys(homeContent);
    const f = [];
    let prod = {};
    res.map((i) => {
      if (i != "products") {
        if (i === "range") {
          homeContent[i].see_all =
            process.env.PRODUCT_SERVICE_URL +
            `/api/v1/product-group?page=1&limit=10&tag=${ProductGroupTag.Range}&is_lean=false`;
        }
        if (i === "skin_type") {
          homeContent[i].see_all =
            process.env.PRODUCT_SERVICE_URL +
            `/api/v1/product-group?page=1&limit=10&tag=${ProductGroupTag.SkinType}&is_lean=false`;
        }
        f.push(homeContent[i]);
      } else {
        prod = homeContent[i];
      }
    });

    const banner_footer = await this._getBannerFooter();

    const response = { docs: f, products: prod, banner_footer };

    await this.redisService.set(cacheKey, JSON.stringify(response), "EX", process.env.REDIS_HOME_CACHED_LIFETIME);

    return response;
  }

  private async _getBannerFooter() {
    return await this.bannerFooterService.getBannerForHome();
  }

  /**
   * get layout for home
   * @param {string} platform
   */
  private async _getLayout(platform, req) {
    const homeLayoutWeb = await this.siteConfigService.findByKey("homeConfig.web");
    const homeLayoutMobile = await this.siteConfigService.findByKey("homeConfig.mobile");
    const layout = JSON.parse(platform == PlatformEnum.Web ? homeLayoutWeb.value : homeLayoutMobile.value);
    const storeCode = platform == PlatformEnum.Web ? 34999 : 34997;
    const keys = Object.keys(layout);
    const result = {};
    let group = {};

    return new Promise(async (resolve) => {
      const collectionType = [];
      for (const section of keys) {
        result[section] = {
          type: section,
          title: layout[section],
          content: await this._getContent(section, storeCode, req),
        };
      }

      if (collectionType.length) {
        group = await this._getProductCollection(layout, storeCode, req);
      }

      resolve({ ...result, ...group });
    });
  }

  /**
   * get content for layout
   * @param {string} layout
   */
  private async _getContent(layout, storeCode, req) {
    switch (layout) {
      case "carousel":
        return await this.carouselService.findAllPublic();
      case "blog":
        return await this.getBlogContents();
      case "promo":
      case "offer":
        return await this._getProductCollection(layout, storeCode, req);
      case "range":
        return await this._getAllRange(storeCode, req, ProductGroupTag.Range);
      case "skin_type":
        return await this._getAllRange(storeCode, req, ProductGroupTag.SkinType);
      case "crm":
        return await this._getCRMContents();
      default:
        // this.collectionType.push(layout);
        break;
    }
  }

  /**
   * get builderio content
   */
  private async _getBuilderIoContent() {
    const result = await this.contentService.findAllPublished({ tags: "home", limit: 3 });
    return result.docs;
  }

  private async getBlogContents() {
    return await this.blogCollectionService.getActiveBlogs();
  }

  /**
   * get collection of product groups
   * @param {string} layout
   */
  private async _getProductCollection(layout, storeCode, req) {
    try {
      const url = `${this.configService.get(
        "OMS_HOST",
      )}/api/v1/product-collection/full?type=${layout}&storeCode=${storeCode}`;

      const result = {};
      const headers = req.headers;
      const responseData: any[] = await lastValueFrom(
        this.httpService.get(url, { headers }).pipe(map((response) => response.data.data)),
      );

      // for (const property in responseData) {
      //   // if (property == "products") {
      //   //   result[property] = responseData[property];
      //   //   continue;
      //   // }
      //   // result[property] = { type: property, title: layout[property], content: responseData[property] };
      // }

      return responseData;
    } catch (e) {
      console.error(e);
    }
  }

  // private async _getOffersCollection(layout, storeCode, req) {
  //   try {
  //     const url = process.env.OMS_HOST + "/api/v1/product-collection/full?type=offers" + "&storeCode=" + storeCode;
  //     const result = {};
  //     const responseData: any[] = await lastValueFrom(
  //       this.httpService.get(url, { headers }).pipe(map((response) => response.data.data)),
  //     );

  //     return responseData;
  //   } catch (e) {
  //     console.error(e);
  //   }
  // }

  private async _getAllRange(storeCode, req, type) {
    const host = this.configService.get<string>("OMS_HOST");
    const url = `${host}/api/v1/product-group?page=1&limit=10&tag=${type}&is_lean=true`;

    try {
      const requestUrl = url;
      const requestConfig = {
        headers: {
          Authorization: req.headers.authorization,
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData.data.docs;
    } catch (e) {
      console.error(e);
      throw new HttpException(e, e.response.status);
    }
  }

  private async _getCRMContents() {
    return await this.crmService.findActiveContents();
  }

  _getUserId(token) {
    const base64Payload = token.split(".")[1];
    const payload = Buffer.from(base64Payload, "base64");
    return JSON.parse(payload.toString()).uid;
  }

  async getUserInfo(req, purge: PurgeEnum, isAdmin?: boolean): Promise<any> {
    if (!req?.headers?.authorization) return { is_public: true };
    if (isAdmin) return { is_public: true };

    const userid = this._getUserId(req?.headers?.authorization);
    const cacheKey = `products/users/${userid}`;
    if (purge == PurgeEnum.keep) {
      const userDetails = await this.redisService.get(cacheKey);
      if (userDetails) return JSON.parse(userDetails);
    }

    const userData = await this.usersService.getProfile(req, isAdmin);

    await this.redisService.set(
      cacheKey,
      JSON.stringify(userData.data),
      "EX",
      process.env.REDIS_URL_CHECKER_CACHED_LIFETIME,
    );

    return userData.data;
  }
}
