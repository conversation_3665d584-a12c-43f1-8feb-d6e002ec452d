import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { BuilderIoModule } from "../builderio/builderio.module";
import { CarouselModule } from "../carousel/carousel.module";
import { HomeController } from "./home.controller";
import { HomeService } from "./home.service";
import { HomeSchema } from "./schema/home.schema";
//import { RedisModule } from "@nestjs-modules/ioredis";
import { RedisModule } from "@liaoliaots/nestjs-redis";
import { ConfigModule } from "@nestjs/config";
import { BannerFooterModule } from "../banner-footer/banner-footer.module";
import { BlogModule } from "../blog/blog.module";
import { CrmModule } from "../crm/crm.module";
import { MegaMenuSchema } from "../mega-menu/schema/mega-menu.schema";
import { UsersModule } from "../microservices/users/users.module";
import { SiteConfigsModule } from "../site-configs/site-configs.module";
@Module({
  controllers: [HomeController],
  providers: [HomeService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "Home",
        schema: HomeSchema,
      },
      {
        name: "MegaMenu",
        schema: MegaMenuSchema,
      },
    ]),
    ApmModule.register(),
    CarouselModule,
    HttpModule,
    BuilderIoModule,
    RedisModule.forRoot({ config: { url: process.env.REDIS_HOST + ":" + process.env.REDIS_PORT_PUBLIC } }),
    UsersModule,
    ConfigModule,
    BannerFooterModule,
    BlogModule,
    CrmModule,
    SiteConfigsModule,
  ],
})
export class HomeModule {}
