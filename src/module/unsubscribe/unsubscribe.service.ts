import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { CreateUnsubscribeDto, EncryptEmailDto } from "./dto/create-unsubscribe.dto";
import mongoose, { PaginateModel } from "mongoose";
import { UnsubscribeDocument } from "./schema/unsubscribe.schema";
import * as crypto from "crypto";
import { lastValueFrom, map } from "rxjs";
import { HttpService } from "@nestjs/axios";

@Injectable()
export class UnsubscribeService {
  constructor(
    @InjectModel("Unsubscribe")
    private unsubscribeModel: PaginateModel<UnsubscribeDocument>,
    private readonly httpService: HttpService
  ) { }

  /**
   * create Unsubscribe
   * @param {CreateUnsubscribeDto} createUnsubscribeDto
   * @param {File} file
   */
  async createUnsubscriber(createUnsubscribeDto: CreateUnsubscribeDto) {
    const tokenPart = createUnsubscribeDto.token.split(":");
    const email = this._decrypt(tokenPart[0], tokenPart[1]);

    const valid = this._isValidEmail(email);
    if (!valid) {
      return;
    }

    const exist = await this._isExist(email);

    if (exist) {
      return exist;
    }

    const payload = {
      email: email,
    };

    await this._unsubscribeSendPortal(payload);

    const newUnsubscribe = new this.unsubscribeModel({ email: email });
    newUnsubscribe.save();
    return true;
  }

  /**
   * encryot text
   * @param {EncryptEmailDto} encryptEmailDto
   */
  async encryptText(encryptEmailDto: EncryptEmailDto) {
    const encrypted = this._encrypt(encryptEmailDto.email);
    return { result: encrypted.iv + ":" + encrypted.encryptedData, id: encryptEmailDto.id };
  }

  /**
   * check if email exist
   * @param {string} email
   */
  private async _isExist(email) {
    const unsuscriber = await this.unsubscribeModel.findOne({ email: email });
    return unsuscriber;
  }

  /**
   * is email valid
   * @param {string} email
   */
  private _isValidEmail(email) {
    return String(email)
      .toLowerCase()
      .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      );
  }

  /**
   * encrypt
   * @param {string} plaintext
   */
  private _encrypt(plaintext: string) {
    const key = Buffer.from(process.env.AES_SALT);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv("aes-256-cbc", key, iv);

    let encrypted = cipher.update(plaintext);
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    return { iv: iv.toString("hex"), encryptedData: encrypted.toString("hex") };
  }

  /**
   * decrypt
   * @param {string} salt
   * @param {string} hashedText
   */
  private _decrypt(salt, hashedText) {
    const iv = Buffer.from(salt, "hex");
    const encryptedText = Buffer.from(hashedText, "hex");
    const key = Buffer.from(process.env.AES_SALT);
    const decipher = crypto.createDecipheriv("aes-256-cbc", key, iv);

    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);

    return decrypted.toString();
  }

  private async _unsubscribeSendPortal(params) {
    try {
      const url = `${process.env.SENDPORTAL_URL}/subscribers/unsubscribers-with-email`;
    
      const requestUrl = url;
      const requestConfig = {
        headers: {
          Authorization: `Bearer ${process.env.SENDPORTAL_TOKEN}`,
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.put(requestUrl, params ,requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    
      return responseData;
    } catch (e) {
      console.error(e);
    }
  }
}
