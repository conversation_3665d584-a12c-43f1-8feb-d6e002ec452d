import { ApiProperty } from "@nestjs/swagger";
import { IsEmail } from "class-validator";

export class CreateUnsubscribeDto {
  @ApiProperty({
    description: "token",
    required: true,
  })
  token: string;
}

export class EncryptEmailDto {
  @ApiProperty({
    description: "email address",
    required: true,
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: "identifier",
    required: false,
  })
  id: string;
}
