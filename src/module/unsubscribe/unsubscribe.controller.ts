import { Controller, Get, HttpCode, HttpStatus, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";
import { CreateUnsubscribeDto, EncryptEmailDto } from "./dto/create-unsubscribe.dto";
import { UnsubscribeService } from "./unsubscribe.service";

@ApiTags("Unsubscribe")
@ApiBearerAuth("access-token")
@Controller("unsubscribe")
// TODO : resources
export class UnsubscribeController {
  constructor(private readonly unsubscribeService: UnsubscribeService) {}

  @Get("encrypt")
  @Public()
  @HttpCode(HttpStatus.OK)
  encrypt(@Query() params: EncryptEmailDto) {
    return this.unsubscribeService.encryptText(params);
  }

  @Get()
  @Public()
  @HttpCode(HttpStatus.OK)
  unsubscribe(@Query() params: CreateUnsubscribeDto) {
    return this.unsubscribeService.createUnsubscriber(params);
  }
}
