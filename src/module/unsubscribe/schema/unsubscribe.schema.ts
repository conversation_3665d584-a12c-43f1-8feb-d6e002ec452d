import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose, { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class Unsubscribe extends Document {
  @Prop({ required: true, type: String })
  email: string;
}

export type UnsubscribeDocument = Unsubscribe & Document;

export const UnsubscribeSchema = SchemaFactory.createForClass(Unsubscribe);

UnsubscribeSchema.plugin(mongoosePaginate);
