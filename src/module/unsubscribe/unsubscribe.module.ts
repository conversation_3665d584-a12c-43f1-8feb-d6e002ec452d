import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { UnsubscribeController } from "./unsubscribe.controller";
import { UnsubscribeService } from "./unsubscribe.service";
import { UnsubscribeSchema } from "./schema/unsubscribe.schema";
import { HttpModule } from "@nestjs/axios";

@Module({
  controllers: [UnsubscribeController],
  providers: [UnsubscribeService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "Unsubscribe",
        schema: UnsubscribeSchema,
      },
    ]),
    HttpModule,
  ],
})
export class UnsubscribeModule {}
