import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";

@Schema()
export class Keycloak {
  @Prop({ required: true, index: true })
  keycloak_id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true, index: true })
  type: string;
}

export type KeycloakDocument = Keycloak & Document;

export const KeycloakSchema = SchemaFactory.createForClass(Keycloak);
