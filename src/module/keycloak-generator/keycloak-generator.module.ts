import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { Keycloak, KeycloakSchema } from "./schema/keycloak-generator.schema";
import { KeycloakGeneratorController } from "./keycloak-generator.controller";
import { KeycloakGeneratorService } from "./keycloak-generator.service";
import { KeycloakModule } from "../microservices/keycloak/keycloak.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Keycloak.name,
        schema: KeycloakSchema,
      },
    ]),
    KeycloakModule,
    ApmModule.register(),
  ],
  controllers: [KeycloakGeneratorController],
  providers: [KeycloakGeneratorService],
  exports: [KeycloakGeneratorService],
})
export class KeycloakGeneratorModule {}
