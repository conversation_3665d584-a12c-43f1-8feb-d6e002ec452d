import { KeycloakGeneratorService } from "./keycloak-generator.service";
import { KeycloakGeneratorFindAllDto } from "./dto/keycloak-generator-find-all.dto";
import { KeycloakGeneratorCreateDto } from "./dto/keycloak-generator-create.dto";
import { Controller } from "@nestjs/common";

@Controller("keycloak-generator")
export class KeycloakGeneratorController {
  constructor(private readonly keycloakService: KeycloakGeneratorService) {}

  async generate(paths: Array<Record<string, any>>) {
    try {
      return await this.keycloakService.generate(paths);
    } catch (err) {
      throw err;
    }
  }

  async findAll(data: KeycloakGeneratorFindAllDto) {
    try {
      return await this.keycloakService.findAll(data);
    } catch (err) {
      throw err;
    }
  }

  async create(data: KeycloakGeneratorCreateDto) {
    try {
      return await this.keycloakService.create(data);
    } catch (err) {
      throw err;
    }
  }
}
