import { InjectModel } from "@nestjs/mongoose";
import { Keycloak, KeycloakDocument } from "./schema/keycloak-generator.schema";
import { Model } from "mongoose";
import { KeycloakGeneratorFindAllDto } from "./dto/keycloak-generator-find-all.dto";
import { KeycloakGeneratorCreateDto } from "./dto/keycloak-generator-create.dto";
import { KeycloakMSService } from "../microservices/keycloak/keycloak.service";
import * as _ from "lodash";
import { Logger } from "@nestjs/common";

export class KeycloakGeneratorService {
  constructor(
    @InjectModel(Keycloak.name) private readonly keycloak: Model<KeycloakDocument>,
    private readonly keycloakMs: KeycloakMSService,
  ) {}

  private readonly logger = new Logger("Keycloak Generator");

  async generate(paths: Array<Record<string, any>>) {
    try {
      const predefinePolicy = process.env.KEYCLOAK_PREDEFINE_POLICY;
      const resources: Record<string, any> = {};
      const permissions: Record<string, any> = {};
      const hashResource: Record<string, any> = {};
      let hashPermissions: Record<string, any> = {};

      const clientDetail = await this.keycloakMs.getDetailClient();

      if (!clientDetail) {
        this.logger.error("Client not found");
      }

      const [dbResource, keycloakResource, dbPermission, keycloakPermission, keycloakScope, keycloakPolicy] =
        await Promise.all([
          this.findAll({ type: "resource" }),
          this.keycloakMs.getAllResources(),
          this.findAll({ type: "permission" }),
          this.keycloakMs.getAllPermissions(clientDetail.id),
          this.keycloakMs.getAllScopes(clientDetail.id),
          this.keycloakMs.getAllPolicy(clientDetail.id),
        ]);

      await Promise.all(keycloakPermission.map((item) => (hashPermissions[item.id] = item)));

      const resourceDiff = _.difference(
        keycloakResource,
        dbResource.map((data) => data.keycloak_id),
      );

      const permissionDiff = _.difference(
        keycloakPermission.map((data) => data.id),
        dbPermission.map((data) => data.keycloak_id),
      );

      if (resourceDiff.length) {
        await Promise.all(
          resourceDiff.map(async (diff) => {
            const detail = await this.keycloakMs.getDetailResources(diff);
            const create = await this.create({
              keycloak_id: detail._id,
              type: "resource",
              name: detail.name,
            });
            dbResource.push(create);
          }),
        );
      }

      if (permissionDiff.length) {
        await Promise.all(
          permissionDiff.map(async (diff) => {
            const detail = hashPermissions[diff];
            const create = await this.create({
              keycloak_id: detail.id,
              type: "permission",
              name: detail.name,
            });
            dbPermission.push(create);
          }),
        );
      }

      hashPermissions = {};

      dbResource.map((data) => (hashResource[data.name] = data));
      dbPermission.map((data) => (hashPermissions[data.name] = data));

      const scope = keycloakScope.map((item) => item.name);
      const adminPolicy = [];

      keycloakPolicy.map((data) => {
        if (predefinePolicy.split(",").includes(data.name)) {
          adminPolicy.push(data.id);
        }
      });

      await Promise.all(
        paths.map((path) => {
          Object.keys(path).map((key) => {
            let controller = path[key].operationId.split("_")[0];
            controller = controller.replace("Controller", "");
            controller = controller.replace(/\.?([A-Z]+)/g, (x, y) => "-" + y.toLowerCase()).replace(/^-/, "");

            controller = this.keycloakMs.prefixRes + controller;
            resources[controller] = { name: controller, scope };

            for (let i = 0; i < keycloakScope.length; i++) {
              let name = controller.replace(this.keycloakMs.prefixRes, "");
              name = this.keycloakMs.prefixPerm + name + "-" + keycloakScope[i]?.name;
              permissions[name] = {
                name: name,
                description: "Permission for " + controller + " with scope " + keycloakScope[i]?.name,
                type: "scope",
                decision_strategy: 0,
                logic: 0,
                client_id: clientDetail.id,
                policies: adminPolicy,
                resources: controller,
                scopes: [keycloakScope[i]?.id],
              };
            }
          });
        }),
      );

      const diffResKeycloak = _.difference(Object.keys(resources), Object.keys(hashResource));
      const diffPermKeycloak = _.difference(Object.keys(permissions), Object.keys(hashPermissions));

      const limit = 15;
      const batchRes = Math.ceil(diffResKeycloak.length / limit);
      for (let i = 0; i < batchRes; i++) {
        const res = diffResKeycloak.splice(0, limit);
        await Promise.all(
          res.map(async (key) => {
            const data = resources[key];
            const res = await this.keycloakMs.createResources(data.name, data.scope);
            hashResource[data.name] = await this.create({
              keycloak_id: res._id,
              type: "resource",
              name: data.name,
            });
          }),
        );
      }

      const batchPerm = Math.ceil(diffPermKeycloak.length / limit);
      for (let i = 0; i < batchPerm; i++) {
        const perm = diffPermKeycloak.splice(0, limit);
        await Promise.all(
          perm.map(async (key) => {
            const data = permissions[key];
            const resource = hashResource[data.resources];
            data.resources = [resource.keycloak_id];

            const res = await this.keycloakMs.createPermissions(data);
            hashResource[data.name] = await this.create({
              keycloak_id: res.id,
              type: "permission",
              name: data.name,
            });
          }),
        );
      }
      return;
    } catch (err) {
      this.logger.error("Error with detail " + err.message);
      throw err;
    }
  }

  async findAll(params: KeycloakGeneratorFindAllDto) {
    const filter: Record<string, any> = {};

    if (params.type) filter.type = params.type;
    if (params.keycloak_id) filter.keycloak_id = params.keycloak_id;
    if (params.name) filter.name = { $regex: new RegExp(params.name, "i") };

    return this.keycloak.find(filter).lean();
  }

  async create(data: KeycloakGeneratorCreateDto) {
    return this.keycloak.create(data);
  }
}
