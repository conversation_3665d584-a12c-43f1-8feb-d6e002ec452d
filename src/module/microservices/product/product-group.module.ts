/*
https://docs.nestjs.com/modules
*/

import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { ProductGroupMSService } from "./product-group.service";

@Module({
  imports: [HttpModule, ConfigModule, ApmModule.register()],
  controllers: [],
  providers: [ProductGroupMSService],
  exports: [ProductGroupMSService],
})
export class ProductGroupModule {}
