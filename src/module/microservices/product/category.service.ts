import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { lastValueFrom, map } from "rxjs";

@Injectable()
export class CategoryMSService {
  constructor(private readonly httpService: HttpService, private readonly configService: ConfigService) {}

  private readonly host = this.configService.get<string>("PRODUCT_SERVICE_URL");

  async getAllResources(id: string) {
    try {
      const url = this.host + `/api/v1/category/multiple/${id}`;
      return await lastValueFrom(
        this.httpService.get(url).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
}
