import { Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { WebBuilderService } from "src/module/microservices/web-builder/web-builder.service";

@Injectable()
export class WebBuilderListener {
  constructor(private readonly webBuilderService: WebBuilderService) {}

  @OnEvent("rebuild-home")
  async rebuildHome() {
    console.log("rebuild home");
    await this.webBuilderService.rebuild("/home");
  }
}
