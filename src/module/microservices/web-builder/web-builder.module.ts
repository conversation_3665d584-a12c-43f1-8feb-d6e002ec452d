/*
https://docs.nestjs.com/modules
*/

import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { WebBuilderListener } from "./listener/web-builder.listener";
import { WebBuilderService } from "./web-builder.service";

@Module({
  imports: [HttpModule, ConfigModule, ApmModule.register()],
  controllers: [],
  providers: [WebBuilderService, WebBuilderListener],
  exports: [WebBuilderService, WebBuilderListener],
})
export class WebBuilderModule {}
