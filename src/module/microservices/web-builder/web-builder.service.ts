import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { lastValueFrom, map } from "rxjs";

@Injectable()
export class WebBuilderService {
  constructor(private readonly httpService: HttpService, private readonly configService: ConfigService) {}
  private readonly host = this.configService.get<string>("WEB_BUILDER_URL");

  async rebuild(key: string) {
    try {
      const url = this.host + key;
      return await lastValueFrom(
        this.httpService.get(url).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
}
