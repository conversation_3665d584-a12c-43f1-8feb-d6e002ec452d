/*
https://docs.nestjs.com/modules
*/

import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { PaymentService } from "./payment.service";

@Module({
  imports: [HttpModule, ConfigModule, ApmModule.register()],
  controllers: [],
  providers: [PaymentService],
  exports: [PaymentService],
})
export class PaymentModule {}
