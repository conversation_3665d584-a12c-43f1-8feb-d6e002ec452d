/*
https://docs.nestjs.com/providers#services
*/

import { HttpService } from "@nestjs/axios";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { lastValueFrom, map } from "rxjs";

@Injectable()
export class PaymentService {
  constructor(private readonly httpService: HttpService) {}

  private readonly paymentHost = process.env.PAYMENT_SERVICE_URL;

  async verifyPayment(paymentCode, channel: string) {
    try {
      const requestUrl = this.paymentHost + `/api/v1/payment-method/verify?amount=10000&paymentCode=${paymentCode}`;
      const requestConfig = {
        headers: {
          channel: channel,
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.post(requestUrl, requestConfig).pipe(
          map((response) => {
            return response.data.data;
          }),
        ),
      );
      return responseData;
    } catch (e) {
      throw new HttpException("Payment method not found", HttpStatus.BAD_REQUEST);
    }
  }
}
