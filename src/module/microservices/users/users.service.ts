/*
https://docs.nestjs.com/providers#services
*/

import { HttpService } from "@nestjs/axios";
import { HttpException, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { lastValueFrom, map } from "rxjs";

@Injectable()
export class UsersService {
  constructor(private readonly httpService: HttpService, private configService: ConfigService) {}

  async getProfile(req, isAdmin?: boolean) {
    const host = this.configService.get<string>("USERS_SERVICE_URL");
    try {
      const requestUrl = `${host}/api/v1/${isAdmin ? "admin" : "user"}/profile`;
      const requestConfig = {
        headers: {
          Authorization: req.headers.authorization,
        },
      };
      const responseData = await lastValueFrom(
        this.httpService.get(requestUrl, requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
      return responseData;
    } catch (e) {
      throw new HttpException(e, e.response.status);
    }
  }
}
