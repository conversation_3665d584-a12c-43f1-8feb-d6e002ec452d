/*
https://docs.nestjs.com/modules
*/

import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { UsersService } from "./users.service";

@Module({
  imports: [HttpModule, ConfigModule, ApmModule.register()],
  controllers: [],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
