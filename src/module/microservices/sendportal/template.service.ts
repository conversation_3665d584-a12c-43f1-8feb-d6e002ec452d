import { Injectable } from "@nestjs/common";
import { SendPortalService } from "./sendportal.service";
import { lastValueFrom, map } from "rxjs";
import { HttpService } from "@nestjs/axios";

@Injectable()
export class TemplateService {
  constructor(private readonly sendPortalService: SendPortalService, private readonly httpService: HttpService) {}
  private host = this.sendPortalService.sendPortalHost + "/templates";
  private requestConfig = {
    headers: { Authorization: this.sendPortalService.token },
  };

  async getTemplates(keyword: string) {
    try {
      const url = this.host + keyword;
      return await lastValueFrom(
        this.httpService.get(url, this.requestConfig).pipe(
          map((response) => {
            return response.data.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async getOneTemplate(id: number) {
    try {
      const url = this.host + `/${id}`;
      return await lastValueFrom(
        this.httpService.get(url, this.requestConfig).pipe(
          map((response) => {
            return response.data.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
}
