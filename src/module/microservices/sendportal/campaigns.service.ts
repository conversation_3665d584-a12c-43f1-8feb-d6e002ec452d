import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { SendPortalService } from "./sendportal.service";
import { lastValueFrom, map } from "rxjs";
import { ICreateCampaign } from "./interfaces/create-campaign.interface";
import { ICampaignRecipients } from "./interfaces/create-campaign-recipients.interface";

@Injectable()
export class CampaignsService {
  constructor(private readonly sendPortalService: SendPortalService, private readonly httpService: HttpService) {}
  private host = this.sendPortalService.sendPortalHost + "/campaigns";
  private requestConfig = {
    headers: { Authorization: this.sendPortalService.token },
  };

  async getCampaigns() {
    try {
      const url = this.host;
      return await lastValueFrom(
        this.httpService.get(url, this.requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async getOneCampaign(id: number) {
    try {
      const url = this.host + `/${id}`;
      return await lastValueFrom(
        this.httpService.get(url, this.requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async createCampaign(payload: ICreateCampaign) {
    try {
      const url = this.host;
      return await lastValueFrom(
        this.httpService.post(url, payload, this.requestConfig).pipe(
          map((response) => {
            return response.data.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err.message);
      throw err;
    }
  }

  async updateCampaign(id: number, payload: ICreateCampaign) {
    try {
      const url = this.host + `/${id}`;
      return await lastValueFrom(
        this.httpService.put(url, payload, this.requestConfig).pipe(
          map((response) => {
            return response.data.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err.message);
      throw err;
    }
  }

  async setCampaignRecipients(campaignId: number, payload: ICampaignRecipients) {
    try {
      const url = this.host + `/${campaignId}/setCardNumbers`;
      return await lastValueFrom(
        this.httpService.post(url, payload, this.requestConfig).pipe(
          map((response) => {
            return response.data.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async sendCampaign(campaignId: number) {
    try {
      const url = this.host + `/${campaignId}/send`;
      return await lastValueFrom(
        this.httpService.post(url, this.requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async resetCampaign(campaignId: number) {
    try {
      const url = this.host + `/${campaignId}/resetToDraft`;
      return await lastValueFrom(
        this.httpService.get(url, this.requestConfig).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      throw err;
    }
  }
}
