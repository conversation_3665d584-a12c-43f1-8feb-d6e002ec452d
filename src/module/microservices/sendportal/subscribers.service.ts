import { Injectable } from "@nestjs/common";
import { SendPortalService } from "./sendportal.service";
import { HttpService } from "@nestjs/axios";
import { lastValueFrom, map } from "rxjs";

@Injectable()
export class SubscribersService {
  constructor(private readonly sendPortalService: SendPortalService, private readonly httpService: HttpService) {}
  private host = this.sendPortalService.sendPortalHost + "/subscribers";
  private token = this.sendPortalService.token;

  async getSubscribers() {
    try {
      const url = this.host;
      return await lastValueFrom(
        this.httpService.get(url, { headers: { Authorization: this.token } }).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async getOneSubscriber(id: number) {
    try {
      const url = this.host + `/${id}`;
      return await lastValue<PERSON>rom(
        this.httpService.get(url, { headers: { Authorization: this.token } }).pipe(
          map((response) => {
            return response.data;
          }),
        ),
      );
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
}
