import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { SendPortalService } from "./sendportal.service";
import { TemplateService } from "./template.service";
import { CampaignsService } from "./campaigns.service";
import { TagsService } from "./tags.service";
import { SubscribersService } from "./subscribers.service";

@Module({
  imports: [HttpModule, ConfigModule, ApmModule.register()],
  controllers: [],
  providers: [SendPortalService, TemplateService, CampaignsService, TagsService, SubscribersService],
  exports: [SendPortalService, TemplateService, CampaignsService, TagsService, SubscribersService],
})
export class SendPortalModule {}
