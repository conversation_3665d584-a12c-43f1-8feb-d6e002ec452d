import { Controller, Get, Post, Body, Query, Param, Patch, Delete, Put } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { BlogCollectionService } from "./blog-collection.service";
import { CreateBlogCollectionDto, GetOneBlogCollectionDto, UpdateBlogCollectionDto } from "./dto";
import { <PERSON>s, Scope, Role } from "../enum";
import { PaginationParamDto } from "src/common/pagination-param.dto";

@ApiTags("Admin - Blog Collection")
@Controller("blog-collection")
@ApiBearerAuth("access-token") //swagger
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
@Resource(Controllers.BLOG_COLLECTION)
export class BlogCollectionController {
  constructor(private readonly blogCollectionService: BlogCollectionService) {}

  @Post()
  @Scopes(Scope.POST)
  async create(@Body() payload: CreateBlogCollectionDto) {
    return await this.blogCollectionService.create(payload);
  }

  @Get(":id")
  @Scopes(Scope.GET)
  async findOne(@Param() params: GetOneBlogCollectionDto) {
    return await this.blogCollectionService.getOne(params.id);
  }

  @Get()
  @Scopes(Scope.GET)
  async findMany(@Query() queries: PaginationParamDto) {
    return await this.blogCollectionService.getMany(queries);
  }

  @Put(":id")
  @Scopes(Scope.PUT)
  async update(@Param() param: GetOneBlogCollectionDto, @Body() payload: CreateBlogCollectionDto) {
    return await this.blogCollectionService.update(param.id, payload);
  }

  @Delete(":id")
  @Scopes(Scope.DELETE)
  async delete(@Param() params: GetOneBlogCollectionDto) {
    return await this.blogCollectionService.delete(params.id);
  }
}
