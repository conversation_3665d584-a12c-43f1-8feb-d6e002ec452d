import { ApiProperty } from "@nestjs/swagger";
import { PaginationParamDto } from "src/common/pagination-param.dto";

export class GetBlogDto extends PaginationParamDto {
  @ApiProperty({ required: false, type: Number })
  category_id: number;

  @ApiProperty({ required: false, type: Number })
  tag_id: number;

  @ApiProperty({ required: false, type: String })
  tag_key: string;

  @ApiProperty({ required: false, type: String })
  category_key: string;

  @ApiProperty({ required: false, type: String })
  keyword: string;
}
