import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsMongoId, IsNotEmpty, IsNumber, IsOptional } from "class-validator";
import { TransformBoolean } from "src/decorator/transform-boolean.decorator";

export class CreateBlogCollectionDto {
  @ApiProperty({ required: true, type: String })
  @IsMongoId()
  content_id?: string;

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @Type(() => Number)
  position?: number;

  @ApiProperty({ required: false, type: Boolean })
  @IsBoolean()
  @TransformBoolean()
  status?: boolean;

  @ApiProperty({
    required: false,
    description: "End Date",
    default: null
  })
  @IsDate()
  @IsOptional()
  @Transform(({ value }) => value == null ? null : new Date(value))
  startDate?: Date;

  @ApiProperty({
    required: false,
    description: "End Date",
    default: null
  })
  @IsDate()
  @IsOptional()
  @Transform(({ value }) => value == null ? null : new Date(value))
  endDate?: Date;
}
