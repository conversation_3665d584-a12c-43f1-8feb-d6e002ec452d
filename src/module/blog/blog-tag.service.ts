import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { UpdateBlogTagDto } from "./dto/update-blog-tag.dto";
import { BlogTagDocument } from "./schema/blog-tag.schema";
import { BlogDocument } from "./schema/blog.schema";

@Injectable()
export class BlogTagService {
  constructor(
    @InjectModel("BlogTag") private modelTag: Model<BlogTagDocument>,
    @InjectModel("Blog") private modelBlog: Model<BlogDocument>,
  ) {}

  async findAll() {
    return this.modelTag.find().lean();
  }

  async updateTags(payload: UpdateBlogTagDto) {
    const blog = await this.modelBlog.findOne({ post_id: payload.post_id });
    if (!blog) {
      throw new HttpException("Blog not found", HttpStatus.NOT_FOUND);
    }

    const tag = await await this.modelTag.findOne({ tag_id: payload.tag_id });
    if (!tag) {
      throw new HttpException("Tag not found", HttpStatus.NOT_FOUND);
    }

    let tagContainsBlog = false;
    let blogContainsTag = false;
    blog.tags?.forEach((item) => {
      if (item.toString() === tag._id.toString()) {
        blogContainsTag = true;
      }
    });

    tag.posts.forEach((item) => {
      if (item.toString() === blog._id.toString()) {
        tagContainsBlog = true;
      }
    });

    // Check if unique
    if (!blogContainsTag) {
      await this.modelBlog.updateOne({ _id: blog._id }, { $push: { tags: tag._id } }).exec();
    }

    if (!tagContainsBlog) {
      await this.modelTag.updateOne({ _id: tag._id }, { $push: { posts: blog._id } }).exec();
    }
    return "Update success";
  }

  async generateDummy() {
    const items = ["5,1"];

    items.forEach((item) => {
      const post_id = item.split(",")[0];
      const tag_id = item.split(",")[1];
      this.updateTags({ post_id: Number(post_id), tag_id: Number(tag_id) });
    });
  }
}
