import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";

export type BlogCategoryDocument = BlogCategory & Document;

@Schema({
  timestamps: true,
})
@Injectable()
export class BlogCategory {
  @Prop()
  category_id: number;

  @Prop()
  name: string;

  @Prop()
  status: number;

  @Prop()
  sort_order: number;

  @Prop()
  meta_title: string;

  @Prop()
  meta_tags: string;

  @Prop()
  meta_description: string;

  @Prop()
  url_key: string;

  @Prop([{ type: mongoose.Schema.Types.ObjectId, ref: "Blog" }])
  posts: mongoose.Schema.Types.ObjectId[];
}

export const BlogCategorySchema = SchemaFactory.createForClass(BlogCategory);
