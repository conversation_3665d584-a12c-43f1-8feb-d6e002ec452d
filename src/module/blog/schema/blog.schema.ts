import { Injectable } from "@nestjs/common";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";

export type BlogDocument = Blog & Document;

@Schema({
  timestamps: true,
})
@Injectable()
export class Blog {
  @Prop()
  post_id: number;

  @Prop()
  status: number;

  @Prop()
  title: string;

  @Prop()
  url_key: string;

  @Prop()
  use_comments: number;

  @Prop()
  short_content: string;

  @Prop()
  full_content: string;

  @Prop()
  meta_title: string;

  @Prop()
  meta_tags: string;

  @Prop()
  meta_description: string;

  @Prop()
  created_at: string;

  @Prop()
  updated_at: string;

  @Prop()
  published_at: string;

  @Prop()
  user_define_publish: number;

  @Prop()
  notify_on_enable: number;

  @Prop()
  display_short_content: number;

  @Prop()
  comments_enabled: number;

  @Prop()
  views: number;

  @Prop()
  post_thumbnail: string;

  @Prop()
  list_thumbnail: string;

  @Prop()
  thumbnail_url: string;

  @Prop()
  grid_class: string;

  @Prop()
  post_thumbnail_alt: string;

  @Prop()
  list_thumbnail_alt: string;

  @Prop()
  product: string;

  @Prop()
  related_post_ids: string;

  @Prop()
  is_featured: number;

  @Prop()
  tag: string;

  @Prop([{ type: mongoose.Schema.Types.ObjectId, ref: "BlogTag" }])
  tags: mongoose.Schema.Types.ObjectId[];

  @Prop([{ type: mongoose.Schema.Types.ObjectId, ref: "BlogCategory" }])
  categories: mongoose.Schema.Types.ObjectId[];
}

export const BlogSchema = SchemaFactory.createForClass(Blog);

BlogSchema.plugin(mongoosePaginate);
