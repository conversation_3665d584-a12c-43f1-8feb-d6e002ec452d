import { Injectable } from "@nestjs/common";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { Content } from "src/module/builderio/schema/content.schema";

@Schema({
  timestamps: true,
})
@Injectable()
export class BlogCollection {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: Content.name,
  })
  content_id: Types.ObjectId;

  @Prop({
    required: true,
    type: Number,
  })
  position: string;

  @Prop({
    required: false,
    default: false,
  })
  status: boolean;

  @Prop({ required: false, type: Date, default: null })
  startDate?: Date;

  @Prop({ required: false, type: Date, default: null })
  endDate?: Date;
}

export type BlogCollectionDocument = BlogCollection & Document;
export const BlogCollectionSchema = SchemaFactory.createForClass(BlogCollection);

BlogCollectionSchema.plugin(mongoosePaginate);
