import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";

export type BlogTagDocument = BlogTag & Document;

@Schema({
  timestamps: true,
})
@Injectable()
export class BlogTag {
  @Prop()
  tag_id: number;

  @Prop()
  url_key: string;

  @Prop()
  name: string;

  @Prop()
  meta_title: string;

  @Prop()
  meta_tags: string;

  @Prop()
  meta_description: string;

  @Prop([{ type: mongoose.Schema.Types.ObjectId, ref: "Blog" }])
  posts: mongoose.Schema.Types.ObjectId[];
}

export const BlogTagSchema = SchemaFactory.createForClass(BlogTag);
