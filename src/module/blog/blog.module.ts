import { BlogCategoryService } from "./blog-category.service";
import { BlogTagService } from "./blog-tag.service";
import { Module } from "@nestjs/common";
import { BlogService } from "./blog.service";
import { BlogController } from "./blog.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { BlogSchema, BlogCategorySchema, BlogCollectionSchema, BlogTagSchema } from "./schema";
import { Blog, BlogCategory, BlogTag, BlogCollection } from "./schema";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { BlogCollectionService } from "./blog-collection.service";
import { BlogCollectionController } from "./blog-collection.controller";
import { SiteConfigsModule } from "../site-configs/site-configs.module";
import { WebBuilderModule } from "../microservices/web-builder/web-builder.module";

@Module({
  controllers: [BlogController, BlogCollectionController],
  providers: [BlogService, BlogTagService, BlogCategoryService, BlogCollectionService],
  imports: [
    MongooseModule.forFeature([
      {
        name: Blog.name,
        schema: BlogSchema,
      },
      {
        name: BlogCategory.name,
        schema: BlogCategorySchema,
      },
      {
        name: BlogTag.name,
        schema: BlogTagSchema,
      },
      {
        name: BlogCollection.name,
        schema: BlogCollectionSchema,
      },
    ]),
    ApmModule.register(),
    SiteConfigsModule,
    WebBuilderModule,
  ],
  exports: [BlogCollectionService],
})
export class BlogModule {}
