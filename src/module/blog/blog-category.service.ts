import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { UpdateBlogCategoryDto } from "./dto/update-blog-category.dto";
import { BlogCategoryDocument } from "./schema/blog-category.schema";
import { BlogDocument } from "./schema/blog.schema";

@Injectable()
export class BlogCategoryService {
  constructor(
    @InjectModel("BlogCategory") private modelCategory: Model<BlogCategoryDocument>,
    @InjectModel("Blog") private modelBlog: Model<BlogDocument>,
  ) {}

  async findAll() {
    return this.modelCategory.find({ status: 1 }, { posts: 0 }).lean();
  }

  async updateCategories(payload: UpdateBlogCategoryDto) {
    const blog = await this.modelBlog.findOne({ post_id: payload.post_id });
    if (!blog) {
      throw new HttpException("Blog not found", HttpStatus.NOT_FOUND);
    }

    const category = await this.modelCategory.findOne({ category_id: payload.category_id });
    if (!category) {
      throw new HttpException("Category not found", HttpStatus.NOT_FOUND);
    }

    let categoryContainsBlog = false;
    let blogContainsCategory = false;
    blog.categories?.forEach((item) => {
      if (item.toString() === category._id.toString()) {
        blogContainsCategory = true;
      }
    });

    category.posts.forEach((item) => {
      if (item.toString() === blog._id.toString()) {
        categoryContainsBlog = true;
      }
    });

    // Check if unique
    if (!blogContainsCategory) {
      await this.modelBlog.updateOne({ _id: blog._id }, { $push: { categories: category._id } }).exec();
    }

    if (!categoryContainsBlog) {
      await this.modelCategory.updateOne({ _id: category._id }, { $push: { posts: blog._id } }).exec();
    }
    return "Update success";
  }

  async generateDummy() {
    const items = [];

    items.forEach((item) => {
      const post_id = item.split(",")[0];
      const category_id = item.split(",")[1];
      this.updateCategories({ post_id: Number(post_id), category_id: Number(category_id) });
    });
  }
}
