import { Injectable } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectModel } from "@nestjs/mongoose";
import { PaginateModel } from "mongoose";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { SiteConfigService } from "../site-configs/site-config.service";
import { CreateBlogCollectionDto } from "./dto";
import { BlogCollection, BlogCollectionDocument } from "./schema";

@Injectable()
export class BlogCollectionService {
  constructor(
    @InjectModel(BlogCollection.name)
    private readonly blogCollectionModel: PaginateModel<BlogCollectionDocument>,
    private readonly siteConfigService: SiteConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(payload: CreateBlogCollectionDto) {
    try {
      const newCollection = await this.blogCollectionModel.create(payload);
      this.eventEmitter.emit("rebuild-home");
      return newCollection;
    } catch (err) {
      throw err;
    }
  }

  async update(id: string, payload: CreateBlogCollectionDto) {
    const updatedCollection = await this.blogCollectionModel.findByIdAndUpdate(id, payload, { new: true });
    this.eventEmitter.emit("rebuild-home");
    return updatedCollection;
  }

  async getOne(id: string) {
    return await this.blogCollectionModel.findById(id, "-__v").populate("content_id", "title url");
  }

  async getMany(queries: PaginationParamDto) {
    const { page, limit, sort } = queries;
    const queryOptions = {
      page: page || 1,
      limit: limit || 10,
      populate: {
        path: "content_id",
        select: "title url",
      },
      projection: "-__v",
    };

    return await this.blogCollectionModel.paginate({}, queryOptions);
  }

  async delete(id: string) {
    await this.blogCollectionModel.deleteOne({ _id: id });
    this.eventEmitter.emit("rebuild-home");
    return "deleted";
  }

  async getActiveBlogs() {
    let blogCount: number;
    try {
      const blogCountConfig = await this.siteConfigService.findByKey("home.blog_display_count");
      blogCount = Number(blogCountConfig.value);
    } catch (err) {
      blogCount = 3;
    }

    const blogCollections = await this.blogCollectionModel
      .find(
        {
          status: true,
          $or: [
            {
              $and: [{ startDate: { $exists: false } }, { endDate: { $exists: false } }],
            },
            {
              $and: [{ startDate: null }, { endDate: null }],
            },
            {
              $and: [{ startDate: { $lte: Date.now() } }, { endDate: { $gte: Date.now() } }],
            },
          ],
        },
        {},
        {
          limit: blogCount,
          sort: { position: -1 },
        },
      )
      .populate("content_id");

    const blogs = blogCollections.map((blog) => blog.content_id);
    return blogs;
  }
}
