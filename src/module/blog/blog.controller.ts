import { Controller, Get, Post, Body, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Public } from "keycloak-connect-tbs";
import { BlogCategoryService } from "./blog-category.service";
import { BlogTagService } from "./blog-tag.service";
import { BlogService } from "./blog.service";
import { GetBlogDto } from "./dto/get-blog.dto";
import { UpdateBlogCategoryDto } from "./dto/update-blog-category.dto";
import { UpdateBlogTagDto } from "./dto/update-blog-tag.dto";
// import { CreateBlogDto } from "./dto/create-blog.dto";
// import { UpdateBlogDto } from "./dto/update-blog.dto";

@ApiTags("Blog")
@Controller("blog")
// TODO : resources
export class BlogController {
  constructor(
    private readonly blogService: BlogService,
    private readonly blogTagService: BlogTagService,
    private readonly blogCategoryService: BlogCategoryService,
  ) {}

  // @Post()
  // create(@Body() createBlogDto: CreateBlogDto) {
  //   return this.blogService.create(createBlogDto);
  // }

  @Get()
  @Public()
  getBlogs(@Query() query: GetBlogDto) {
    return this.blogService.getBlogs(query);
  }

  @Get("/tags")
  @Public()
  findAll() {
    return this.blogTagService.findAll();
  }

  @Post("/tags")
  @Public()
  setTag(@Body() payload: UpdateBlogTagDto) {
    return this.blogTagService.updateTags(payload);
  }

  @Get("/tags/dummy")
  @Public()
  generateDummyTags() {
    return this.blogTagService.generateDummy();
  }

  @Get("/categories")
  @Public()
  findAllCategories() {
    return this.blogCategoryService.findAll();
  }

  @Post("/categories")
  @Public()
  setCategories(@Body() payload: UpdateBlogCategoryDto) {
    return this.blogCategoryService.updateCategories(payload);
  }

  @Get("/categories/dummy")
  @Public()
  generateDummies() {
    return this.blogCategoryService.generateDummy();
  }

  // @Get(":id")
  // findOne(@Param("id") id: string) {
  //   return this.blogService.findOne(+id);
  // }

  // @Patch(":id")
  // update(@Param("id") id: string, @Body() updateBlogDto: UpdateBlogDto) {
  //   return this.blogService.update(+id, updateBlogDto);
  // }

  // @Delete(":id")
  // remove(@Param("id") id: string) {
  //   return this.blogService.remove(+id);
  // }
}
