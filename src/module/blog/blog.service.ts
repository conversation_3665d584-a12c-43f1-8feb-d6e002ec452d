import { GetBlogDto } from "./dto/get-blog.dto";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PaginateModel } from "mongoose";
import { BlogDocument } from "./schema/blog.schema";
import { BlogCategoryDocument } from "./schema/blog-category.schema";
import { BlogTagDocument } from "./schema/blog-tag.schema";

@Injectable()
export class BlogService {
  constructor(
    @InjectModel("Blog") private blogModel: PaginateModel<BlogDocument>,
    @InjectModel("BlogTag") private blogTagModel: Model<BlogTagDocument>,
    @InjectModel("BlogCategory") private blogCategoryModel: Model<BlogCategoryDocument>,
  ) {}

  async getBlogs(payload: GetBlogDto) {
    const query = {};
    // TODO - check ini harus nya status nya 1 only, tp isinya cuma ada 10 biji?

    if (payload.keyword) {
      const regex = new RegExp(payload.keyword, "i");

      query["$or"] = [{ title: regex }, { url_key: regex }, { short_content: regex }, { full_content: regex }];
    }

    if (payload.category_id) {
      const category = await this.blogCategoryModel.findOne({ category_id: Number(payload.category_id) });
      if (!category) {
        throw new HttpException("Category not found", HttpStatus.NOT_FOUND);
      }
      query["categories"] = { $in: [category._id] };
    }
    if (payload.category_key) {
      const category = await this.blogCategoryModel.findOne({ url_key: payload.category_key });
      if (!category) {
        throw new HttpException("Category not found", HttpStatus.NOT_FOUND);
      }
      query["categories"] = { $in: [category._id] };
    }
    if (payload.tag_id) {
      const tag = await this.blogTagModel.findOne({ tag_id: Number(payload.tag_id) });
      if (!tag) {
        throw new HttpException("Tag not found", HttpStatus.NOT_FOUND);
      }
      query["tags"] = { $in: [tag._id] };
    }
    if (payload.tag_key) {
      const tag = await this.blogTagModel.findOne({ url_key: payload.tag_key });
      if (!tag) {
        throw new HttpException("Tag not found", HttpStatus.NOT_FOUND);
      }
      query["tags"] = { $in: [tag._id] };
    }

    const options = {
      page: Number(payload.page ?? 1),
      limit: Number(payload.limit ?? 10),
      forceCountFn: true,
      select: ["-full_content", "-categories", "-tags"],
      sort: { views: -1, post_id: -1 },
    };
    return this.blogModel.paginate(query, options, function (err, result) {
      // Tambahkan path nya /blog/ di depan nya
      result.docs.forEach(function (e) {
        e.url_key = "/blog/" + e.url_key;

        const dummyImageUrl =
          "https://www.thebodyshop.co.id/media/amasty/blog/cache/M/a/1440/589/Manfaat_Raspberry_untuk_Kulit_Wajah.jpg";
        e.thumbnail_url = dummyImageUrl;
        e.post_thumbnail = dummyImageUrl;
        e.list_thumbnail = dummyImageUrl;
      });
      return result;
    });
  }
}
