import { CreateUrlShortenerDto } from "./dto/create-url-shortener.dto";
import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
  VERSION_NEUTRAL,
} from "@nestjs/common";
import { UrlShortener } from "./schema/url-shortener.schema";
import { UrlShortenerService } from "./url-shortener.service";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Public, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { Role } from "../enum/role.enum";
import { Scope } from "../enum/rbac.enum";
@ApiTags("Url Shortener")
@Controller({ version: VERSION_NEUTRAL })
export class UrlShortenerController {
  constructor(private readonly urlShortenerService: UrlShortenerService) {}

  @Post("url-shortener/admin")
  @ApiBearerAuth("access-token")
  @Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
  @Scopes(Scope.POST)
  async createShortUrl(@Body() payload: CreateUrlShortenerDto): Promise<UrlShortener> {
    const urlShortener = await this.urlShortenerService.createShortUrl(payload);
    return urlShortener;
  }

  @Public()
  @Get(":shortUrl")
  async redirectToOriginalUrl(@Param("shortUrl") shortUrl: string, @Query() query: Record<string, string>, @Res() res) {
    const urlShortener = await this.urlShortenerService.getOriginalUrl(shortUrl, query);
    if (!urlShortener) {
      throw new HttpException("Not found", HttpStatus.NOT_FOUND);
    }

    if (!res.headersSent) {
      res.redirect(urlShortener.originalUrl);
    }
  }
}
