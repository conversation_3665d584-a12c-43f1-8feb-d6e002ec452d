import { CreateUrlShortenerDto } from "./dto/create-url-shortener.dto";
import { HttpException, HttpStatus, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { UrlShortener, UrlShortenerDocument } from "./schema/url-shortener.schema";
import { UrlShortenerLog } from "./schema/url-shortener-log.schema";

@Injectable()
export class UrlShortenerService {
  constructor(
    @InjectModel("UrlShortener") private readonly urlShortenerModel: Model<UrlShortener>,
    @InjectModel("UrlShortenerLog") private readonly urlLogModel: Model<UrlShortenerLog>,
  ) {}

  async createShortUrl(payload: CreateUrlShortenerDto): Promise<UrlShortener> {
    const existing = await this.urlShortenerModel.findOne({ shortUrl: payload.shortUrl }).exec();
    if (existing) {
      throw new HttpException("Short url is already exist", HttpStatus.BAD_REQUEST);
    }

    const createdUrlShortener = new this.urlShortenerModel(payload);
    return createdUrlShortener.save();
  }

  async findOne(shortUrl: string): Promise<UrlShortenerDocument> {
    return this.urlShortenerModel.findOne({ shortUrl }).exec();
  }

  async getOriginalUrl(shortUrl: string, query: Record<string, string>): Promise<UrlShortener> {
    const urlShortener = await this.urlShortenerModel.findOneAndUpdate(
      { shortUrl },
      { $inc: { count: 1 } },
      { new: true },
    );

    if (urlShortener) {
      // Update parameter count if query parameters are present
      if (Object.keys(query).length > 0) {
        await this.updateParams(urlShortener._id, query);
      }

      await this.updateLog(urlShortener._id, shortUrl, query);

      return urlShortener;
    }

    return null;
  }

  async updateParams(id: string, queryParams: Record<string, string>): Promise<any> {
    const urlShortener = await this.urlShortenerModel.findById(id);
    if (!urlShortener) {
      throw new NotFoundException(`Url with ID "${id}" not found`);
    }

    const updateObj = {};
    for (const [paramName, paramValue] of Object.entries(queryParams)) {
      updateObj[`params.${paramName}.${paramValue}`] = 1; // Increment the count for this param value
    }

    return this.urlShortenerModel.updateOne({ _id: id }, { $inc: updateObj }).exec();
  }

  async updateLog(id: string, url: string, query: Record<string, string>) {
    const params = new URLSearchParams(query).toString();
    const model = new this.urlLogModel({ urlId: id, url, params });
    model.save();
  }
}
