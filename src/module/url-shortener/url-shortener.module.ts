import { UrlShortenerSchema } from "./schema/url-shortener.schema";
import { UrlShortenerController } from "./url-shortener.controller";
import { Module } from "@nestjs/common";
import { UrlShortenerService } from "./url-shortener.service";
import { MongooseModule } from "@nestjs/mongoose";
import { UrlShortenerLogSchema } from "./schema/url-shortener-log.schema";

@Module({
  providers: [UrlShortenerService],
  controllers: [UrlShortenerController],
  imports: [
    MongooseModule.forFeature([
      {
        name: "UrlShortener",
        schema: UrlShortenerSchema,
      },
      {
        name: "UrlShortenerLog",
        schema: UrlShortenerLogSchema,
      },
    ]),
  ],
})
export class UrlShortenerModule {}
