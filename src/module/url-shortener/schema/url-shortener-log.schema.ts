import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId } from "bson";
import mongoose, { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
@Schema({
  timestamps: true,
})
export class UrlShortenerLog extends Document {
  @Prop({ type: mongoose.mongo.ObjectId })
  urlId: ObjectId;

  @Prop({ type: String, unique: false })
  url: string;

  @Prop({ type: String, unique: false })
  params: string;
}

export type UrlShortenerLogDocument = UrlShortenerLog & Document;

export const UrlShortenerLogSchema = SchemaFactory.createForClass(UrlShortenerLog);

UrlShortenerLogSchema.plugin(mongoosePaginate);
