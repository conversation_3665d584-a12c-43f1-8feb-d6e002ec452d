import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose, { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
@Schema({
  timestamps: true,
})
export class UrlShortener extends Document {
  @Prop({ required: true, type: String, trim: true })
  name: string;

  @Prop({ type: String, required: true, trim: true })
  originalUrl: string;

  @Prop({ type: String, required: true, unique: true, trim: true })
  shortUrl: string;

  @Prop({ type: Number, default: 0, isInteger: true })
  count: number;

  @Prop({ type: mongoose.Schema.Types.Mixed, default: {} })
  params: Record<string, Record<string, number>>;
}

export type UrlShortenerDocument = UrlShortener & Document;

export const UrlShortenerSchema = SchemaFactory.createForClass(UrlShortener);

UrlShortenerSchema.plugin(mongoosePaginate);
