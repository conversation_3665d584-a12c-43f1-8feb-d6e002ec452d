import { En<PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn } from 'typeorm';

@Entity('analytics_data') // Replace with your actual table name
export class AnalyticsData {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  category: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  value: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  status: string;

  @CreateDateColumn()
  created_date: Date;

  @Column({ type: 'varchar', length: 100, nullable: true })
  created_by: string;
}
