import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { AnalyticsData } from "./entities/analytics-data.entity";
import { AnalyticsQueryDto } from "./dto/analytics-query.dto";

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(AnalyticsData)
    private readonly analyticsRepository: Repository<AnalyticsData>,
  ) {}

  async findAll(queryDto: AnalyticsQueryDto) {
    const { category, status, startDate, endDate, page = 1, limit = 10 } = queryDto;

    const queryBuilder: SelectQueryBuilder<AnalyticsData> = this.analyticsRepository.createQueryBuilder("analytics");

    // Apply filters
    if (category) {
      queryBuilder.andWhere("analytics.category = :category", { category });
    }

    if (status) {
      queryBuilder.andWhere("analytics.status = :status", { status });
    }

    if (startDate) {
      queryBuilder.andWhere("analytics.created_date >= :startDate", { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere("analytics.created_date <= :endDate", { endDate });
    }

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Order by created date descending
    queryBuilder.orderBy("analytics.created_date", "DESC");

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findById(id: number): Promise<AnalyticsData> {
    return this.analyticsRepository.findOne({ where: { id } });
  }

  async getStatistics() {
    const totalRecords = await this.analyticsRepository.count();

    const categoryStats = await this.analyticsRepository
      .createQueryBuilder("analytics")
      .select("analytics.category", "category")
      .addSelect("COUNT(*)", "count")
      .groupBy("analytics.category")
      .getRawMany();

    const statusStats = await this.analyticsRepository
      .createQueryBuilder("analytics")
      .select("analytics.status", "status")
      .addSelect("COUNT(*)", "count")
      .groupBy("analytics.status")
      .getRawMany();

    return {
      totalRecords,
      categoryStats,
      statusStats,
    };
  }

  async executeCustomQuery(query: string, parameters: any[] = []) {
    // WARNING: This is for demonstration only. In production, you should:
    // 1. Validate and sanitize the query
    // 2. Use parameterized queries
    // 3. Implement proper access controls
    // 4. Log all custom queries for security auditing

    try {
      const result = await this.analyticsRepository.query(query, parameters);
      return {
        success: true,
        data: result,
        rowCount: result.length,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }
  }
}
