import { Controller, Get, Query, Param, Post, Body, ParseIntPipe } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from "@nestjs/swagger";
import { AnalyticsService } from "./analytics.service";
import { AnalyticsQueryDto } from "./dto/analytics-query.dto";

@ApiTags("Analytics")
@Controller("analytics")
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get()
  @ApiOperation({ summary: "Get analytics data with filtering and pagination" })
  @ApiResponse({ status: 200, description: "Analytics data retrieved successfully" })
  @ApiQuery({ name: "category", required: false, description: "Filter by category" })
  @ApiQuery({ name: "status", required: false, description: "Filter by status" })
  @ApiQuery({ name: "startDate", required: false, description: "Start date (YYYY-MM-DD)" })
  @ApiQuery({ name: "endDate", required: false, description: "End date (YYYY-MM-DD)" })
  @ApiQuery({ name: "page", required: false, description: "Page number", example: 1 })
  @ApiQuery({ name: "limit", required: false, description: "Items per page", example: 10 })
  async findAll(@Query() queryDto: AnalyticsQueryDto) {
    return this.analyticsService.findAll(queryDto);
  }

  @Get("statistics")
  @ApiOperation({ summary: "Get analytics statistics and summary" })
  @ApiResponse({ status: 200, description: "Statistics retrieved successfully" })
  async getStatistics() {
    return this.analyticsService.getStatistics();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get analytics data by ID" })
  @ApiResponse({ status: 200, description: "Analytics data found" })
  @ApiResponse({ status: 404, description: "Analytics data not found" })
  async findById(@Param("id", ParseIntPipe) id: number) {
    return this.analyticsService.findById(id);
  }

  @Post("custom-query")
  @ApiOperation({
    summary: "Execute custom SQL query",
    description:
      "WARNING: This endpoint is for demonstration only. In production, implement proper validation and security measures.",
  })
  @ApiResponse({ status: 200, description: "Query executed successfully" })
  @ApiResponse({ status: 400, description: "Query execution failed" })
  async executeCustomQuery(@Body() body: { query: string; parameters?: any[] }) {
    const { query, parameters = [] } = body;
    return this.analyticsService.executeCustomQuery(query, parameters);
  }
}
