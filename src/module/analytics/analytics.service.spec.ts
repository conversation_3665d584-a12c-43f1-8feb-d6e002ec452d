import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { AnalyticsService } from "./analytics.service";
import { AnalyticsData } from "./entities/analytics-data.entity";

describe("AnalyticsService", () => {
  let service: AnalyticsService;
  let repository: Repository<AnalyticsData>;

  const mockRepository = {
    createQueryBuilder: jest.fn(() => ({
      andWhere: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
    })),
    findOne: jest.fn(),
    count: jest.fn(),
    query: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AnalyticsService,
        {
          provide: getRepositoryToken(AnalyticsData),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<AnalyticsService>(AnalyticsService);
    repository = module.get<Repository<AnalyticsData>>(getRepositoryToken(AnalyticsData));
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  it("should find all analytics data with pagination", async () => {
    const queryDto = { page: 1, limit: 10 };
    const result = await service.findAll(queryDto);

    expect(result).toHaveProperty("data");
    expect(result).toHaveProperty("pagination");
    expect(result.pagination.page).toBe(1);
    expect(result.pagination.limit).toBe(10);
  });
});
