import { Controller, Get, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { InternalAccess, Public } from "keycloak-connect-tbs";
import { CacheManagerService } from "./cache-manager.service";
import { CacheManagerDto } from "./cache-manager.dto";

@ApiTags("Cache Manager")
@Controller("cache-manager")
@InternalAccess()
@Public()
export class CacheManagerController {
  constructor(private readonly cacheManagerService: CacheManagerService) {}

  // Cache manager delete all cache in redis
  @Get("purge")
  async purge() {
    return this.cacheManagerService.purge();
  }

  // Delete cache with key pattern (accepts enum of string)
  @Get("purge-pattern")
  async purgePattern(@Query() pattern: CacheManagerDto) {
    return this.cacheManagerService.purgePattern(pattern);
  }
}
