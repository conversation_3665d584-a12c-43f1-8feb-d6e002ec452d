import { <PERSON>du<PERSON> } from "@nestjs/common";
import { CacheManagerService } from "./cache-manager.service";
import { CacheManagerController } from "./cache-manager.controller";
import { RedisModule } from "@liaoliaots/nestjs-redis";

@Module({
  controllers: [CacheManagerController],
  providers: [CacheManagerService],
  imports: [RedisModule.forRoot({ config: { url: process.env.REDIS_HOST + ":" + process.env.REDIS_PORT_PUBLIC } })],
})
export class CacheManagerModule {}
