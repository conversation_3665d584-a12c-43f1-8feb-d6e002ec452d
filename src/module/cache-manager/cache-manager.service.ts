import { InjectRedis } from "@liaoliaots/nestjs-redis";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import Redis from "ioredis";
import { CacheManagerDto } from "./cache-manager.dto";

@Injectable()
export class CacheManagerService {
  constructor(
    @InjectRedis()
    private readonly redisService: Redis,
  ) {}

  async purge() {
    try {
      const result = await this.redisService.flushall();
      return result;
    } catch (e) {
      throw new HttpException("No data", HttpStatus.NOT_FOUND);
    }
  }

  async purgePattern(params: CacheManagerDto) {
    try {
      let { pattern, keyword } = params;
      let keywordToPurge = "";
      if (keyword) {
        // remove leading /
        keyword = keyword.replace(/^\//, "");
        keywordToPurge = `*${keyword}*`;
      }
      if (pattern) {
        pattern = pattern.replace(/^\//, "");
        keywordToPurge = pattern;
      }
      const keys = await this.redisService.keys(keywordToPurge);
      const result = await this.redisService.del(keys);
      return result;
    } catch (e) {
      return "no cache";
      // throw new HttpException("No data", HttpStatus.NOT_FOUND);
    }
  }
}
