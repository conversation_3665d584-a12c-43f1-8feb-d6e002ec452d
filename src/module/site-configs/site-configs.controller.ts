import { Controller, Get, Param } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Public, InternalAccess } from "keycloak-connect-tbs";
import { SiteConfigService } from "./site-config.service";

@ApiTags("Site Configs")
@Controller("configs")
// TODO : resources
export class SiteConfigsController {
  constructor(private readonly service: SiteConfigService) {}

  @Get()
  @Public()
  getConfigs() {
    return this.service.findAll();
  }

  @Get(":key")
  @Public()
  @InternalAccess()
  getConfigByKey(@Param("key") key: string) {
    return this.service.findByKey(key);
  }
}
