import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
@Schema({
  timestamps: true,
})
export class SiteConfig extends Document {
  @Prop({ required: true, unique: true, type: String, trim: true })
  key: string;

  @Prop()
  value: string;

  @Prop()
  description: string;

  @Prop({ default: 1 })
  status: number;
}

export type SiteConfigDocument = SiteConfig & Document;

export const SiteConfigSchema = SchemaFactory.createForClass(SiteConfig);

SiteConfigSchema.plugin(mongoosePaginate);
