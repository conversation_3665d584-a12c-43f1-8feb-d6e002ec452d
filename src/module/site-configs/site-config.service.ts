import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { PaginateModel } from "mongoose";
import { CreateConfigDto } from "./dto/create-config.dto";
import { UpdateConfigDto } from "./dto/update-config.dto";
import { SiteConfig, SiteConfigDocument } from "./schema/site-config.schema";
import { FindAllConfigDto } from "./dto/find-all-config.dto";
import { TbsSiteConfigService } from "tbs-site-config";

@Injectable()
export class SiteConfigService {
  constructor(
    @InjectModel("SiteConfig") private configModel: PaginateModel<SiteConfigDocument>,
    private readonly siteConfigService: TbsSiteConfigService,
  ) {}

  async findAll() {
    return this.siteConfigService.getAll();
    // const res = [];
    // Object.values(this.siteConfig).map((item) => {
    //   if (item.status === 1) res.push(item);
    // });
    //
    // return res;
    // return await this.configModel.find({ status: 1 });
  }

  async findByKey(key: string) {
    const config = await this.siteConfigService.get(key);
    if (!config) {
      throw new HttpException("Config not found", HttpStatus.BAD_REQUEST);
    }
    return JSON.parse(config);
  }

  async findAllAdmin(params: FindAllConfigDto) {
    const { page = 1, limit = 10, sort, key_prefix } = params;
    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort,
      lean: true,
    };

    const query: Record<string, any> = {};
    if (key_prefix) query.key = { $regex: new RegExp(`^${key_prefix}`, "i") };

    const res = await this.configModel.paginate(query, options);

    res.docs = await Promise.all(
      res.docs.map(async (item) => {
        const redis = await this.siteConfigService.get(item.key);

        if (redis) {
          item.value = JSON.parse(redis)?.value;
        }

        return item;
      }),
    );

    return res;
  }

  async create(createConfigDto: CreateConfigDto) {
    try {
      return await this.siteConfigService.create(createConfigDto);
    } catch (err) {
      if (/already exists/.test(err.message)) {
        throw new HttpException(err.message, HttpStatus.BAD_REQUEST);
      }

      throw err;
    }
    // const existing = await this.configModel.findOne({ key: createConfigDto.key }).exec();
    //
    // if (existing) {
    //   throw new HttpException("Config is already exist", HttpStatus.BAD_REQUEST);
    // }
    // const newData = new this.configModel(createConfigDto);
    //
    // const data = await newData.save();
    //
    // await this.siteConfigService.update(data.toObject());
    //
    // return data;
  }

  async findById(id: string): Promise<SiteConfig | null> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const fromDb = await this.configModel.findById(id).exec();
    const fromRedis = await this.siteConfigService.get(fromDb.key);

    if (fromRedis) {
      return JSON.parse(fromRedis);
    }
    return fromDb;
  }

  async update(id: string, payload: UpdateConfigDto) {
    const data = await this.configModel.findById(id).lean();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const update = await this.configModel.findOneAndUpdate({ _id: id }, payload, { new: true });
    await this.siteConfigService.update(update.key, { ...data, ...payload });

    if (!update) {
      throw "Update data failed.";
    }
    return update;
  }

  async remove(id: string): Promise<any> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const check = await this.configModel.findById(id, "key").lean();

    if (check) {
      await this.configModel.deleteOne({ _id: check._id });
      await this.siteConfigService.delete(check.key);
      return true;
    }

    return check;
    //
    // return await this.configModel.deleteOne({ _id: id }).exec();
  }
}
