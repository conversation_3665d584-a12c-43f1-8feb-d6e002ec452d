import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { AdminSiteConfigsController } from "./admin-site-configs.controller";
import { SiteConfigSchema } from "./schema/site-config.schema";
import { SiteConfigService } from "./site-config.service";
import { SiteConfigsController } from "./site-configs.controller";

@Module({
  controllers: [SiteConfigsController, AdminSiteConfigsController],
  providers: [SiteConfigService],
  imports: [
    MongooseModule.forFeature([
      {
        name: "SiteConfig",
        schema: SiteConfigSchema,
      },
    ]),
    ApmModule.register(),
  ],
  exports: [SiteConfigService],
})
export class SiteConfigsModule {}
