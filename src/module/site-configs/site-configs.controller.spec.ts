import { Test, TestingModule } from "@nestjs/testing";
import { SiteConfigsController } from "./site-configs.controller";

describe("SiteConfigsController", () => {
  let controller: SiteConfigsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SiteConfigsController],
    }).compile();

    controller = module.get<SiteConfigsController>(SiteConfigsController);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
