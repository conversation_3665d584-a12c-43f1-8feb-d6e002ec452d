import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Resource, RoleMatchingMode, Roles, Scopes, InternalAccess } from "keycloak-connect-tbs";
import { Role } from "../enum/role.enum";
import { CreateConfigDto } from "./dto/create-config.dto";
import { UpdateConfigDto } from "./dto/update-config.dto";
import { SiteConfigService } from "./site-config.service";
import { Controllers, Scope } from "../enum/rbac.enum";
import { FindAllConfigDto } from "./dto/find-all-config.dto";

@ApiTags("Admin - Site Configs")
@ApiBearerAuth("access-token")
@Controller("admin/configs")
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
@Resource(Controllers.ADMIN_SITE_CONFIGS)
export class AdminSiteConfigsController {
  constructor(private readonly service: SiteConfigService) {}

  @Get()
  @Scopes(Scope.GET)
  @InternalAccess()
  findAll(@Query() query: FindAllConfigDto) {
    return this.service.findAllAdmin(query);
  }

  @Post()
  @Scopes(Scope.POST)
  @InternalAccess()
  create(@Body() payload: CreateConfigDto) {
    return this.service.create(payload);
  }

  @Get(":id")
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.service.findById(id);
  }

  @Patch(":id")
  @Scopes(Scope.PATCH)
  @InternalAccess()
  update(@Param("id") id: string, @Body() payload: UpdateConfigDto) {
    return this.service.update(id, payload);
  }

  @Delete(":id")
  @Scopes(Scope.DELETE)
  remove(@Param("id") id: string) {
    return this.service.remove(id);
  }
}
