import { Controller, Get, Param, Query } from "@nestjs/common";
import { TemplateService } from "../microservices/sendportal/template.service";
import { ApiTags } from "@nestjs/swagger";
import { Public, Resource, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../enum";
import { EmailTemplateService } from "./email-template.service";
import { GetTemplatesDto } from "./dto/get-templates.dto";

@ApiTags("EMAIL NOTIFICATION")
@Resource(Controllers.EMAIL_NOTIFICATION_TEMPLATE)
@Controller("email/template")
export class EmailTemplateController {
  constructor(private readonly emailTemplateService: EmailTemplateService) {}

  @Get()
  @Scopes(Scope.GET)
  @Public()
  async getTemplates(@Query() queries: GetTemplatesDto) {
    try {
      return await this.emailTemplateService.getTemplates(queries.keyword);
    } catch (err) {
      throw err;
    }
  }

  @Get(":id")
  @Scopes(Scope.GET)
  @Public()
  async getTemplateDetail(@Param("id") templateID: number) {
    try {
      return await this.emailTemplateService.getTemplateDetail(templateID);
    } catch (err) {
      throw err;
    }
  }

  @Get(":id/keys")
  @Scopes(Scope.GET)
  @Public()
  async getTemplateKeys(@Param("id") templateID: number) {
    try {
      return await this.emailTemplateService.getTemplateKeys(templateID);
    } catch (err) {
      throw err;
    }
  }
}
