import { <PERSON>du<PERSON> } from "@nestjs/common";
import { EmailNotificationController } from "./email-notification.controller";
import { EmailNotificationService } from "./email-notification.service";
import { SendPortalModule } from "../microservices/sendportal/sendportal.module";
import { ConfigModule } from "@nestjs/config";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { EmailTemplateController } from "./email-template.controller";
import { EmailTemplateService } from "./email-template.service";

@Module({
  imports: [SendPortalModule, ConfigModule, ApmModule.register()],
  controllers: [EmailNotificationController, EmailTemplateController],
  providers: [EmailNotificationService, EmailTemplateService],
})
export class EmailNotifModule {}
