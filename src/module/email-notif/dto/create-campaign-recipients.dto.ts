import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";

export class CampaignRecipientDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  card_number: string;

  @ApiProperty({
    type: Array,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  params?: Array<Record<string, any>>;
}

export class CampaignRecipientsDto {
  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  campaign_id: number;

  @ApiProperty({
    type: [CampaignRecipientDto],
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  card_numbers: Array<CampaignRecipientDto>;
}
