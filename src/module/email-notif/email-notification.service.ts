import { Injectable } from "@nestjs/common";
import { TemplateService } from "../microservices/sendportal/template.service";
import { CampaignsService } from "../microservices/sendportal/campaigns.service";
import { SubscribersService } from "../microservices/sendportal/subscribers.service";
import { ICreateCampaign } from "../microservices/sendportal/interfaces/create-campaign.interface";
import { CreateCampaignDto } from "./dto/create-campaign.dto";
import { CampaignRecipientsDto } from "./dto/create-campaign-recipients.dto";
import { ICampaignRecipients } from "../microservices/sendportal/interfaces/create-campaign-recipients.interface";

@Injectable()
export class EmailNotificationService {
  constructor(
    private readonly templateService: TemplateService,
    private readonly campaignService: CampaignsService,
    private readonly subscriberService: SubscribersService,
  ) {}

  async createCampaign(createCampaignDto: CreateCampaignDto) {
    try {
      const payload: ICreateCampaign = {
        name: createCampaignDto.campaign_name,
        subject: createCampaignDto.email_subject,
        content: "",
        template_id: String(createCampaignDto.template_id),
        email_service_id: "1",
        from_name: createCampaignDto.from_name,
        from_email: createCampaignDto.from_email,
        is_open_tracking: "1",
        is_click_tracking: "1",
        send_to_all: "0",
        save_as_draft: "0",
        send_to_card_number: "1",
        scheduled_at: new Date().toISOString(),
        params: createCampaignDto.params,
      };

      const campaign = await this.campaignService.createCampaign(payload);
      return campaign;
    } catch (err) {
      throw err;
    }
  }

  async setCampaignRecipients(campaignRecipientDto: CampaignRecipientsDto) {
    try {
      const { campaign_id, card_numbers } = campaignRecipientDto;
      const payload: ICampaignRecipients = {
        card_numbers,
      };

      const campaignRecipients = await this.campaignService.setCampaignRecipients(campaign_id, payload);
      return campaignRecipients;
    } catch (err) {
      throw err;
    }
  }

  async updateCampaign(campaignId: number, createCampaignDto: CreateCampaignDto) {
    try {
      const payload: ICreateCampaign = {
        name: createCampaignDto.campaign_name,
        subject: createCampaignDto.email_subject,
        content: "",
        tags: [],
        template_id: String(createCampaignDto.template_id),
        email_service_id: "1",
        from_name: createCampaignDto.from_name,
        from_email: createCampaignDto.from_email,
        is_open_tracking: "1",
        is_click_tracking: "1",
        send_to_all: "0",
        save_as_draft: "0",
        send_to_card_number: "1",
        scheduled_at: new Date().toISOString(),
        params: createCampaignDto.params,
      };

      await this.campaignService.resetCampaign(campaignId);
      const campaign = await this.campaignService.updateCampaign(campaignId, payload);
      return campaign;
    } catch (err) {
      throw err;
    }
  }

  async getCampaigns() {}

  async getOneCampaign() {}

  async sendCampaign(campaignId: number) {
    try {
      return await this.campaignService.sendCampaign(campaignId);
    } catch (err) {
      throw err;
    }
  }

  async resetCampaign(campaignId: number) {
    try {
      return await this.campaignService.resetCampaign(campaignId);
    } catch (err) {
      throw err;
    }
  }
}
