import { Body, Controller, Get, Param, Post, Put } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { InternalAccess, Public, Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../enum/rbac.enum";
import { EmailNotificationService } from "./email-notification.service";
import { CreateCampaignDto } from "./dto/create-campaign.dto";
import { CampaignRecipientsDto } from "./dto/create-campaign-recipients.dto";
import { Role } from "../enum/role.enum";

@ApiTags("EMAIL NOTIFICATION")
@ApiBearerAuth("access-token")
@Resource(Controllers.EMAIL_NOTIFICATION)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
@Controller("email/campaign")
export class EmailNotificationController {
  constructor(private readonly emailService: EmailNotificationService) {}

  @Post("")
  @Scopes(Scope.POST)
  createCampaign(@Body() createCampaignDto: CreateCampaignDto) {
    return this.emailService.createCampaign(createCampaignDto);
  }

  @Put("/:id")
  @Scopes(Scope.PUT)
  updateCampaign(@Param("id") id: number, @Body() campaignRecipientDto: CreateCampaignDto) {
    return this.emailService.updateCampaign(id, campaignRecipientDto);
  }

  @Post("/recipient")
  @Scopes(Scope.POST)
  setCampaignRecipients(@Body() campaignRecipientDto: CampaignRecipientsDto) {
    return this.emailService.setCampaignRecipients(campaignRecipientDto);
  }

  @Get("/:id/send")
  @Scopes(Scope.GET)
  sendCampaign(@Param("id") campaignID: number) {
    try {
      return this.emailService.sendCampaign(campaignID);
    } catch (err) {
      throw err;
    }
  }

  @Get("/:id/reset")
  @Scopes(Scope.GET)
  resetCampaign(@Param("id") campaignID: number) {
    try {
      return this.emailService.resetCampaign(campaignID);
    } catch (err) {
      throw err;
    }
  }
}
