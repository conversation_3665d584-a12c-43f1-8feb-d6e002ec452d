import { Injectable } from "@nestjs/common";
import { TemplateService } from "../microservices/sendportal/template.service";

@Injectable()
export class EmailTemplateService {
  constructor(private readonly templateService: TemplateService) {}

  async getTemplates(keyword: string) {
    try {
      const filter = keyword ? `?name=${keyword}` : "";
      const template = await this.templateService.getTemplates(filter);
      const result = template.map((template) => ({ id: template.id, name: template.name }));

      return result;
    } catch (err) {
      throw err;
    }
  }

  async getTemplateKeys(templateID: number) {
    try {
      const template = await this.templateService.getOneTemplate(templateID);

      const keys = template.content.match(new RegExp("[^{{]+(?=}})", "g"));
      if (!keys) return [];

      const campaignKey = "camp.";
      const result = keys.filter((key) => key.startsWith(campaignKey)).map((key) => key.replace(campaignKey, ""));
      return result;
    } catch (err) {
      throw err;
    }
  }

  async getTemplateDetail(templateID: number) {
    try {
      const { id, name, content } = await this.templateService.getOneTemplate(templateID);

      return {
        id,
        name,
        keys: this.getCampaignKeys(content),
      };
    } catch (err) {
      throw err;
    }
  }

  private getCampaignKeys(templateHtml: string) {
    const keys = templateHtml.match(new RegExp("[^{{]+(?=}})", "g")) || [];
    if (!keys) return [];

    const campaignKey = "camp.";
    const result = keys.filter((key) => key.startsWith(campaignKey)).map((key) => key.replace(campaignKey, ""));

    return result;
  }
}
