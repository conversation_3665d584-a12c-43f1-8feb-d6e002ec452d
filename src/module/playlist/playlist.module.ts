import { RedisModule } from "@liaoliaots/nestjs-redis";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { StoreGroupModule } from "../store-group/store-group.module";
import { AdminPlaylistController } from "./admin-playlist.controller";
import { PlaylistController } from "./playlist.controller";
import { PlaylistService } from "./playlist.service";
import { PlaylistSchema } from "./schema/playlist.schema";

@Module({
  controllers: [AdminPlaylistController, PlaylistController],
  providers: [PlaylistService],
  imports: [
    StoreGroupModule,
    MongooseModule.forFeature([
      {
        name: "Playlist",
        schema: PlaylistSchema,
      },
    ]),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const host = configService.get<string>("REDIS_HOST");
        const port = configService.get<number>("REDIS_PORT");
        const db = configService.get<number>("REDIS_PLAYLIST_DB") || 6;
        return { config: { host, port, db } };
      },
    }),
  ],
  exports: [PlaylistService],
})
export class PlaylistModule {}
