import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { BypassFieldsValidation } from "global-body-validator-tbs";
import { Resource, RoleMatchingMode, Roles, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../enum/rbac.enum";
import { Role } from "../enum/role.enum";
import { CreatePlaylistDto } from "./dto/create-playlist.dto";
import { GetPlaylistDto } from "./dto/get-playlist.dto";
import { PlaylistService } from "./playlist.service";

@ApiTags("Admin - Playlist")
@Controller("admin/playlist")
@ApiBearerAuth("access-token")
@Resource(Controllers.ADMIN_PLAYLIST)
@Roles({ roles: [Role.Admin, `realm:app-${Role.Admin}`], mode: RoleMatchingMode.ANY })
export class AdminPlaylistController {
  constructor(private readonly playlistService: PlaylistService) {}

  /*
   * create a playlist
   */
  @Post()
  @Scopes(Scope.POST)
  @BypassFieldsValidation("*")
  createPlaylist(@Body() createPlaylistDto: CreatePlaylistDto) {
    return this.playlistService.create(createPlaylistDto);
  }

  /**
   * get a playlist
   * @param {id}
   */
  @Get("/:id")
  @Scopes(Scope.GET)
  findOne(@Param("id") id: string) {
    return this.playlistService.findById(id);
  }

  /**
   * get list of playlists
   */
  @Get("")
  @Scopes(Scope.GET)
  find(@Query() pagination: GetPlaylistDto) {
    return this.playlistService.findAll(pagination);
  }

  /**
   * update a playlist
   * @param {id}
   */
  @Patch("/:id")
  @Scopes(Scope.PATCH)
  @BypassFieldsValidation("*")
  update(@Param("id") id: string, @Body() updatePlaylistDto: CreatePlaylistDto) {
    return this.playlistService.update(id, updatePlaylistDto);
  }

  /**
   * delete a playlist
   * @param {id}
   */
  @Delete("/:id")
  @Scopes(Scope.DELETE)
  remove(@Param("id") id: string) {
    return this.playlistService.remove(id);
  }

  /**
   * refresh all playlists
   */
  @Get("/refresh")
  @Scopes(Scope.GET)
  refresh() {
    return this.playlistService.flushAllCache();
  }
}
