import { ApiProperty } from "@nestjs/swagger";
import {
  <PERSON>Array,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsO<PERSON>al,
  IsString,
  Matches,
  Min,
} from "class-validator";
import { TransformArray } from "src/decorator/transform-array.decorator";
import { PlaylistType } from "../enum/playlist-type.enum";

export class CreatePlaylistDto {
  @ApiProperty({ required: true, description: "Playlist title" })
  @IsNotEmpty({ message: "Title cannot be empty" })
  @IsString()
  @Matches(/^(?!\s*$).+/, { message: "Title cannot contain only spaces" })
  title: string;

  @ApiProperty({ required: true, description: "Start date", example: "2023-01-01T00:00:00.000Z" })
  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  @ApiProperty({ required: true, description: "End date", example: "2023-12-31T23:59:59.000Z" })
  @IsNotEmpty()
  @IsDateString()
  endDate: string;

  @ApiProperty({ required: true, description: "URL for the playlist item" })
  @IsNotEmpty()
  @IsString()
  url: string;

  @ApiProperty({
    required: true,
    description: "Type of playlist item",
    enum: PlaylistType,
    example: PlaylistType.IMAGE,
  })
  @IsNotEmpty()
  @IsEnum(PlaylistType)
  type: PlaylistType;

  @ApiProperty({
    required: false,
    default: null,
    example: '["65a4b778dcc6792340095afc","65a4c165c39c62bfa901b3d5"]',
  })
  @IsArray()
  @IsOptional()
  @TransformArray()
  store_group: Record<string, any>;

  @ApiProperty({ required: false, description: "Order position (higher numbers appear first)", default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  ordering?: number;
}
