import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { PlaylistType } from "../enum/playlist-type.enum";

export class GetPlaylistDto extends PaginationParamDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    required: false,
    enum: PlaylistType,
    description: "Type of playlist item",
  })
  @IsEnum(PlaylistType)
  @IsOptional()
  type?: PlaylistType;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  ordering?: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  keyword?: string;
}
