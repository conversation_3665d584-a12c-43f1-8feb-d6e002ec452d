import { <PERSON>, Get, Param } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON>erA<PERSON>, ApiTags } from "@nestjs/swagger";
import { InternalAccess, Public, Resource, Scopes } from "keycloak-connect-tbs";
import { <PERSON><PERSON>, Scope } from "../enum/rbac.enum";
import { PlaylistService } from "./playlist.service";

@Controller("playlist")
@ApiTags("Playlist")
@Resource(Controllers.PLAYLIST)
@ApiBearerAuth("access-token")
export class PlaylistController {
  constructor(private readonly playlistService: PlaylistService) {}

  /**
   * get playlist by id
   * @returns get playlist details
   */
  @Get("detail/:id")
  @Scopes(Scope.GET)
  @InternalAccess()
  async getById(@Param("id") id: string) {
    return this.playlistService.findById(id);
  }

  /**
   * get active playlists by store code
   * @returns get active playlists for a store code
   */
  @Get("list/:storeCode")
  @Scopes(Scope.GET)
  @Public()
  async listByStoreCode(@Param("storeCode") storeCode: string) {
    return this.playlistService.getActivePlaylists(storeCode);
  }

  /**
   * get all active playlists
   * @returns get all active playlists
   */
  @Get("list")
  @Scopes(Scope.GET)
  @Public()
  async list() {
    return this.playlistService.getActivePlaylists();
  }
}
