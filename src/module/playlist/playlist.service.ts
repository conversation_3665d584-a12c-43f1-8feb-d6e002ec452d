import { InjectRedis } from "@liaoliaots/nestjs-redis";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import Redis from "ioredis";
import mongoose, { PaginateModel } from "mongoose";
import { StoreGroupService } from "../store-group/store-group.service";
import { CreatePlaylistDto } from "./dto/create-playlist.dto";
import { GetPlaylistDto } from "./dto/get-playlist.dto";
import { PlaylistDocument } from "./schema/playlist.schema";

@Injectable()
export class PlaylistService {
  constructor(
    @InjectModel("Playlist") private playlistModel: PaginateModel<PlaylistDocument>,
    @InjectRedis()
    private readonly redisService: Redis,
    private readonly storeGroupService: StoreGroupService,
  ) {}

  /**
   * create playlist
   * @param {CreatePlaylistDto} params
   */
  async create(params: CreatePlaylistDto) {
    const playlist = new this.playlistModel(params);
    await playlist.save();
    await this.flushAllCache();
    return playlist;
  }

  /**
   * find playlist by id
   * @param {string} id
   */
  async findById(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }
    const playlist = await this.playlistModel.findById(id);
    if (!playlist) {
      throw new HttpException("Playlist not found.", HttpStatus.NOT_FOUND);
    }
    return playlist;
  }

  /**
   * get all playlists
   * @param GetPlaylistDto params
   */
  async findAll(params: GetPlaylistDto) {
    const filter: Record<string, any> = {};
    const { page = 1, limit = 10, sort } = params;

    if (params.title) {
      filter.title = { $regex: new RegExp(params.title), $options: "i" };
    }

    if (params.ordering) {
      filter.ordering = params.ordering;
    }

    if (params.type) {
      filter.type = params.type;
    }

    if (params.keyword) {
      filter.$or = [
        { title: { $regex: new RegExp(params.keyword), $options: "i" } },
        { storeCode: { $regex: new RegExp(params.keyword), $options: "i" } },
      ];
    }

    const options = {
      page: Number(page),
      limit: Number(limit),
      forceCountFn: true,
      sort: sort || "-ordering",
    };

    const result = await this.playlistModel.paginate(filter, options);

    return result;
  }

  /**
   * update playlist
   * @param {string} id
   * @param {CreatePlaylistDto} params
   */
  async update(id: string, params: CreatePlaylistDto) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const playlist = await this.playlistModel.findOneAndUpdate({ _id: id }, params, {
      new: true,
    });

    if (!playlist) {
      throw new HttpException("Update data failed.", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    await this.flushAllCache();
    return playlist;
  }

  /**
   * remove playlist
   * @param {string} id
   */
  async remove(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new HttpException("Provided ID is not valid.", HttpStatus.BAD_REQUEST);
    }

    const playlist = await this.playlistModel.findByIdAndDelete(id);

    if (!playlist) {
      throw new HttpException("Playlist not found.", HttpStatus.NOT_FOUND);
    }

    await this.flushAllCache();
    return { message: "Playlist deleted successfully" };
  }

  /**
   * get active playlists
   * @returns array of active playlists
   */
  async getActivePlaylists(storeCode?: string) {
    const cacheKey = !storeCode ? "playlist-all" : `playlist-${storeCode}`;
    const ids = await this._findStoreGroupIds(storeCode);
    const cachedPlaylists = await this.redisService.get(cacheKey);

    if (cachedPlaylists) {
      return JSON.parse(cachedPlaylists);
    }

    const currentDate = new Date();
    const filter: Record<string, any> = {
      startDate: { $lte: currentDate },
      endDate: { $gte: currentDate },
      $or: [{ store_group: { $exists: false } }, { store_group: { $size: 0 } }, { store_group: { $in: ids } }],
    };

    if (storeCode) {
      filter.storeCode = storeCode;
    }

    const playlists = await this.playlistModel.find(filter).sort({ ordering: -1 });

    if (playlists.length > 0) {
      await this.redisService.set(
        cacheKey,
        JSON.stringify(playlists),
        "EX",
        process.env.REDIS_PLAYLIST_CACHED_LIFETIME || 3600,
      );
    } else {
      await this.redisService.del(cacheKey);
    }

    return playlists;
  }

  /**
   * flush all cache
   */
  async flushAllCache() {
    const keys = await this.redisService.keys("playlist-*");
    if (keys.length > 0) {
      await this.redisService.del(...keys);
    }
    return true;
  }

  async _findStoreGroupIds(storeCode: string) {
    const ids = [];

    if (storeCode) {
      const storeGroups = await this.storeGroupService.getStoreGroups(storeCode);

      if (!storeGroups.length) {
        return ids;
      }

      storeGroups.map((sg) => {
        ids.push(sg._id);
      });
    }

    return ids;
  }
}
