import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes, Types } from "mongoose";
import * as mongoosePaginate from "mongoose-paginate-v2";
import { PlaylistType } from "../enum/playlist-type.enum";

function setImageUrl(img_url) {
  return img_url.split(process.env.AWS_S3_BASE_URL)[1];
}

function getImageUrl(img_url) {
  return process.env.AWS_S3_BASE_URL + img_url;
}

@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class Playlist extends Document {
  @Prop({ required: true, type: String })
  title: string;

  @Prop({ required: true, type: Date })
  startDate: Date;

  @Prop({ required: true, type: Date })
  endDate: Date;

  @Prop({ required: true, type: String, get: getImageUrl, set: setImageUrl })
  url: string;

  @Prop({ required: true, type: String, enum: PlaylistType })
  type: PlaylistType;

  @Prop({ required: false, type: Number, default: 0 })
  ordering: number;

  @Prop({ required: false, type: [SchemaTypes.ObjectId] })
  store_group: Types.ObjectId[];
}

export type PlaylistDocument = Playlist & Document;

export const PlaylistSchema = SchemaFactory.createForClass(Playlist);

PlaylistSchema.plugin(mongoosePaginate);
