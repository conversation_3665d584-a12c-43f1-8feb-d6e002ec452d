export enum ChannelHeader {
  WEB = "web",
  MOBILE = "mobile",
  POS = "pos",
  BC = "bc",
  MARKETPLACE = "marketplace",
  WAC = "wac",
}
export type ChannelHeaderType = keyof typeof ChannelHeader;

export const ChannelHeaderReverse = {
  web: "34999",
  mobile: "34997",
  marketplace: "34996",
  lazada: "35902",
  elevania: "35903",
  blibli: "35904",
  shopee: "35905",
  tokopedia: "35906",
  jd_indonesia: "35907",
  zalora: "35908",
  b2b: "35909",
  tiktok: "35910",
  sociolla: "60001",
};
