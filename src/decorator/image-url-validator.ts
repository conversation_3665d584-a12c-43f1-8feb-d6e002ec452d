import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from "class-validator";

@ValidatorConstraint()
export class IsImageUrlRegisteredConstraint implements ValidatorConstraintInterface {
  validate(imageUrl: string, args: ValidationArguments) {
    const imageBaseUrls = [
      process.env.IMAGE_BASE_URL,
      process.env.IMAGE_BASE_URL_POS,
      process.env.AWS_S3_BASE_URL,
      ...process.env.CUSTOM_IMAGE_URL.split(","),
    ];
    return imageBaseUrls.some((url) => imageUrl.includes(url));
  }
}

export function IsImageUrlRegistered(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsImageUrlRegisteredConstraint,
    });
  };
}
