import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from "class-validator";

@ValidatorConstraint()
export class ValidDateConstraint implements ValidatorConstraintInterface {
  validate(date: string, args: ValidationArguments) {
    return /^(1[0-9]{3}|2[0-9]{3})-(0[0-9]|1[0-2])-([0-2][0-9]|3[0-1])$/.test(date);
  }

  defaultMessage(args: ValidationArguments) {
    return `${args.property} must be in valid YYYY-MM-DD format`;
  }
}

export function ValidDate(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: ValidDateConstraint,
    });
  };
}
