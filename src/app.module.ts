import { Module } from "@nestjs/common";
import { APP_GUARD } from "@nestjs/core";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { MongooseModule, MongooseModuleOptions } from "@nestjs/mongoose";
import { ScheduleModule } from "@nestjs/schedule";
import {
  AuthGuard,
  InternalAccessGuard,
  KeycloakConnectModule,
  PolicyEnforcementMode,
  ResourceGuard,
  RoleGuard,
  TokenValidation,
} from "keycloak-connect-tbs";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { TbsSiteConfigModule } from "tbs-site-config";
import { Amazons3Module } from "./amazon-s3/amazons3.module";
import { AppConfigModule } from "./common/app-config.module";
import { AppConfigService } from "./common/app-config.service";
import { BannerFooterModule } from "./module/banner-footer/banner-footer.module";
import { BlogModule } from "./module/blog/blog.module";
import { BuilderIoModule } from "./module/builderio/builderio.module";
import { CacheManagerModule } from "./module/cache-manager/cache-manager.module";
import { CarbonModule } from "./module/carbon/carbon.module";
import { CarouselModule } from "./module/carousel/carousel.module";
import { CrmModule } from "./module/crm/crm.module";
import { EmailNotifModule } from "./module/email-notif/email-notif.module";
import { FileUploaderModule } from "./module/file/file-uploader.module";
import { HealthCheckModule } from "./module/health-check/health-check.module";
import { HomeModule } from "./module/home/<USER>";
import { KeycloakGeneratorModule } from "./module/keycloak-generator/keycloak-generator.module";
import { KibanaDashboardModule } from "./module/kibana-dashboard/kibana-dashboard.module";
import { MegaMenuModule } from "./module/mega-menu/mega-menu.module";
import { NewsletterModule } from "./module/newsletter/newsletter.module";
import { OffersModule } from "./module/offers/offers.module";
import { PlaylistModule } from "./module/playlist/playlist.module";
import { FcmPublisherModule } from "./module/push-notif/fcm-publisher/fcm-publisher.module";
import { FcmUserMapperModule } from "./module/push-notif/fcm-user-mapper/fcm-user-mapper.module";
import { InboxModule } from "./module/push-notif/inbox/inbox.module";
import { NotifManagementModule } from "./module/push-notif/notif-management/notif-management.module";
import { PushNotifSenderModule } from "./module/push-notif/push-notif-sender/push-notif-sender.module";
import { ScreenModule } from "./module/screen/screen.module";
import { SiteConfig } from "./module/site-configs/schema/site-config.schema";
import { SiteConfigsModule } from "./module/site-configs/site-configs.module";
import { StoreGroupModule } from "./module/store-group/store-group.module";
import { StoreModule } from "./module/store/store.module";
import { UnsubscribeModule } from "./module/unsubscribe/unsubscribe.module";
import { UrlShortenerModule } from "./module/url-shortener/url-shortener.module";
// import { StoreGroupModule } from "./module/store-group/store-group.module";
import { InStoreServiceModule } from "./module/in-store-service/in-store-service.module";

@Module({
  imports: [
    KeycloakConnectModule.register({
      authServerUrl: process.env.KEYCLOAK_HOST,
      clientId: process.env.KEYCLOAK_CLIENTID,
      secret: process.env.KEYCLOAK_SECRET,
      realm: process.env.KEYCLOAK_REALM,
      policyEnforcement: PolicyEnforcementMode.PERMISSIVE,
      tokenValidation: TokenValidation.ONLINE,
      internalUrls: process.env.INTERNAL_ACCESS_URL.split(","),
      app_port: +process.env.APPS_PORT,
      bypass_iss_check: Boolean(process.env.KEYCLOAK_BYPASS_ISS),
      // Secret key of the client taken from keycloak server
    }),
    MongooseModule.forRootAsync({
      imports: [AppConfigModule],
      inject: [AppConfigService],
      useFactory: async (appConfigService: AppConfigService) => {
        const uri = await appConfigService.connectionString;
        const options: MongooseModuleOptions = {
          uri: uri,
          dbName: "tbs_db_utils",
          useNewUrlParser: true,
          useUnifiedTopology: true,
        };
        return options;
      },
    }),
    TbsSiteConfigModule.registerAsync({
      options: { host: process.env.REDIS_HOST, port: +process.env.REDIS_PORT, db: +process.env.REDIS_CONFIG_DB },
      prefix: process.env.KAFKA_TOPIC_PREFIX,
      isMaster: true,
      mongoDbUrl: process.env.MONGO_URL,
      mongoDbName: "tbs_db_utils",
      mongoDbCollection: SiteConfig.name.toLowerCase() + "s",
    }),
    AppConfigModule,
    StoreModule,
    FileUploaderModule,
    Amazons3Module,
    SiteConfigsModule,
    CarbonModule,
    CarouselModule,
    OffersModule,
    HomeModule,
    NewsletterModule,
    BlogModule,
    HealthCheckModule,
    KeycloakGeneratorModule,
    ApmModule.register(),
    MegaMenuModule,
    ScreenModule,
    UrlShortenerModule,
    BuilderIoModule,
    PushNotifSenderModule,
    KibanaDashboardModule,
    UnsubscribeModule,
    CacheManagerModule,
    BannerFooterModule,
    FcmPublisherModule,
    FcmUserMapperModule,
    InboxModule,
    EventEmitterModule.forRoot(),
    CrmModule,
    ScheduleModule.forRoot(),
    NotifManagementModule,
    EmailNotifModule,
    // SendPortalModule,
    StoreGroupModule,
    InStoreServiceModule,
    PlaylistModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: InternalAccessGuard,
    },
    // This adds a global level authentication guard,
    // you can also have it scoped
    // if you like.
    //
    // Will return a 401 unauthorized when it is unable to
    // verify the JWT token or Bearer header is missing.
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    // This adds a global level resource guard, which is permissive.
    // Only controllers annotated with @Resource and
    // methods with @Scopes
    // are handled by this guard.
    {
      provide: APP_GUARD,
      useClass: ResourceGuard,
    },
    // New in 1.1.0
    // This adds a global level role guard, which is permissive.
    // Used by `@Roles` decorator with the
    // optional `@AllowAnyRole` decorator for allowing any
    // specified role passed.
    {
      provide: APP_GUARD,
      useClass: RoleGuard,
    },
  ],
})
export class AppModule {}
