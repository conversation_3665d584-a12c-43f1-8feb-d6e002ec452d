import { Module } from "@nestjs/common";
import { APP_GUARD } from "@nestjs/core";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { MongooseModule, MongooseModuleOptions } from "@nestjs/mongoose";
import { ScheduleModule } from "@nestjs/schedule";
import {
  AuthGuard,
  InternalAccessGuard,
  KeycloakConnectModule,
  PolicyEnforcementMode,
  ResourceGuard,
  RoleGuard,
  TokenValidation,
} from "keycloak-connect-tbs";
import { ApmModule } from "modules/nestjs-elastic-apm";
import { TbsSiteConfigModule } from "tbs-site-config";
import { Amazons3Module } from "./amazon-s3/amazons3.module";
import { AppConfigModule } from "./common/app-config.module";
import { AppConfigService } from "./common/app-config.service";
import { CacheManagerModule } from "./module/cache-manager/cache-manager.module";
import { HealthCheckModule } from "./module/health-check/health-check.module";

@Module({
  imports: [
    KeycloakConnectModule.register({
      authServerUrl: process.env.KEYCLOAK_HOST,
      clientId: process.env.KEYCLOAK_CLIENTID,
      secret: process.env.KEYCLOAK_SECRET,
      realm: process.env.KEYCLOAK_REALM,
      policyEnforcement: PolicyEnforcementMode.PERMISSIVE,
      tokenValidation: TokenValidation.ONLINE,
      internalUrls: process.env.INTERNAL_ACCESS_URL.split(","),
      app_port: +process.env.APPS_PORT,
      bypass_iss_check: Boolean(process.env.KEYCLOAK_BYPASS_ISS),
      // Secret key of the client taken from keycloak server
    }),
    MongooseModule.forRootAsync({
      imports: [AppConfigModule],
      inject: [AppConfigService],
      useFactory: async (appConfigService: AppConfigService) => {
        const uri = await appConfigService.connectionString;
        const options: MongooseModuleOptions = {
          uri: uri,
          dbName: "tbs_db_utils",
          useNewUrlParser: true,
          useUnifiedTopology: true,
        };
        return options;
      },
    }),
    // TbsSiteConfigModule.registerAsync({
    //   options: { host: process.env.REDIS_HOST, port: +process.env.REDIS_PORT, db: +process.env.REDIS_CONFIG_DB },
    //   prefix: process.env.KAFKA_TOPIC_PREFIX,
    //   isMaster: true,
    //   mongoDbUrl: process.env.MONGO_URL,
    //   mongoDbName: "tbs_db_utils",
    //   mongoDbCollection: SiteConfig.name.toLowerCase() + "s",
    // }),
    AppConfigModule,
    Amazons3Module,
    HealthCheckModule,
    ApmModule.register(),
    CacheManagerModule,
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: InternalAccessGuard,
    },
    // This adds a global level authentication guard,
    // you can also have it scoped
    // if you like.
    //
    // Will return a 401 unauthorized when it is unable to
    // verify the JWT token or Bearer header is missing.
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    // This adds a global level resource guard, which is permissive.
    // Only controllers annotated with @Resource and
    // methods with @Scopes
    // are handled by this guard.
    {
      provide: APP_GUARD,
      useClass: ResourceGuard,
    },
    // New in 1.1.0
    // This adds a global level role guard, which is permissive.
    // Used by `@Roles` decorator with the
    // optional `@AllowAnyRole` decorator for allowing any
    // specified role passed.
    {
      provide: APP_GUARD,
      useClass: RoleGuard,
    },
  ],
})
export class AppModule {}
