- hosts: localhost
  tasks:
  - name: Deploy the application
    k8s:
      state: present
      validate_certs: no
      namespace: "{{ namespace }}"
      definition: "{{ lookup('template', 'deployment.yml') | from_yaml }}"
  - name: Deploy the application worker
    k8s:
      state: present
      validate_certs: no
      namespace: "{{ namespace }}"
      definition: "{{ lookup('template', 'deployment-worker.yml') | from_yaml }}"