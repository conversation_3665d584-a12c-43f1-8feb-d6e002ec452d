apiVersion: apps/v1 #  for k8s versions before 1.9.0 use apps/v1beta2  and before 1.8.0 use extensions/v1beta1
kind: Deployment
metadata:
  # This name uniquely identifies the Deployment
  name: be-api-utils-worker
spec:
  selector:
    matchLabels:
      app: be-api-utils-worker
  replicas: 1
  revisionHistoryLimit: 3 
  template:
    metadata:
      annotations:
        co.elastic.logs/enabled: "true"
        co.elastic.logs/exclude_lines: '.*VERBOSE.*'
      labels:
        # Label is used as selector in the service.
        app: be-api-utils-worker
    spec:
      # Refer to the PVC created earlier
      imagePullSecrets:
      - name: tbsi-jfrog
      hostAliases:
      - ip: "**************"
        hostnames:
        - "keycloak.sit.tbsgroup.co.id"
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              app: be-api-utils-worker
      containers:
      - name: be-api-utils-worker
        image: {{ image_registry }}/{{ image_id }}
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        resources:
         limits:
           cpu: 1000m
           memory: 1024Mi
         requests:
           cpu: 150m
           memory: 128Mi
        readinessProbe:
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 1
          successThreshold: 2
          failureThreshold: 3
          httpGet:
            host:
            scheme: HTTP
            path: /api/v1/health-check
            port: 3000
        livenessProbe:
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 1
          successThreshold: 1
          failureThreshold: 3
          httpGet:
            host:
            scheme: HTTP
            path: /api/v1/health-check
            port: 3000