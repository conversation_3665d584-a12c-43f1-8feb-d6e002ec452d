export declare const transformAllowedSize: (size: string) => number;
export declare const ConvertUrlToPath: (fileurl: string) => string;
export declare const ConvertPathToUrl: (filepath: string) => string;
export declare const ConvertPathToUrlChildren: (images: any) => any;
export declare const ConvertUrlToPathChildren: (images: any) => any;
export declare const ConvertToDate: (value: string) => Date;
interface IPaginateFormat {
    data: Array<Record<string, any>>;
    page: number;
    limit: number;
    total: number;
    offset: number;
}
export declare const PaginateFormat: (params: IPaginateFormat) => {
    docs: Record<string, any>[];
    totalDocs: number;
    limit: number;
    totalPages: number;
    page: number;
    pagingCounter: number;
    hasPrevPage: boolean;
    hasNextPage: boolean;
    prevPage: number;
    nextPage: number;
};
export declare const HeadingSlash: (path: any) => any;
export {};
