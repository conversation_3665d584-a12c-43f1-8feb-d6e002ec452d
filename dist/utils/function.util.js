"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HeadingSlash = exports.PaginateFormat = exports.ConvertToDate = exports.ConvertUrlToPathChildren = exports.ConvertPathToUrlChildren = exports.ConvertPathToUrl = exports.ConvertUrlToPath = exports.transformAllowedSize = void 0;
const transformAllowedSize = (size) => {
    const [value, unit] = size.split(" ");
    let res;
    if (!value && !unit) {
        throw new Error("Format params invalid");
    }
    switch (unit) {
        case "kb":
        case "kilobyte":
        case "kilobytes":
            res = Number(value) * 1024;
            break;
        case "mb":
        case "megabyte":
        case "megabytes":
            res = Number(value) * 1024 * 1024;
            break;
        case "gb":
        case "gigabyte":
        case "gigabytes":
            res = Number(value) * 1024 * 1024 * 1024;
            break;
        default:
            res = Number(value);
    }
    return res;
};
exports.transformAllowedSize = transformAllowedSize;
const ConvertUrlToPath = (fileurl) => {
    if (fileurl !== null && fileurl !== undefined) {
        return fileurl.split(process.env.AWS_S3_BASE_URL)[1];
    }
};
exports.ConvertUrlToPath = ConvertUrlToPath;
const ConvertPathToUrl = (filepath) => {
    if (filepath !== null && filepath !== undefined) {
        return process.env.AWS_S3_BASE_URL + filepath;
    }
};
exports.ConvertPathToUrl = ConvertPathToUrl;
const ConvertPathToUrlChildren = (images) => {
    return images.map((image) => ({
        img_url: process.env.AWS_S3_BASE_URL + image.img_url,
        path_url: image.path_url,
    }));
};
exports.ConvertPathToUrlChildren = ConvertPathToUrlChildren;
const ConvertUrlToPathChildren = (images) => {
    return images.map((image) => ({
        img_url: image.img_url.split(process.env.AWS_S3_BASE_URL)[1],
        path_url: image.path_url,
    }));
};
exports.ConvertUrlToPathChildren = ConvertUrlToPathChildren;
const ConvertToDate = (value) => {
    return value ? new Date(value) : null;
};
exports.ConvertToDate = ConvertToDate;
const PaginateFormat = (params) => {
    const { data, limit, page, total, offset } = params;
    const totalPage = Math.ceil(total / limit);
    return {
        docs: data,
        totalDocs: total,
        limit: limit,
        totalPages: totalPage,
        page: page,
        pagingCounter: offset + 1,
        hasPrevPage: page > 1,
        hasNextPage: page < totalPage,
        prevPage: page > 1 ? page - 1 : null,
        nextPage: page < totalPage ? page + 1 : null,
    };
};
exports.PaginateFormat = PaginateFormat;
const HeadingSlash = (path) => {
    return path.substring(0, 1) == "/" ? path : "/" + path;
};
exports.HeadingSlash = HeadingSlash;
//# sourceMappingURL=function.util.js.map