"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customOriginValidation = void 0;
const customOriginValidation = (origin, cb) => {
    const allowedOrigins = process.env.CORS_DOMAIN.split(",");
    if (!origin)
        return cb(null, true);
    if (!allowedOrigins.includes(origin)) {
        const msg = "The CORS policy for this site does not " + "allow access from the specified Origin.";
        return cb(new Error(msg), false);
    }
    return cb(null, true);
};
exports.customOriginValidation = customOriginValidation;
//# sourceMappingURL=custom-origin-validation.js.map