"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const response_interceptor_1 = require("./response.interceptor");
const custom_origin_validation_1 = require("./utils/custom-origin-validation");
const microservices_1 = require("@nestjs/microservices");
const logger = new common_1.Logger();
function bootstrap() {
    return __awaiter(this, void 0, void 0, function* () {
        const app = yield core_1.NestFactory.create(app_module_1.AppModule);
        app.enableCors({
            methods: process.env.CORS_METHOD,
            origin: custom_origin_validation_1.customOriginValidation,
        });
        app.useGlobalPipes(new common_1.ValidationPipe());
        app.useGlobalInterceptors(new response_interceptor_1.ResponseInterceptor());
        app.setGlobalPrefix("api", { exclude: [":shortUrl"] });
        app.enableVersioning({
            type: common_1.VersioningType.URI,
            defaultVersion: "1",
        });
        app.enableShutdownHooks();
        const config = new swagger_1.DocumentBuilder()
            .setTitle("TBS Microservice API")
            .setDescription("The Body Shop : Analytics Microservice API")
            .setVersion("0.1")
            .addBearerAuth({
            description: `[just text field] Please enter token in following format: Bearer <JWT>`,
            name: "Authorization",
            bearerFormat: "Bearer",
            scheme: "Bearer",
            type: "http",
            in: "Header",
        }, "access-token")
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup("docs", app, document, {
            swaggerOptions: {
                docExpansion: "none",
                persistAuthorization: true,
                displayRequestDuration: true,
            },
        });
        app.connectMicroservice({
            transport: microservices_1.Transport.KAFKA,
            options: {
                client: {
                    brokers: process.env.KAFKA_HOST.split(","),
                },
                consumer: {
                    groupId: process.env.KAFKA_GROUP_ID,
                    allowAutoTopicCreation: true,
                    heartbeatInterval: 10000,
                },
            },
        });
        yield app.startAllMicroservices();
        yield app.listen(process.env.APPS_PORT || 3000).then(() => {
            logger.log("Running on port: " + process.env.APPS_PORT);
        });
    });
}
bootstrap();
//# sourceMappingURL=main.js.map