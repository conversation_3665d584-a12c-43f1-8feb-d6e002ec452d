"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const nestjs_elastic_apm_1 = require("../../../modules/nestjs-elastic-apm");
const payment_module_1 = require("../microservices/payments/payment.module");
const admin_store_controller_1 = require("./admin-store.controller");
const gold_store_schema_1 = require("./schema/gold-store.schema");
const store_schema_1 = require("./schema/store.schema");
const store_controller_1 = require("./store.controller");
const store_service_1 = require("./store.service");
const store_group_module_1 = require("../store-group/store-group.module");
let StoreModule = class StoreModule {
};
StoreModule = __decorate([
    (0, common_1.Module)({
        controllers: [store_controller_1.StoreController, admin_store_controller_1.AdminStoreController],
        providers: [store_service_1.StoreService],
        imports: [
            mongoose_1.MongooseModule.forFeature([
                {
                    name: store_schema_1.Store.name,
                    schema: store_schema_1.StoreSchema,
                },
                {
                    name: "GoldStore",
                    schema: gold_store_schema_1.GoldStoreSchema,
                },
            ]),
            nestjs_elastic_apm_1.ApmModule.register(),
            payment_module_1.PaymentModule,
            (0, common_1.forwardRef)(() => store_group_module_1.StoreGroupModule),
        ],
        exports: [store_service_1.StoreService],
    })
], StoreModule);
exports.StoreModule = StoreModule;
//# sourceMappingURL=store.module.js.map