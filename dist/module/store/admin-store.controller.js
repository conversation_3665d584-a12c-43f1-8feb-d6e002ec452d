"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminStoreController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const enum_1 = require("../enum");
const create_store_dto_1 = require("./dto/create-store.dto");
const filter_store_dto_1 = require("./dto/filter-store.dto");
const update_store_dto_1 = require("./dto/update-store.dto");
const store_service_1 = require("./store.service");
let AdminStoreController = class AdminStoreController {
    constructor(storeService) {
        this.storeService = storeService;
    }
    create(payload) {
        return this.storeService.create(payload);
    }
    findAll(filter) {
        return this.storeService.findAllAdmin(filter, filter.page, filter.limit, filter.sort, filter.radius);
    }
    findOne(id) {
        return this.storeService.findById(id);
    }
    update(id, updateStoreDto) {
        return this.storeService.update(id, updateStoreDto);
    }
    remove(id) {
        return this.storeService.remove(id);
    }
    syncGoldStore() {
        return this.storeService.syncStoreGold();
    }
    syncDataEmployee(storeCode) {
        return this.storeService.syncSingleStore(storeCode);
    }
};
__decorate([
    (0, common_1.Post)(),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.POST),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_store_dto_1.CreateStoreDto]),
    __metadata("design:returntype", void 0)
], AdminStoreController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        description: `
    things to search with keyword:
    - store name: (substring)
    - store code: (exact match)
    - city: (substring)
    - state: (substring)

    *search methods:
    exact match = you're expected to provide a complete data to query. i.e: 14144 (store code)
    substring = partial and insensitive-case query is allowed. i.e: MEDAN/medan/Medan (city); Boemi Kedaton Lampung (store name)
    `,
    }),
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.GET),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_store_dto_1.FilterStoreDto]),
    __metadata("design:returntype", void 0)
], AdminStoreController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.GET),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminStoreController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.PATCH),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_store_dto_1.UpdateStoreDto]),
    __metadata("design:returntype", void 0)
], AdminStoreController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.DELETE),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminStoreController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)("sync-gold-store"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AdminStoreController.prototype, "syncGoldStore", null);
__decorate([
    (0, common_1.Post)("sync-gold-store/:storeCode"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.POST),
    __param(0, (0, common_1.Param)("storeCode")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminStoreController.prototype, "syncDataEmployee", null);
AdminStoreController = __decorate([
    (0, swagger_1.ApiTags)("Admin - Stores"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)("admin/store"),
    (0, keycloak_connect_tbs_1.Resource)(enum_1.Controllers.ADMIN_STORE),
    __metadata("design:paramtypes", [store_service_1.StoreService])
], AdminStoreController);
exports.AdminStoreController = AdminStoreController;
//# sourceMappingURL=admin-store.controller.js.map