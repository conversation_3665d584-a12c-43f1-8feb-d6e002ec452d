import { PaginateModel } from "mongoose";
import { TbsSiteConfigService } from "tbs-site-config";
import { PaymentService } from "../microservices/payments/payment.service";
import { StoreGroupService } from "../store-group/store-group.service";
import { CreateStoreDto } from "./dto/create-store.dto";
import { FilterStoreDto } from "./dto/filter-store.dto";
import { UpdateStoreDto } from "./dto/update-store.dto";
import { GoldStoreDocument } from "./schema/gold-store.schema";
import { Store, StoreDocument } from "./schema/store.schema";
export declare class StoreService {
    private storeModel;
    private goldStoreModel;
    private readonly paymentService;
    private readonly storeGroupService;
    private readonly siteConfigService;
    constructor(storeModel: PaginateModel<StoreDocument>, goldStoreModel: PaginateModel<GoldStoreDocument>, paymentService: PaymentService, storeGroupService: StoreGroupService, siteConfigService: TbsSiteConfigService);
    create(createStoreDto: CreateStoreDto): Promise<Store>;
    distance(lat1: any, lon1: any, lat2: any, lon2: any): number;
    findAll(filter: FilterStoreDto, page: number, limit: number, sort: string, radius: number): Promise<any>;
    findAllAdmin(filter: FilterStoreDto, page: number, limit: number, sort: string, radius: number): Promise<any>;
    findByStoreCode(storeCode: string): Promise<any>;
    findById(id: string): Promise<any>;
    update(id: string, payload: UpdateStoreDto): Promise<any>;
    remove(id: string): Promise<any>;
    syncStoreGold(): Promise<void>;
    syncSingleStore(storeCode: string): Promise<void>;
    findAllStorePricings(): Promise<any>;
    getAllStoreCodes(): Promise<any[]>;
    private _getConfig;
    findAllV2(filter: FilterStoreDto, page: number, limit: number, sort: string, radius: number): Promise<any>;
    findAllAdminV2(filter: FilterStoreDto, page: number, limit: number, sort: string, radius: number): Promise<any>;
}
