"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const filter_store_dto_1 = require("./dto/filter-store.dto");
const store_service_1 = require("./store.service");
let StoreController = class StoreController {
    constructor(storeService) {
        this.storeService = storeService;
    }
    findAll(filter) {
        return this.storeService.findAll(filter, filter.page, filter.limit, filter.sort, filter.radius);
    }
    findAllV2(filter) {
        return this.storeService.findAllV2(filter, filter.page, filter.limit, filter.sort, filter.radius);
    }
    findAllStorePricings() {
        return this.storeService.findAllStorePricings();
    }
    findOne(id) {
        return this.storeService.findById(id);
    }
};
__decorate([
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_store_dto_1.FilterStoreDto]),
    __metadata("design:returntype", void 0)
], StoreController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)("v2"),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    (0, swagger_1.ApiOperation)({
        description: "Get stores with support for in-store service filtering. Can filter by single serviceId or multiple serviceIds",
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_store_dto_1.FilterStoreDto]),
    __metadata("design:returntype", void 0)
], StoreController.prototype, "findAllV2", null);
__decorate([
    (0, common_1.Get)("/pricings"),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], StoreController.prototype, "findAllStorePricings", null);
__decorate([
    (0, swagger_1.ApiTags)("rate-limit:strict"),
    (0, common_1.Get)(":id"),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StoreController.prototype, "findOne", null);
StoreController = __decorate([
    (0, swagger_1.ApiTags)("Stores"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)("store"),
    __metadata("design:paramtypes", [store_service_1.StoreService])
], StoreController);
exports.StoreController = StoreController;
//# sourceMappingURL=store.controller.js.map