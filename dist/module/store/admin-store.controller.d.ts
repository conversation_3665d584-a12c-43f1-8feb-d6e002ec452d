import { CreateStoreDto } from "./dto/create-store.dto";
import { FilterStoreDto } from "./dto/filter-store.dto";
import { UpdateStoreDto } from "./dto/update-store.dto";
import { StoreService } from "./store.service";
export declare class AdminStoreController {
    private readonly storeService;
    constructor(storeService: StoreService);
    create(payload: CreateStoreDto): Promise<import("./schema/store.schema").Store>;
    findAll(filter: FilterStoreDto): Promise<any>;
    findOne(id: string): Promise<any>;
    update(id: string, updateStoreDto: UpdateStoreDto): Promise<any>;
    remove(id: string): Promise<any>;
    syncGoldStore(): Promise<void>;
    syncDataEmployee(storeCode: string): Promise<void>;
}
