import { FilterStoreDto } from "./dto/filter-store.dto";
import { StoreService } from "./store.service";
export declare class StoreController {
    private readonly storeService;
    constructor(storeService: StoreService);
    findAll(filter: FilterStoreDto): Promise<any>;
    findAllV2(filter: FilterStoreDto): Promise<any>;
    findAllStorePricings(): Promise<any>;
    findOne(id: string): Promise<any>;
}
