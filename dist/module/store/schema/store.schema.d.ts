/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { Document, Schema as MongooseSchema } from "mongoose";
import { StoreClass } from "src/module/enum/store-class.enum";
import { InStoreService } from "src/module/in-store-service/schema/in-store-service.schema";
export declare class Store extends Document {
    storeCode: string;
    name: string;
    position: number;
    description: string;
    image: string;
    address: string;
    phone: string;
    email: string;
    countryId: string;
    city: string;
    state: string;
    lat: number;
    lng: number;
    vat: boolean;
    ppn: number;
    status: number;
    storeClass: StoreClass;
    location: Record<string, any>;
    pkpNo: string;
    pkpDate: string;
    ptkpName: string;
    ptkpAddress: string;
    midBca: string;
    merchantName: string;
    terminalId: string;
    closingDate: Date;
    edc: Record<string, any>;
    pricing_type: string;
    inStoreServices: InStoreService[];
}
export type StoreDocument = Store & Document;
export declare const StoreSchema: MongooseSchema<Store, import("mongoose").Model<Store, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Store>;
