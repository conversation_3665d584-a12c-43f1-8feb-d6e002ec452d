"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreSchema = exports.Store = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoosePaginate = require("mongoose-paginate-v2");
const store_class_enum_1 = require("../../enum/store-class.enum");
const function_util_1 = require("../../../utils/function.util");
const in_store_service_schema_1 = require("../../in-store-service/schema/in-store-service.schema");
let Store = class Store extends mongoose_2.Document {
    constructor() {
        super(...arguments);
        this.edc = [];
    }
};
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true, type: String, trim: true }),
    __metadata("design:type", String)
], Store.prototype, "storeCode", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Store.prototype, "position", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ get: function_util_1.ConvertPathToUrl, set: function_util_1.ConvertUrlToPath }),
    __metadata("design:type", String)
], Store.prototype, "image", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "address", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "phone", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "email", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "countryId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "city", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "state", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Store.prototype, "lat", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Store.prototype, "lng", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], Store.prototype, "vat", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Store.prototype, "ppn", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 1 }),
    __metadata("design:type", Number)
], Store.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String, enum: store_class_enum_1.StoreClass }),
    __metadata("design:type", String)
], Store.prototype, "storeClass", void 0);
__decorate([
    (0, mongoose_1.Prop)((0, mongoose_1.raw)({
        type: { type: String, default: "Point" },
        coordinates: { type: [Number] },
    })),
    __metadata("design:type", Object)
], Store.prototype, "location", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "pkpNo", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "pkpDate", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "ptkpName", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "ptkpAddress", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "midBca", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "merchantName", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "terminalId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], Store.prototype, "closingDate", void 0);
__decorate([
    (0, mongoose_1.Prop)((0, mongoose_1.raw)({
        paymentMethodCode: { type: String },
        paymentMethodName: { type: String },
        merchantName: { type: String },
        tid: { type: String },
        mid: { type: String },
    })),
    __metadata("design:type", Object)
], Store.prototype, "edc", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Store.prototype, "pricing_type", void 0);
__decorate([
    (0, mongoose_1.Prop)([{ type: mongoose_2.Schema.Types.ObjectId, ref: in_store_service_schema_1.InStoreService.name }]),
    __metadata("design:type", Array)
], Store.prototype, "inStoreServices", void 0);
Store = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        toJSON: { getters: true },
        toObject: { getters: true },
    })
], Store);
exports.Store = Store;
exports.StoreSchema = mongoose_1.SchemaFactory.createForClass(Store);
exports.StoreSchema.plugin(mongoosePaginate);
exports.StoreSchema.index({ location: "2dsphere" });
exports.StoreSchema.index({ inStoreServices: 1 });
//# sourceMappingURL=store.schema.js.map