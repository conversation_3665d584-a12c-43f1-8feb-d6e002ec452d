"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const dayjs = require("dayjs");
const timezone = require("dayjs/plugin/timezone");
const utc = require("dayjs/plugin/utc");
const mongoose_2 = require("mongoose");
const tbs_site_config_1 = require("tbs-site-config");
const channel_header_enum_1 = require("../enum/channel-header.enum");
const store_class_enum_1 = require("../enum/store-class.enum");
const payment_service_1 = require("../microservices/payments/payment.service");
const store_group_service_1 = require("../store-group/store-group.service");
const store_schema_1 = require("./schema/store.schema");
dayjs.extend(utc);
dayjs.extend(timezone);
let StoreService = class StoreService {
    constructor(storeModel, goldStoreModel, paymentService, storeGroupService, siteConfigService) {
        this.storeModel = storeModel;
        this.goldStoreModel = goldStoreModel;
        this.paymentService = paymentService;
        this.storeGroupService = storeGroupService;
        this.siteConfigService = siteConfigService;
    }
    create(createStoreDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const existingStore = yield this.storeModel.findOne({ name: createStoreDto.name }).exec();
            if (existingStore) {
                throw new common_1.HttpException("Store is already exist", common_1.HttpStatus.BAD_REQUEST);
            }
            if (createStoreDto.edc) {
                for (const edc of createStoreDto.edc) {
                    const paymentMethod = yield this.paymentService.verifyPayment(edc.paymentMethodCode, channel_header_enum_1.ChannelHeader.POS);
                    if (!paymentMethod) {
                        throw new common_1.HttpException("Payment method not found", common_1.HttpStatus.BAD_REQUEST);
                    }
                    edc["paymentMethodName"] = paymentMethod.name;
                }
            }
            const newStore = new this.storeModel(createStoreDto);
            newStore.location = {
                type: "Point",
                coordinates: [createStoreDto.lng, createStoreDto.lat],
            };
            yield newStore.save();
            yield this.storeGroupService.refreshAllStore();
            return newStore;
        });
    }
    distance(lat1, lon1, lat2, lon2) {
        const p = 0.017453292519943295;
        const c = Math.cos;
        const a = 0.5 - c((lat2 - lat1) * p) / 2 + (c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p))) / 2;
        return 12742 * Math.asin(Math.sqrt(a));
    }
    findAll(filter, page = 1, limit = 10, sort, radius) {
        return __awaiter(this, void 0, void 0, function* () {
            const maxDistanceInMeters = radius ? radius : 10000;
            let filterLocation = {};
            let filterKeyword = {};
            if (filter.keyword) {
                filterKeyword = {
                    $or: [
                        { name: { $regex: filter.keyword, $options: "i" } },
                        { storeCode: filter.keyword },
                        { city: { $regex: filter.keyword, $options: "i" } },
                    ],
                };
            }
            if (filter.lat && filter.lng) {
                filterLocation = {
                    location: {
                        $near: {
                            $geometry: {
                                type: "Point",
                                coordinates: [filter.lng, filter.lat],
                            },
                            $maxDistance: maxDistanceInMeters,
                        },
                    },
                };
            }
            const filterStoreClass = { storeClass: { $nin: [store_class_enum_1.StoreClass.BAZAAR, store_class_enum_1.StoreClass.OUTLET, store_class_enum_1.StoreClass.TestMarket] } };
            const filterStatus = { status: 1 };
            const query = Object.assign({}, filterKeyword, filterLocation, filterStoreClass, filterStatus);
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
                lean: true,
            };
            const result = yield this.storeModel.paginate(query, options);
            result.docs.forEach((e) => {
                Object.assign(e, {
                    distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
                });
            });
            return result;
        });
    }
    findAllAdmin(filter, page = 1, limit = 10, sort, radius) {
        return __awaiter(this, void 0, void 0, function* () {
            const maxDistanceInMeters = radius ? radius : 10000;
            let filterLocation = {};
            let filterKeyword = {};
            if (filter.keyword) {
                filterKeyword = {
                    $or: [
                        { name: { $regex: filter.keyword, $options: "i" } },
                        { city: { $regex: filter.keyword, $options: "i" } },
                        { state: { $regex: filter.keyword, $options: "i" } },
                        { storeCode: filter.keyword },
                    ],
                };
            }
            if (filter.lat && filter.lng) {
                filterLocation = {
                    location: {
                        $near: {
                            $geometry: {
                                type: "Point",
                                coordinates: [filter.lng, filter.lat],
                            },
                            $maxDistance: maxDistanceInMeters,
                        },
                    },
                };
            }
            const query = Object.assign({}, filterKeyword, filterLocation);
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            const result = yield this.storeModel.paginate(query, options);
            result.docs.forEach((e) => {
                Object.assign(e, {
                    distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
                });
            });
            return result;
        });
    }
    findByStoreCode(storeCode) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.storeModel.findOne({ storeCode: storeCode });
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            let store;
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                store = yield this.storeModel.findOne({ storeCode: id }).populate("inStoreServices");
            }
            else {
                store = this.storeModel.findById(id).populate("inStoreServices");
            }
            if (!store) {
                throw new common_1.HttpException("Store tidak ditemukan", common_1.HttpStatus.BAD_REQUEST);
            }
            return store;
        });
    }
    update(id, payload) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            if (payload.edc) {
                for (const edc of payload.edc) {
                    const paymentMethod = yield this.paymentService.verifyPayment(edc.paymentMethodCode, channel_header_enum_1.ChannelHeader.POS);
                    if (!paymentMethod) {
                        throw new common_1.HttpException("Payment method not found", common_1.HttpStatus.BAD_REQUEST);
                    }
                    edc["paymentMethodName"] = paymentMethod.name;
                }
            }
            if (payload.lat && payload.lng) {
                payload.location = {
                    type: "Point",
                    coordinates: [payload.lng, payload.lat],
                };
            }
            const update = yield this.storeModel.updateOne({ _id: id }, payload).exec();
            if (!update) {
                throw "Update data failed.";
            }
            yield this.storeGroupService.refreshAllStore();
            return this.storeModel.findById(id);
        });
    }
    remove(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const deleting = yield this.storeModel.deleteOne({ _id: id }).exec();
            yield this.storeGroupService.refreshAllStore();
            return deleting;
        });
    }
    syncStoreGold() {
        return __awaiter(this, void 0, void 0, function* () {
            const ppn = yield this._getConfig("store.ppn", 11);
            const goldStores = yield this.goldStoreModel.find();
            console.log(`Running sync store : ${goldStores.length}`);
            for (const goldStore of goldStores) {
                try {
                    const lat = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[0].trim()) : 0.0;
                    const lng = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[1].trim()) : 0.0;
                    const payload = {
                        storeCode: goldStore.storeCode,
                        storeClass: goldStore.storeClass,
                        name: goldStore.name,
                        address: `${(goldStore.address1, goldStore.address2)}`,
                        phone: goldStore.phone,
                        email: goldStore.email,
                        countryId: "ID",
                        city: goldStore.city,
                        lat: lat,
                        lng: lng,
                        status: dayjs().isAfter(dayjs(goldStore.closingDate)) ? 0 : 1,
                        vat: goldStore.vat,
                        ppn: goldStore.vat ? ppn : 0,
                        location: {
                            type: "Point",
                            coordinates: [lng, lat],
                        },
                        pkpNo: goldStore.pkpNo,
                        pkpDate: dayjs(goldStore.pkpDate).tz("Asia/Jakarta").format("DD/MM/YYYY"),
                        ptkpName: goldStore.ptkpName,
                        ptkpAddress: goldStore.ptkpAddress,
                        closingDate: goldStore.closingDate,
                    };
                    yield this.storeModel.findOneAndUpdate({ storeCode: goldStore.storeCode }, payload, {
                        new: true,
                        upsert: true,
                    });
                    yield this.storeGroupService.refreshAllStore();
                }
                catch (e) {
                    console.log(`Error sync store :${goldStore.storeCode} - ${goldStore.name}, Error : ${e}`);
                }
            }
        });
    }
    syncSingleStore(storeCode) {
        return __awaiter(this, void 0, void 0, function* () {
            const ppn = this._getConfig("store.ppn", 11);
            const goldStore = yield this.goldStoreModel.findOne({ storeCode: storeCode });
            const lat = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[0].trim()) : 0.0;
            const lng = goldStore.longitude != "-" ? parseFloat(goldStore.longitude.split(",")[1].trim()) : 0.0;
            const payload = {
                storeCode: goldStore.storeCode,
                storeClass: goldStore.storeClass,
                name: goldStore.name,
                address: `${(goldStore.address1, goldStore.address2)}`,
                phone: goldStore.phone,
                email: goldStore.email,
                countryId: "ID",
                city: goldStore.city,
                lat: lat,
                lng: lng,
                status: dayjs().isAfter(dayjs(goldStore.closingDate)) ? 0 : 1,
                vat: goldStore.vat,
                ppn: goldStore.vat ? ppn : 0,
                location: {
                    type: "Point",
                    coordinates: [lng, lat],
                },
                pkpNo: goldStore.pkpNo,
                pkpDate: dayjs(goldStore.pkpDate).tz("Asia/Jakarta").format("DD/MM/YYYY"),
                ptkpName: goldStore.ptkpName,
                ptkpAddress: goldStore.ptkpAddress,
                closingDate: goldStore.closingDate,
            };
            const store = yield this.storeModel.findOneAndUpdate({ storeCode: goldStore.storeCode }, payload, {
                new: true,
                upsert: true,
            });
            yield this.storeGroupService.refreshAllStore();
            console.info(`Sync update store : ${store.storeCode} - ${store.name}`);
        });
    }
    findAllStorePricings() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.storeModel.find({ status: 1, pricing_type: { $exists: true } }, "pricing_type storeCode name");
        });
    }
    getAllStoreCodes() {
        return __awaiter(this, void 0, void 0, function* () {
            const result = [];
            const contents = yield this.storeModel.find({ status: 1 }, { _id: 0, storeCode: 1 }).exec();
            contents.map((k) => {
                result.push(k.storeCode);
            });
            const specialChannel = Object.keys(channel_header_enum_1.ChannelHeaderReverse);
            specialChannel.map((ss) => {
                result.push(channel_header_enum_1.ChannelHeaderReverse[ss]);
            });
            return result;
        });
    }
    _getConfig(key, _default) {
        return __awaiter(this, void 0, void 0, function* () {
            let result;
            try {
                const config = yield this.siteConfigService.get(key);
                result = JSON.parse(config).value;
            }
            catch (err) {
                result = _default;
            }
            return result;
        });
    }
    findAllV2(filter, page = 1, limit = 10, sort, radius) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const maxDistanceInMeters = radius ? radius : 10000;
            let filterLocation = {};
            let filterKeyword = {};
            let filterService = {};
            if (filter.keyword) {
                filterKeyword = {
                    $or: [
                        { name: { $regex: filter.keyword, $options: "i" } },
                        { storeCode: filter.keyword },
                        { city: { $regex: filter.keyword, $options: "i" } },
                    ],
                };
            }
            if (filter.lat && filter.lng) {
                filterLocation = {
                    location: {
                        $near: {
                            $geometry: {
                                type: "Point",
                                coordinates: [filter.lng, filter.lat],
                            },
                            $maxDistance: maxDistanceInMeters,
                        },
                    },
                };
            }
            if (filter.serviceId) {
                filterService = {
                    inStoreServices: filter.serviceId,
                };
            }
            if ((_a = filter.serviceIds) === null || _a === void 0 ? void 0 : _a.length) {
                filterService = {
                    inStoreServices: { $all: filter.serviceIds },
                };
            }
            const filterStoreClass = { storeClass: { $nin: [store_class_enum_1.StoreClass.BAZAAR, store_class_enum_1.StoreClass.OUTLET, store_class_enum_1.StoreClass.TestMarket] } };
            const filterStatus = { status: 1 };
            const query = Object.assign({}, filterKeyword, filterLocation, filterService, filterStoreClass, filterStatus);
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
                populate: "inStoreServices",
            };
            const result = yield this.storeModel.paginate(query, options);
            if (filter.lat && filter.lng) {
                result.docs.forEach((e) => {
                    Object.assign(e, {
                        distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
                    });
                });
            }
            return result;
        });
    }
    findAllAdminV2(filter, page = 1, limit = 10, sort, radius) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const maxDistanceInMeters = radius ? radius : 10000;
            let filterLocation = {};
            let filterKeyword = {};
            let filterService = {};
            if (filter.keyword) {
                filterKeyword = {
                    $or: [
                        { name: { $regex: filter.keyword, $options: "i" } },
                        { city: { $regex: filter.keyword, $options: "i" } },
                        { state: { $regex: filter.keyword, $options: "i" } },
                        { storeCode: filter.keyword },
                    ],
                };
            }
            if (filter.lat && filter.lng) {
                filterLocation = {
                    location: {
                        $near: {
                            $geometry: {
                                type: "Point",
                                coordinates: [filter.lng, filter.lat],
                            },
                            $maxDistance: maxDistanceInMeters,
                        },
                    },
                };
            }
            if (filter.serviceId) {
                filterService = {
                    inStoreServices: filter.serviceId,
                };
            }
            if ((_a = filter.serviceIds) === null || _a === void 0 ? void 0 : _a.length) {
                filterService = {
                    inStoreServices: { $all: filter.serviceIds },
                };
            }
            const query = Object.assign({}, filterKeyword, filterLocation, filterService);
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
                populate: "inStoreServices",
            };
            const result = yield this.storeModel.paginate(query, options);
            if (filter.lat && filter.lng) {
                result.docs.forEach((e) => {
                    Object.assign(e, {
                        distance: this.distance(filter.lat, filter.lng, e.lat, e.lng),
                    });
                });
            }
            return result;
        });
    }
};
StoreService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(store_schema_1.Store.name)),
    __param(1, (0, mongoose_1.InjectModel)("GoldStore")),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => store_group_service_1.StoreGroupService))),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, typeof (_b = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _b : Object, payment_service_1.PaymentService,
        store_group_service_1.StoreGroupService, typeof (_c = typeof tbs_site_config_1.TbsSiteConfigService !== "undefined" && tbs_site_config_1.TbsSiteConfigService) === "function" ? _c : Object])
], StoreService);
exports.StoreService = StoreService;
//# sourceMappingURL=store.service.js.map