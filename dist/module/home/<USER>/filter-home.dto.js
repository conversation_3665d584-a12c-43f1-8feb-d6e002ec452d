"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterHomeV2Dto = exports.FilterHomeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const home_platform_enum_1 = require("../enum/home-platform.enum");
const home_section_enum_1 = require("../enum/home-section.enum");
class FilterHomeDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, enum: home_platform_enum_1.HomePlatformEnum }),
    __metadata("design:type", String)
], FilterHomeDto.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, enum: home_section_enum_1.HomeSectionEnum }),
    __metadata("design:type", String)
], FilterHomeDto.prototype, "section", void 0);
exports.FilterHomeDto = FilterHomeDto;
class FilterHomeV2Dto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, enum: home_platform_enum_1.PlatformEnum }),
    __metadata("design:type", String)
], FilterHomeV2Dto.prototype, "platform", void 0);
exports.FilterHomeV2Dto = FilterHomeV2Dto;
//# sourceMappingURL=filter-home.dto.js.map