"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeService = void 0;
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const ioredis_1 = require("ioredis");
const mongoose_2 = require("mongoose");
const rxjs_1 = require("rxjs");
const banner_footer_service_1 = require("../banner-footer/banner-footer.service");
const blog_collection_service_1 = require("../blog/blog-collection.service");
const builderio_service_1 = require("../builderio/builderio.service");
const carousel_service_1 = require("../carousel/carousel.service");
const crm_service_1 = require("../crm/crm.service");
const product_group_tag_enum_1 = require("../enum/product-group-tag.enum");
const users_service_1 = require("../microservices/users/users.service");
const site_config_service_1 = require("../site-configs/site-config.service");
const home_platform_enum_1 = require("./enum/home-platform.enum");
const purge_enum_1 = require("./enum/purge.enum");
let HomeService = class HomeService {
    constructor(model, megaMenuModel, contentService, carouselService, httpService, blogCollectionService, redisService, usersService, configService, bannerFooterService, crmService, siteConfigService) {
        this.model = model;
        this.megaMenuModel = megaMenuModel;
        this.contentService = contentService;
        this.carouselService = carouselService;
        this.httpService = httpService;
        this.blogCollectionService = blogCollectionService;
        this.redisService = redisService;
        this.usersService = usersService;
        this.configService = configService;
        this.bannerFooterService = bannerFooterService;
        this.crmService = crmService;
        this.siteConfigService = siteConfigService;
    }
    findAll(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.model
                .find({
                status: "Active",
                section: payload.section,
                platform: { $in: [payload.platform, home_platform_enum_1.HomePlatformEnum.All] },
            })
                .sort({ position: 1 })
                .lean()
                .exec();
        });
    }
    getHome(payload, purgeOption, req) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.getUserInfo(req, purgeOption, false);
            const tier = !user || (user === null || user === void 0 ? void 0 : user.is_public) ? "PUBLIC" : user.customerGroup;
            const cacheKey = `home/${tier}/${payload.platform}`;
            if (purgeOption == purge_enum_1.PurgeEnum.keep) {
                const home = yield this.redisService.get(cacheKey);
                if (home)
                    return JSON.parse(home);
            }
            const homeContent = yield this._getLayout(payload.platform, req);
            const res = Object.keys(homeContent);
            const f = [];
            let prod = {};
            res.map((i) => {
                if (i != "products") {
                    if (i === "range") {
                        homeContent[i].see_all =
                            process.env.PRODUCT_SERVICE_URL +
                                `/api/v1/product-group?page=1&limit=10&tag=${product_group_tag_enum_1.ProductGroupTag.Range}&is_lean=false`;
                    }
                    if (i === "skin_type") {
                        homeContent[i].see_all =
                            process.env.PRODUCT_SERVICE_URL +
                                `/api/v1/product-group?page=1&limit=10&tag=${product_group_tag_enum_1.ProductGroupTag.SkinType}&is_lean=false`;
                    }
                    f.push(homeContent[i]);
                }
                else {
                    prod = homeContent[i];
                }
            });
            const banner_footer = yield this._getBannerFooter();
            const response = { docs: f, products: prod, banner_footer };
            yield this.redisService.set(cacheKey, JSON.stringify(response), "EX", process.env.REDIS_HOME_CACHED_LIFETIME);
            return response;
        });
    }
    _getBannerFooter() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.bannerFooterService.getBannerForHome();
        });
    }
    _getLayout(platform, req) {
        return __awaiter(this, void 0, void 0, function* () {
            const homeLayoutWeb = yield this.siteConfigService.findByKey("homeConfig.web");
            const homeLayoutMobile = yield this.siteConfigService.findByKey("homeConfig.mobile");
            const layout = JSON.parse(platform == home_platform_enum_1.PlatformEnum.Web ? homeLayoutWeb.value : homeLayoutMobile.value);
            const storeCode = platform == home_platform_enum_1.PlatformEnum.Web ? 34999 : 34997;
            const keys = Object.keys(layout);
            const result = {};
            let group = {};
            return new Promise((resolve) => __awaiter(this, void 0, void 0, function* () {
                const collectionType = [];
                for (const section of keys) {
                    result[section] = {
                        type: section,
                        title: layout[section],
                        content: yield this._getContent(section, storeCode, req),
                    };
                }
                if (collectionType.length) {
                    group = yield this._getProductCollection(layout, storeCode, req);
                }
                resolve(Object.assign(Object.assign({}, result), group));
            }));
        });
    }
    _getContent(layout, storeCode, req) {
        return __awaiter(this, void 0, void 0, function* () {
            switch (layout) {
                case "carousel":
                    return yield this.carouselService.findAllPublic();
                case "blog":
                    return yield this.getBlogContents();
                case "promo":
                case "offer":
                    return yield this._getProductCollection(layout, storeCode, req);
                case "range":
                    return yield this._getAllRange(storeCode, req, product_group_tag_enum_1.ProductGroupTag.Range);
                case "skin_type":
                    return yield this._getAllRange(storeCode, req, product_group_tag_enum_1.ProductGroupTag.SkinType);
                case "crm":
                    return yield this._getCRMContents();
                default:
                    break;
            }
        });
    }
    _getBuilderIoContent() {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.contentService.findAllPublished({ tags: "home", limit: 3 });
            return result.docs;
        });
    }
    getBlogContents() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.blogCollectionService.getActiveBlogs();
        });
    }
    _getProductCollection(layout, storeCode, req) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = `${this.configService.get("OMS_HOST")}/api/v1/product-collection/full?type=${layout}&storeCode=${storeCode}`;
                const result = {};
                const headers = req.headers;
                const responseData = yield (0, rxjs_1.lastValueFrom)(this.httpService.get(url, { headers }).pipe((0, rxjs_1.map)((response) => response.data.data)));
                return responseData;
            }
            catch (e) {
                console.error(e);
            }
        });
    }
    _getAllRange(storeCode, req, type) {
        return __awaiter(this, void 0, void 0, function* () {
            const host = this.configService.get("OMS_HOST");
            const url = `${host}/api/v1/product-group?page=1&limit=10&tag=${type}&is_lean=true`;
            try {
                const requestUrl = url;
                const requestConfig = {
                    headers: {
                        Authorization: req.headers.authorization,
                    },
                };
                const responseData = yield (0, rxjs_1.lastValueFrom)(this.httpService.get(requestUrl, requestConfig).pipe((0, rxjs_1.map)((response) => {
                    return response.data;
                })));
                return responseData.data.docs;
            }
            catch (e) {
                console.error(e);
                throw new common_1.HttpException(e, e.response.status);
            }
        });
    }
    _getCRMContents() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.crmService.findActiveContents();
        });
    }
    _getUserId(token) {
        const base64Payload = token.split(".")[1];
        const payload = Buffer.from(base64Payload, "base64");
        return JSON.parse(payload.toString()).uid;
    }
    getUserInfo(req, purge, isAdmin) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            if (!((_a = req === null || req === void 0 ? void 0 : req.headers) === null || _a === void 0 ? void 0 : _a.authorization))
                return { is_public: true };
            if (isAdmin)
                return { is_public: true };
            const userid = this._getUserId((_b = req === null || req === void 0 ? void 0 : req.headers) === null || _b === void 0 ? void 0 : _b.authorization);
            const cacheKey = `products/users/${userid}`;
            if (purge == purge_enum_1.PurgeEnum.keep) {
                const userDetails = yield this.redisService.get(cacheKey);
                if (userDetails)
                    return JSON.parse(userDetails);
            }
            const userData = yield this.usersService.getProfile(req, isAdmin);
            yield this.redisService.set(cacheKey, JSON.stringify(userData.data), "EX", process.env.REDIS_URL_CHECKER_CACHED_LIFETIME);
            return userData.data;
        });
    }
};
HomeService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Home")),
    __param(1, (0, mongoose_1.InjectModel)("MegaMenu")),
    __param(6, (0, nestjs_redis_1.InjectRedis)()),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        builderio_service_1.BuilderIoService,
        carousel_service_1.CarouselService, typeof (_a = typeof axios_1.HttpService !== "undefined" && axios_1.HttpService) === "function" ? _a : Object, blog_collection_service_1.BlogCollectionService, typeof (_b = typeof ioredis_1.default !== "undefined" && ioredis_1.default) === "function" ? _b : Object, users_service_1.UsersService, typeof (_c = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _c : Object, banner_footer_service_1.BannerFooterService,
        crm_service_1.CrmService,
        site_config_service_1.SiteConfigService])
], HomeService);
exports.HomeService = HomeService;
//# sourceMappingURL=home.service.js.map