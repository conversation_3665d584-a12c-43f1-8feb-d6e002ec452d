import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import Redis from "ioredis";
import { Model } from "mongoose";
import { BannerFooterService } from "../banner-footer/banner-footer.service";
import { BlogCollectionService } from "../blog/blog-collection.service";
import { BuilderIoService } from "../builderio/builderio.service";
import { CarouselService } from "../carousel/carousel.service";
import { CrmService } from "../crm/crm.service";
import { MegaMenuDocument } from "../mega-menu/schema/mega-menu.schema";
import { UsersService } from "../microservices/users/users.service";
import { SiteConfigService } from "../site-configs/site-config.service";
import { FilterHomeDto, FilterHomeV2Dto } from "./dto/filter-home.dto";
import { PurgeEnum } from "./enum/purge.enum";
import { HomeDocument } from "./schema/home.schema";
export declare class HomeService {
    private model;
    private megaMenuModel;
    private readonly contentService;
    private readonly carouselService;
    private readonly httpService;
    private readonly blogCollectionService;
    private readonly redisService;
    private readonly usersService;
    private configService;
    private bannerFooterService;
    private crmService;
    private readonly siteConfigService;
    constructor(model: Model<HomeDocument>, megaMenuModel: Model<MegaMenuDocument>, contentService: BuilderIoService, carouselService: CarouselService, httpService: HttpService, blogCollectionService: BlogCollectionService, redisService: Redis, usersService: UsersService, configService: ConfigService, bannerFooterService: BannerFooterService, crmService: CrmService, siteConfigService: SiteConfigService);
    findAll(payload: FilterHomeDto): Promise<import("mongoose").LeanDocument<import("./schema/home.schema").Home & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    getHome(payload: FilterHomeV2Dto, purgeOption: any, req: any): Promise<any>;
    private _getBannerFooter;
    private _getLayout;
    private _getContent;
    private _getBuilderIoContent;
    private getBlogContents;
    private _getProductCollection;
    private _getAllRange;
    private _getCRMContents;
    _getUserId(token: any): any;
    getUserInfo(req: any, purge: PurgeEnum, isAdmin?: boolean): Promise<any>;
}
