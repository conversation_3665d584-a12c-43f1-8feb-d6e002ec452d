import { FilterHomeDto, FilterHomeV2Dto } from "./dto/filter-home.dto";
import { HomeService } from "./home.service";
export declare class HomeController {
    private readonly homeService;
    constructor(homeService: HomeService);
    findAll(payload: FilterHomeDto): Promise<import("mongoose").LeanDocument<import("./schema/home.schema").Home & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    fetch(req: any, payload: FilterHomeV2Dto): Promise<any>;
}
