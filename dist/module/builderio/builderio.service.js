"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuilderIoService = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const rxjs_1 = require("rxjs");
const model_enum_1 = require("./enum/model-enum");
let BuilderIoService = class BuilderIoService {
    constructor(contentModel, httpService) {
        this.contentModel = contentModel;
        this.httpService = httpService;
        this.fromTime = 0;
    }
    syncBuilderIoModel(lastnhour) {
        return __awaiter(this, void 0, void 0, function* () {
            console.time("- sync builder io");
            this.fromTime = 0;
            if (lastnhour > 0) {
                this.fromTime = this._getUnixtimestamp(lastnhour);
            }
            const models = Object.values(model_enum_1.ModelEnum);
            for (let index = 0; index < models.length; index++) {
                try {
                    const res = yield this._syncModelContents(models[index]);
                }
                catch (err) {
                    console.error(`error synch builderio: ${err}`);
                }
            }
            console.timeEnd("- sync builder io");
        });
    }
    _getUnixtimestamp(lastnhour) {
        if (!lastnhour || lastnhour <= 0) {
            return 0;
        }
        const date = new Date();
        date.setHours(date.getHours() - lastnhour + date.getTimezoneOffset() / 60);
        return date.getTime();
    }
    _syncModelContents(model) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve) => __awaiter(this, void 0, void 0, function* () {
                let inc = 0;
                let page = true;
                let totalContent = 0;
                do {
                    const result = yield this._getBuilderIoContent(model, inc);
                    if (!result || !result.results.length) {
                        page = false;
                        break;
                    }
                    const documents = [];
                    for (const doc of result.results) {
                        const { blocks } = doc, _doc = __rest(doc, ["blocks"]);
                        if (_doc.data.thumbnail) {
                            documents.push(_doc);
                            continue;
                        }
                        const newThumbnail = this._findFirstImage(doc);
                        _doc.data.thumbnail = newThumbnail;
                        documents.push(_doc);
                    }
                    totalContent += documents.length;
                    yield this._createContent(model, documents);
                    inc += Number(process.env.BUILDERIO_CONTENT_LIMIT);
                } while (page);
                resolve(`Sync model ${model} done, imported ${totalContent}`);
            }));
        });
    }
    _createContent(model, result) {
        return __awaiter(this, void 0, void 0, function* () {
            result.forEach((item) => __awaiter(this, void 0, void 0, function* () {
                yield this._execute(model, item);
            }));
        });
    }
    _execute(model, item) {
        return __awaiter(this, void 0, void 0, function* () {
            const currentContent = yield this.contentModel.findOne({ content_id: item.id });
            if (currentContent) {
                currentContent.published = item.published || "";
                if (item.data) {
                    currentContent.thumbnail = item.data.thumbnail || "";
                    currentContent.title = item.data.title || "";
                    currentContent.description = item.data.description || "";
                    currentContent.url = item.data.url || "";
                    currentContent.tags = item.data.tags || [];
                    currentContent.position = item.data.position || 0;
                }
                if (item.lastUpdated) {
                    currentContent.updatedDate = new Date(item.lastUpdated);
                }
                yield currentContent.save();
                return;
            }
            const content = {
                model: model[0].toUpperCase() + model.slice(1),
                content_id: item.id,
                published: item.published || "",
                createdDate: new Date(item.createdDate),
                updatedDate: item.lastUpdated ? new Date(item.lastUpdated) : new Date(),
            };
            if (item.data) {
                content["thumbnail"] = item.data.thumbnail || "";
                content["title"] = item.data.title || "";
                content["url"] = item.data.url || "";
                content["tags"] = item.data.tags || [];
                content["position"] = item.data.position || 0;
            }
            const newScreen = new this.contentModel(content);
            return newScreen.save();
        });
    }
    _getBuilderIoContent(model, offset) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const fields = `id,createdBy,data.thumbnail,data.title,data.description,data.tags,data.url,data.position,published,createdDate,lastUpdated,data.blocks`;
                let url = `${process.env.BUILDERIO_API_URL}/content/${model}?apiKey=${process.env.BUILDERIO_PUBLIC_API}&userAttributes&limit=${process.env.BUILDERIO_CONTENT_LIMIT}&offset=${offset}&fields=${fields}&omit&includeUnpublished=true&includeRefs&cacheSeconds=0&sort`;
                if (this.fromTime > 0) {
                    url +=
                        "&query.$or[0]={createdDate:{$gte:" +
                            this.fromTime +
                            "}}&query.$or[1]={lastUpdated:{$gte:" +
                            this.fromTime +
                            "}}";
                }
                const responseData = yield (0, rxjs_1.lastValueFrom)(this.httpService.get(url).pipe((0, rxjs_1.map)((response) => {
                    return response.data;
                })));
                return responseData;
            }
            catch (err) {
                console.error(err);
                return false;
            }
        });
    }
    findAllAdmin(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort, keyword } = params;
            if (keyword) {
                filter["$or"] = [
                    { title: { $regex: keyword, $options: "i" } },
                    { url: { $regex: keyword, $options: "i" } }
                ];
            }
            if (params.model) {
                filter.model = params.model[0].toUpperCase() + params.model.slice(1);
            }
            if (params.published) {
                filter.published = params.published;
            }
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            const result = yield this.contentModel.paginate(filter, options);
            return result;
        });
    }
    findAllPublished(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            const arrFilter = [];
            if (params.tags) {
                arrFilter.push({ tags: new RegExp(params.tags.replace(",", "|"), "i") });
            }
            if (params.keyword) {
                arrFilter.push({ title: new RegExp(params.keyword, "gi") });
            }
            if (arrFilter.length > 0) {
                filter.$and = arrFilter;
            }
            filter.published = "published";
            filter.model = { $in: ["Blog", "Page"] };
            filter.is_visible = { $ne: false };
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            const result = yield this.contentModel.paginate(filter, options);
            return result;
        });
    }
    findByPath(param) {
        return __awaiter(this, void 0, void 0, function* () {
            let path = param.path;
            if (path[0] != "/") {
                path = "/" + path;
            }
            const filter = { url: path, published: "published" };
            const result = yield this.contentModel.findOne(filter);
            if (!result) {
                throw new common_1.HttpException(`data not found`, common_1.HttpStatus.NOT_FOUND);
            }
            return result;
        });
    }
    findTags() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.contentModel.find().distinct("tags");
        });
    }
    findUrlKeyPublished() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const filter = { published: "published" };
                const project = { url: 1, _id: 0 };
                return yield this.contentModel.find(filter, project).lean();
            }
            catch (error) {
                throw new common_1.HttpException(error, common_1.HttpStatus.BAD_REQUEST);
            }
        });
    }
    updateContentVisibility(id, payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.contentModel.findByIdAndUpdate(id, payload, { new: true });
        });
    }
    _findFirstImage(blocks) {
        const blockString = JSON.stringify(blocks);
        const imageBlocks = blockString.split(`"image":"`);
        if (imageBlocks.length < 2)
            return "";
        const [firstImg] = imageBlocks[1].split(`"`);
        return firstImg;
    }
};
BuilderIoService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Content")),
    __metadata("design:paramtypes", [Object, axios_1.HttpService])
], BuilderIoService);
exports.BuilderIoService = BuilderIoService;
//# sourceMappingURL=builderio.service.js.map