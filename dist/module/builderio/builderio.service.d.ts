/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { HttpService } from "@nestjs/axios";
import { PaginateModel } from "mongoose";
import { GetContentPathDto } from "./dto/get-content-path.dto";
import { GetContentDto, GetContentPublicDto } from "./dto/get-content.dto";
import { ContentDocument } from "./schema/content.schema";
import { UpdateContentVisibilityDto } from "./dto/update-content-visibility.dto";
export declare class BuilderIoService {
    private contentModel;
    private readonly httpService;
    constructor(contentModel: PaginateModel<ContentDocument>, httpService: HttpService);
    private fromTime;
    syncBuilderIoModel(lastnhour?: number): Promise<void>;
    private _getUnixtimestamp;
    private _syncModelContents;
    private _createContent;
    private _execute;
    private _getBuilderIoContent;
    findAllAdmin(params: GetContentDto): Promise<import("mongoose").PaginateResult<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>>;
    findAllPublished(params: GetContentPublicDto): Promise<import("mongoose").PaginateResult<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>>;
    findByPath(param: GetContentPathDto): Promise<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    findTags(): Promise<any[]>;
    findUrlKeyPublished(): Promise<import("mongoose").LeanDocument<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    updateContentVisibility(id: string, payload: UpdateContentVisibilityDto): Promise<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    _findFirstImage(blocks: Array<any>): string;
}
