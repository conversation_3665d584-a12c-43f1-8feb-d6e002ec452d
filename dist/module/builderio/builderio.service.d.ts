import { HttpService } from "@nestjs/axios";
import { PaginateModel } from "mongoose";
import { GetContentPathDto } from "./dto/get-content-path.dto";
import { GetContentDto, GetContentPublicDto } from "./dto/get-content.dto";
import { ContentDocument } from "./schema/content.schema";
import { UpdateContentVisibilityDto } from "./dto/update-content-visibility.dto";
export declare class BuilderIoService {
    private contentModel;
    private readonly httpService;
    constructor(contentModel: PaginateModel<ContentDocument>, httpService: HttpService);
    private fromTime;
    syncBuilderIoModel(lastnhour?: number): Promise<void>;
    private _getUnixtimestamp;
    private _syncModelContents;
    private _createContent;
    private _execute;
    private _getBuilderIoContent;
    findAllAdmin(params: GetContentDto): Promise<any>;
    findAllPublished(params: GetContentPublicDto): Promise<any>;
    findByPath(param: GetContentPathDto): Promise<any>;
    findTags(): Promise<any>;
    findUrlKeyPublished(): Promise<any>;
    updateContentVisibility(id: string, payload: UpdateContentVisibilityDto): Promise<any>;
    _findFirstImage(blocks: Array<any>): string;
}
