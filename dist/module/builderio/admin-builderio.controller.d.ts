import { BuilderIoService } from "./builderio.service";
import { GetContentDto } from "./dto/get-content.dto";
import { UpdateContentVisibilityDto } from "./dto/update-content-visibility.dto";
export declare class AdminBuilderIoController {
    private readonly builderIoService;
    constructor(builderIoService: BuilderIoService);
    find(pagination: GetContentDto): Promise<any>;
    updateVisibility(id: string, payload: UpdateContentVisibilityDto): Promise<any>;
}
