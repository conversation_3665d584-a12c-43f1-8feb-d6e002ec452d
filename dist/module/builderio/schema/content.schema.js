"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentSchema = exports.Content = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoosePaginate = require("mongoose-paginate-v2");
let Content = class Content extends mongoose_2.Document {
};
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String }),
    __metadata("design:type", String)
], Content.prototype, "content_id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Content.prototype, "thumbnail", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: String,
        get: (value) => {
            const words = value.split(" ");
            const upperCaseWords = words.map((word) => {
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            });
            return upperCaseWords.join(" ");
        },
    }),
    __metadata("design:type", String)
], Content.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Content.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Content.prototype, "url", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Content.prototype, "published", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Content.prototype, "model", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Array }),
    __metadata("design:type", Array)
], Content.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Date }),
    __metadata("design:type", Date)
], Content.prototype, "createdDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Date }),
    __metadata("design:type", Date)
], Content.prototype, "updatedDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Number }),
    __metadata("design:type", Number)
], Content.prototype, "position", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Boolean }),
    __metadata("design:type", Boolean)
], Content.prototype, "is_visible", void 0);
Content = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        toJSON: { getters: true },
        toObject: { getters: true },
    })
], Content);
exports.Content = Content;
exports.ContentSchema = mongoose_1.SchemaFactory.createForClass(Content);
exports.ContentSchema.plugin(mongoosePaginate);
//# sourceMappingURL=content.schema.js.map