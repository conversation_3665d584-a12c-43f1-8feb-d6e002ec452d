{"version": 3, "file": "content.schema.js", "sourceRoot": "", "sources": ["../../../../src/module/builderio/schema/content.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAAoC;AACpC,yDAAyD;AAOlD,IAAM,OAAO,GAAb,MAAM,OAAQ,SAAQ,mBAAQ;CA+CpC,CAAA;AA9CC;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;2CACpB;AAEnB;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;0CACtB;AAElB;IAAC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,GAAG,EAAE,CAAC,KAAa,EAAE,EAAE;YACrB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACxC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC;KACF,CAAC;;sCACY;AAEd;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;4CACpB;AAEpB;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;oCAC5B;AAEZ;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;0CACtB;AAElB;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;sCAC1B;AAEd;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;8BACjC,KAAK;qCAAM;AAEjB;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACzB,IAAI;4CAAC;AAElB;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACzB,IAAI;4CAAC;AAElB;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;yCACvB;AAEjB;IAAC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;2CACtB;AA9CR,OAAO;IALnB,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,IAAI;QAChB,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;QACzB,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;KAC5B,CAAC;GACW,OAAO,CA+CnB;AA/CY,0BAAO;AAmDP,QAAA,aAAa,GAAG,wBAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAEnE,qBAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC"}