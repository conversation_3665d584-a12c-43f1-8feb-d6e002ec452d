import { Document } from "mongoose";
export declare class Content extends Document {
    content_id: string;
    thumbnail: string;
    title: string;
    description: string;
    url: string;
    published: string;
    model: string;
    tags: Array<any>;
    createdDate: Date;
    updatedDate: Date;
    position: number;
    is_visible: boolean;
}
export type ContentDocument = Content & Document;
export declare const ContentSchema: any;
