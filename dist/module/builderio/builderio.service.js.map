{"version": 3, "file": "builderio.service.js", "sourceRoot": "", "sources": ["../../../src/module/builderio/builderio.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA4C;AAC5C,2CAAuE;AACvE,+CAA+C;AAE/C,+BAA0C;AAG1C,kDAA8C;AAKvC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YACkC,YAA4C,EAC3D,WAAwB;QADT,iBAAY,GAAZ,YAAY,CAAgC;QAC3D,gBAAW,GAAX,WAAW,CAAa;QAGnC,aAAQ,GAAG,CAAC,CAAC;IAFlB,CAAC;IAOE,kBAAkB,CAAC,SAAkB;;YACzC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAClB,IAAI,SAAS,GAAG,CAAC,EAAE;gBACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;aACnD;YAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,sBAAS,CAAC,CAAC;YACxC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAClD,IAAI;oBACF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC1D;gBAAC,OAAO,GAAG,EAAE;oBACZ,OAAO,CAAC,KAAK,CAAC,0BAA0B,GAAG,EAAE,CAAC,CAAC;iBAChD;aACF;YACD,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;KAAA;IAMO,iBAAiB,CAAC,SAAkB;QAC1C,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,EAAE;YAChC,OAAO,CAAC,CAAC;SACV;QACD,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAMa,kBAAkB,CAAC,KAAa;;YAC5C,OAAO,IAAI,OAAO,CAAC,CAAO,OAAO,EAAE,EAAE;gBACnC,IAAI,GAAG,GAAG,CAAC,CAAC;gBACZ,IAAI,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAI,YAAY,GAAG,CAAC,CAAC;gBACrB,GAAG;oBACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBAC3D,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE;wBACrC,IAAI,GAAG,KAAK,CAAC;wBACb,MAAM;qBACP;oBAED,MAAM,SAAS,GAAG,EAAE,CAAC;oBACrB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE;wBAChC,MAAM,EAAE,MAAM,KAAc,GAAG,EAAZ,IAAI,UAAK,GAAG,EAAzB,UAAmB,CAAM,CAAC;wBAChC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACvB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BACrB,SAAS;yBACV;wBACD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;wBAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;wBACnC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACtB;oBAED,YAAY,IAAI,SAAS,CAAC,MAAM,CAAC;oBACjC,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAE5C,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;iBACpD,QAAQ,IAAI,EAAE;gBACf,OAAO,CAAC,cAAc,KAAK,mBAAmB,YAAY,EAAE,CAAC,CAAC;YAChE,CAAC,CAAA,CAAC,CAAC;QACL,CAAC;KAAA;IAOa,cAAc,CAAC,KAAa,EAAE,MAAkB;;YAC5D,MAAM,CAAC,OAAO,CAAC,CAAO,IAAI,EAAE,EAAE;gBAC5B,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACnC,CAAC,CAAA,CAAC,CAAC;QACL,CAAC;KAAA;IAOa,QAAQ,CACpB,KAAa,EACb,IAMC;;YAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAChF,IAAI,cAAc,EAAE;gBAClB,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;gBAChD,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;oBACrD,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC7C,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;oBACzD,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;oBACzC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;oBAC3C,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;iBACnD;gBACD,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,cAAc,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBACzD;gBACD,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC5B,OAAO;aACR;YAED,MAAM,OAAO,GAAG;gBACd,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC9C,UAAU,EAAE,IAAI,CAAC,EAAE;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE;gBAC/B,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;gBACvC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;aACxE,CAAC;YAEF,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;gBACjD,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACzC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;gBACrC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBACvC,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;aAC/C;YACD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACjD,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC;QAC1B,CAAC;KAAA;IAOa,oBAAoB,CAAC,KAAa,EAAE,MAAc;;YAC9D,IAAI;gBACF,MAAM,MAAM,GAAG,wIAAwI,CAAC;gBACxJ,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,YAAY,KAAK,WAAW,OAAO,CAAC,GAAG,CAAC,oBAAoB,yBAAyB,OAAO,CAAC,GAAG,CAAC,uBAAuB,WAAW,MAAM,WAAW,MAAM,+DAA+D,CAAC;gBAEpQ,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;oBACrB,GAAG;wBACD,mCAAmC;4BACnC,IAAI,CAAC,QAAQ;4BACb,qCAAqC;4BACrC,IAAI,CAAC,QAAQ;4BACb,IAAI,CAAC;iBACR;gBAED,MAAM,YAAY,GAAG,MAAM,IAAA,oBAAa,EACtC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAC5B,IAAA,UAAG,EAAC,CAAC,QAAQ,EAAE,EAAE;oBACf,OAAO,QAAQ,CAAC,IAAI,CAAC;gBACvB,CAAC,CAAC,CACH,CACF,CAAC;gBACF,OAAO,YAAY,CAAC;aACrB;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACnB,OAAO,KAAK,CAAC;aACd;QACH,CAAC;KAAA;IAMK,YAAY,CAAC,MAAqB;;YACtC,MAAM,MAAM,GAAwB,EAAE,CAAC;YACvC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;YAEvD,IAAI,OAAO,EAAE;gBACX,MAAM,CAAC,KAAK,CAAC,GAAG;oBACd,EAAC,KAAK,EAAE,EAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAC,EAAC;oBACzC,EAAC,GAAG,EAAE,EAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAC,EAAC;iBACxC,CAAA;aACF;YACD,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChB,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACtE;YACD,IAAI,MAAM,CAAC,SAAS,EAAE;gBACpB,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;aACrC;YAED,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;gBAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;gBACpB,YAAY,EAAE,IAAI;gBAClB,IAAI,EAAE,IAAI;aACX,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEjE,OAAO,MAAM,CAAC;QAChB,CAAC;KAAA;IAMK,gBAAgB,CAAC,MAA2B;;YAChD,MAAM,MAAM,GAAwB,EAAE,CAAC;YACvC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;YAM9C,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,IAAI,MAAM,CAAC,IAAI,EAAE;gBAEf,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;aAC1E;YAED,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;aAC7D;YAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC;aACzB;YAED,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC;YAC/B,MAAM,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;gBAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;gBACpB,YAAY,EAAE,IAAI;gBAClB,IAAI,EAAE,IAAI;aACX,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEjE,OAAO,MAAM,CAAC;QAChB,CAAC;KAAA;IAEK,UAAU,CAAC,KAAwB;;YACvC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACtB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;gBAClB,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;aACnB;YACD,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC;YACrD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,IAAI,sBAAa,CAAC,gBAAgB,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;aACjE;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;KAAA;IAEK,QAAQ;;YACZ,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC;KAAA;IAEK,mBAAmB;;YACvB,IAAI;gBACF,MAAM,MAAM,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC;gBAC1C,MAAM,OAAO,GAAoC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBACpE,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;aAC7D;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,IAAI,sBAAa,CAAC,KAAK,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aACxD;QACH,CAAC;KAAA;IAEK,uBAAuB,CAAC,EAAU,EAAE,OAAmC;;YAC3E,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,EAAC,GAAG,EAAE,IAAI,EAAC,CAAC,CAAA;QAC5E,CAAC;KAAA;IAED,eAAe,CAAC,MAAkB;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACnD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,EAAE,CAAC;QAEtC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAjSY,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,SAAS,CAAC,CAAA;6CACO,mBAAW;GAHhC,gBAAgB,CAiS5B;AAjSY,4CAAgB"}