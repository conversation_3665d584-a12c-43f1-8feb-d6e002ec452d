"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminBuilderIoController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const role_enum_1 = require("../enum/role.enum");
const rbac_enum_1 = require("../enum/rbac.enum");
const builderio_service_1 = require("./builderio.service");
const get_content_dto_1 = require("./dto/get-content.dto");
const update_content_visibility_dto_1 = require("./dto/update-content-visibility.dto");
let AdminBuilderIoController = class AdminBuilderIoController {
    constructor(builderIoService) {
        this.builderIoService = builderIoService;
    }
    find(pagination) {
        return this.builderIoService.findAllAdmin(pagination);
    }
    updateVisibility(id, payload) {
        return this.builderIoService.updateContentVisibility(id, payload);
    }
};
__decorate([
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_content_dto_1.GetContentDto]),
    __metadata("design:returntype", void 0)
], AdminBuilderIoController.prototype, "find", null);
__decorate([
    (0, common_1.Patch)("/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.PATCH),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_content_visibility_dto_1.UpdateContentVisibilityDto]),
    __metadata("design:returntype", void 0)
], AdminBuilderIoController.prototype, "updateVisibility", null);
AdminBuilderIoController = __decorate([
    (0, swagger_1.ApiTags)("Admin - Builder Io"),
    (0, common_1.Controller)("admin/builderio"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, keycloak_connect_tbs_1.Resource)(rbac_enum_1.Controllers.ADMIN_BUILDERIO),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [role_enum_1.Role.Admin, `realm:app-${role_enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __metadata("design:paramtypes", [builderio_service_1.BuilderIoService])
], AdminBuilderIoController);
exports.AdminBuilderIoController = AdminBuilderIoController;
//# sourceMappingURL=admin-builderio.controller.js.map