"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuilderIoController = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const builderio_service_1 = require("./builderio.service");
const get_content_path_dto_1 = require("./dto/get-content-path.dto");
const get_content_dto_1 = require("./dto/get-content.dto");
const get_last_n_hour_dto_1 = require("./dto/get-last-n-hour.dto");
let BuilderIoController = class BuilderIoController {
    constructor(builderIoService, eventEmitter) {
        this.builderIoService = builderIoService;
        this.eventEmitter = eventEmitter;
    }
    sync(params) {
        return this.builderIoService.syncBuilderIoModel(params.lastnhour);
    }
    find(pagination) {
        return this.builderIoService.findAllPublished(pagination);
    }
    findTags() {
        return this.builderIoService.findTags();
    }
    findPath(param) {
        return this.builderIoService.findByPath(param);
    }
    findUrlKeyPublished() {
        return this.builderIoService.findUrlKeyPublished();
    }
};
__decorate([
    (0, common_1.Get)("/sync/"),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_last_n_hour_dto_1.GetLastNHourDto]),
    __metadata("design:returntype", void 0)
], BuilderIoController.prototype, "sync", null);
__decorate([
    (0, common_1.Get)("/content/"),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_content_dto_1.GetContentPublicDto]),
    __metadata("design:returntype", void 0)
], BuilderIoController.prototype, "find", null);
__decorate([
    (0, common_1.Get)("/tags/"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BuilderIoController.prototype, "findTags", null);
__decorate([
    (0, common_1.Get)("/search-path"),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_content_path_dto_1.GetContentPathDto]),
    __metadata("design:returntype", void 0)
], BuilderIoController.prototype, "findPath", null);
__decorate([
    (0, common_1.Get)("/url-key/published"),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BuilderIoController.prototype, "findUrlKeyPublished", null);
BuilderIoController = __decorate([
    (0, swagger_1.ApiTags)("Builder Io"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)("builderio"),
    __metadata("design:paramtypes", [builderio_service_1.BuilderIoService, typeof (_a = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _a : Object])
], BuilderIoController);
exports.BuilderIoController = BuilderIoController;
//# sourceMappingURL=builderio.controller.js.map