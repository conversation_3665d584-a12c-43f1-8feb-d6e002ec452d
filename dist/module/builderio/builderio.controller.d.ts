/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { EventEmitter2 } from "@nestjs/event-emitter";
import { BuilderIoService } from "./builderio.service";
import { GetContentPathDto } from "./dto/get-content-path.dto";
import { GetContentPublicDto } from "./dto/get-content.dto";
import { GetLastNHourDto } from "./dto/get-last-n-hour.dto";
export declare class BuilderIoController {
    private readonly builderIoService;
    private readonly eventEmitter;
    constructor(builderIoService: BuilderIoService, eventEmitter: EventEmitter2);
    sync(params: GetLastNHourDto): Promise<void>;
    find(pagination: GetContentPublicDto): Promise<import("mongoose").PaginateResult<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>>;
    findTags(): Promise<any[]>;
    findPath(param: GetContentPathDto): Promise<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    findUrlKeyPublished(): Promise<import("mongoose").LeanDocument<import("./schema/content.schema").Content & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
}
