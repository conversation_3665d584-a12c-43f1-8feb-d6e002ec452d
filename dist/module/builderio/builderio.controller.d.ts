import { EventEmitter2 } from "@nestjs/event-emitter";
import { BuilderIoService } from "./builderio.service";
import { GetContentPathDto } from "./dto/get-content-path.dto";
import { GetContentPublicDto } from "./dto/get-content.dto";
import { GetLastNHourDto } from "./dto/get-last-n-hour.dto";
export declare class BuilderIoController {
    private readonly builderIoService;
    private readonly eventEmitter;
    constructor(builderIoService: BuilderIoService, eventEmitter: EventEmitter2);
    sync(params: GetLastNHourDto): Promise<void>;
    find(pagination: GetContentPublicDto): Promise<any>;
    findTags(): Promise<any>;
    findPath(param: GetContentPathDto): Promise<any>;
    findUrlKeyPublished(): Promise<any>;
}
