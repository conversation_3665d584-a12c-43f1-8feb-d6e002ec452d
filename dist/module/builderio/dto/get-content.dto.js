"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetContentPublicDto = exports.GetContentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const pagination_param_dto_1 = require("../../../common/pagination-param.dto");
const model_enum_1 = require("../enum/model-enum");
const published_enum_1 = require("../enum/published-enum");
class GetContentDto extends pagination_param_dto_1.PaginationParamDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Published Status",
        enum: published_enum_1.PublishedEnum,
    }),
    (0, class_validator_1.ValidateIf)((obj) => Object.values(published_enum_1.PublishedEnum).includes(obj.type)),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsBooleanString)(),
    __metadata("design:type", String)
], GetContentDto.prototype, "published", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Model",
        enum: model_enum_1.ModelEnum,
    }),
    (0, class_validator_1.ValidateIf)((obj) => Object.values(model_enum_1.ModelEnum).includes(obj.type)),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(model_enum_1.ModelEnum),
    __metadata("design:type", String)
], GetContentDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Content tags. Comma separated",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetContentDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "keyword for searching title or url-key",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetContentDto.prototype, "keyword", void 0);
exports.GetContentDto = GetContentDto;
class GetContentPublicDto extends pagination_param_dto_1.PaginationParamDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Content tags. Comma separated",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetContentPublicDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Keyword",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetContentPublicDto.prototype, "keyword", void 0);
exports.GetContentPublicDto = GetContentPublicDto;
//# sourceMappingURL=get-content.dto.js.map