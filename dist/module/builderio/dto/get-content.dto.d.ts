import { PaginationParamDto } from "src/common/pagination-param.dto";
import { ModelEnum } from "../enum/model-enum";
import { PublishedEnum } from "../enum/published-enum";
export declare class GetContentDto extends PaginationParamDto {
    published: PublishedEnum;
    model: ModelEnum;
    tags?: string;
    keyword?: string;
}
export declare class GetContentPublicDto extends PaginationParamDto {
    tags?: string;
    keyword?: string;
}
