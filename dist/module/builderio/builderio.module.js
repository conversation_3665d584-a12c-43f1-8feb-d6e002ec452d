"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuilderIoModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const builderio_controller_1 = require("./builderio.controller");
const builderio_service_1 = require("./builderio.service");
const content_schema_1 = require("./schema/content.schema");
const axios_1 = require("@nestjs/axios");
const admin_builderio_controller_1 = require("./admin-builderio.controller");
let BuilderIoModule = class BuilderIoModule {
};
BuilderIoModule = __decorate([
    (0, common_1.Module)({
        controllers: [admin_builderio_controller_1.AdminBuilderIoController, builderio_controller_1.BuilderIoController],
        providers: [builderio_service_1.BuilderIoService],
        exports: [builderio_service_1.BuilderIoService],
        imports: [
            mongoose_1.MongooseModule.forFeature([
                {
                    name: "Content",
                    schema: content_schema_1.ContentSchema,
                },
            ]),
            axios_1.HttpModule,
        ],
    })
], BuilderIoModule);
exports.BuilderIoModule = BuilderIoModule;
//# sourceMappingURL=builderio.module.js.map