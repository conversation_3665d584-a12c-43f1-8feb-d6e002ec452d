/// <reference types="multer" />
/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { AmazonS3Services } from "src/amazon-s3/amazons3.service";
import { BannerFooterService } from "./banner-footer.service";
import { CreateBannerFooterDto } from "./dto/create-banner-footer.dto";
import { GetAllBannerFooterDto } from "./dto/get-all-banner-footer.dto";
import { UpdateBannerFooterDto } from "./dto/update-banner-footer.dto";
export declare class BannerFooterController {
    private readonly bannerFooterService;
    private readonly s3Services;
    constructor(bannerFooterService: BannerFooterService, s3Services: AmazonS3Services);
    create(body: CreateBannerFooterDto, files: {
        web_image?: Express.Multer.File;
        apps_image?: Express.Multer.File;
    }): Promise<import("mongoose").Document<unknown, any, import("./schema/banner-footer.schema").BannerFooter> & Omit<import("./schema/banner-footer.schema").BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    findAll(query: GetAllBannerFooterDto): Promise<import("mongoose").PaginateResult<import("mongoose").Document<unknown, any, import("./schema/banner-footer.schema").BannerFooter> & Omit<import("./schema/banner-footer.schema").BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, "page" | "limit" | "sort" | "forceCountFn"> & {
        page: number;
        limit: number;
        forceCountFn: boolean;
        sort: string;
    }>>;
    findOne(id: string): Promise<import("mongoose").Document<unknown, any, import("./schema/banner-footer.schema").BannerFooter> & Omit<import("./schema/banner-footer.schema").BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    update(id: string, updateBannerFooterDto: UpdateBannerFooterDto, files: {
        web_image?: Express.Multer.File;
        apps_image?: Express.Multer.File;
    }): Promise<import("mongoose").Document<unknown, any, import("./schema/banner-footer.schema").BannerFooter> & Omit<import("./schema/banner-footer.schema").BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    remove(id: string): Promise<import("mongoose").Document<unknown, any, import("./schema/banner-footer.schema").BannerFooter> & Omit<import("./schema/banner-footer.schema").BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    private _uploadFile;
}
