import { PaginateModel } from "mongoose";
import { CreateBannerFooterDto } from "./dto/create-banner-footer.dto";
import { GetAllBannerFooterDto } from "./dto/get-all-banner-footer.dto";
import { UpdateBannerFooterDto } from "./dto/update-banner-footer.dto";
import { BannerFooter } from "./schema/banner-footer.schema";
export declare class BannerFooterService {
    private readonly bannerFooterModel;
    constructor(bannerFooterModel: PaginateModel<BannerFooter>);
    create(createBannerFooterDto: CreateBannerFooterDto, imagePayload: any): Promise<any>;
    findAll(params: GetAllBannerFooterDto): Promise<any>;
    findOne(id: any): Promise<any>;
    update(id: string, updateBannerFooterDto: UpdateBannerFooterDto, webUrl?: string, appUrl?: string): Promise<any>;
    remove(id: any): Promise<any>;
    getBannerForHome(): Promise<any>;
}
