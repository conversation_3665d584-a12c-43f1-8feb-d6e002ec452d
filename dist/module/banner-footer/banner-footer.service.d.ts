/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { PaginateModel } from "mongoose";
import { CreateBannerFooterDto } from "./dto/create-banner-footer.dto";
import { GetAllBannerFooterDto } from "./dto/get-all-banner-footer.dto";
import { UpdateBannerFooterDto } from "./dto/update-banner-footer.dto";
import { BannerFooter } from "./schema/banner-footer.schema";
export declare class BannerFooterService {
    private readonly bannerFooterModel;
    constructor(bannerFooterModel: PaginateModel<BannerFooter>);
    create(createBannerFooterDto: CreateBannerFooterDto, imagePayload: any): Promise<import("mongoose").Document<unknown, any, BannerFooter> & Omit<BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    findAll(params: GetAllBannerFooterDto): Promise<import("mongoose").PaginateResult<import("mongoose").Document<unknown, any, BannerFooter> & Omit<BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, "page" | "limit" | "sort" | "forceCountFn"> & {
        page: number;
        limit: number;
        forceCountFn: boolean;
        sort: string;
    }>>;
    findOne(id: any): Promise<import("mongoose").Document<unknown, any, BannerFooter> & Omit<BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    update(id: string, updateBannerFooterDto: UpdateBannerFooterDto, webUrl?: string, appUrl?: string): Promise<import("mongoose").Document<unknown, any, BannerFooter> & Omit<BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    remove(id: any): Promise<import("mongoose").Document<unknown, any, BannerFooter> & Omit<BannerFooter & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    getBannerForHome(): Promise<{}>;
}
