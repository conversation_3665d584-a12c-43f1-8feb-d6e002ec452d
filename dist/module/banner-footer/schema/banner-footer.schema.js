"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BannerFooterSchema = exports.BannerFooter = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoosePaginate = require("mongoose-paginate-v2");
let BannerFooter = class BannerFooter {
};
__decorate([
    (0, mongoose_1.Prop)({
        type: mongoose_2.SchemaTypes.Mixed,
        raw: {
            web: { type: String },
            apps: { type: String },
        },
    }),
    __metadata("design:type", Object)
], BannerFooter.prototype, "image", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.SchemaTypes.String }),
    __metadata("design:type", String)
], BannerFooter.prototype, "url", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.SchemaTypes.Date }),
    __metadata("design:type", Object)
], BannerFooter.prototype, "startDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.SchemaTypes.Date }),
    __metadata("design:type", Object)
], BannerFooter.prototype, "endDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.SchemaTypes.Boolean }),
    __metadata("design:type", Object)
], BannerFooter.prototype, "status", void 0);
BannerFooter = __decorate([
    (0, mongoose_1.Schema)({ timestamps: { createdAt: true, updatedAt: true } })
], BannerFooter);
exports.BannerFooter = BannerFooter;
exports.BannerFooterSchema = mongoose_1.SchemaFactory.createForClass(BannerFooter);
exports.BannerFooterSchema.plugin(mongoosePaginate);
//# sourceMappingURL=banner-footer.schema.js.map