"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BannerFooterModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const amazons3_module_1 = require("../../amazon-s3/amazons3.module");
const banner_footer_controller_1 = require("./banner-footer.controller");
const banner_footer_service_1 = require("./banner-footer.service");
const banner_footer_schema_1 = require("./schema/banner-footer.schema");
let BannerFooterModule = class BannerFooterModule {
};
BannerFooterModule = __decorate([
    (0, common_1.Module)({
        controllers: [banner_footer_controller_1.BannerFooterController],
        providers: [banner_footer_service_1.BannerFooterService],
        imports: [mongoose_1.MongooseModule.forFeature([{ name: banner_footer_schema_1.BannerFooter.name, schema: banner_footer_schema_1.BannerFooterSchema }]), amazons3_module_1.Amazons3Module],
        exports: [banner_footer_service_1.BannerFooterService],
    })
], BannerFooterModule);
exports.BannerFooterModule = BannerFooterModule;
//# sourceMappingURL=banner-footer.module.js.map