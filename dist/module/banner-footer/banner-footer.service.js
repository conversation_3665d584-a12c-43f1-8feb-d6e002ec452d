"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BannerFooterService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const banner_footer_schema_1 = require("./schema/banner-footer.schema");
let BannerFooterService = class BannerFooterService {
    constructor(bannerFooterModel) {
        this.bannerFooterModel = bannerFooterModel;
    }
    create(createBannerFooterDto, imagePayload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.bannerFooterModel.create(Object.assign(Object.assign({}, createBannerFooterDto), imagePayload));
        });
    }
    findAll(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const { page = 1, limit = 10, sort } = params;
            let filter = {};
            if (params.url)
                filter.url = new RegExp(params.url, "i");
            if (params.status)
                filter.status = params.status;
            if (params.keyword) {
                filter = {
                    $or: [
                        { "image.app": { $regex: filter.keyword, $options: "i" } },
                        { "image.web": { $regex: filter.keyword, $options: "i" } },
                        { url: { $regex: filter.keyword, $options: "i" } },
                    ],
                };
            }
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            return yield this.bannerFooterModel.paginate(filter, options);
        });
    }
    findOne(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.bannerFooterModel.findById(id);
        });
    }
    update(id, updateBannerFooterDto, webUrl = "", appUrl = "") {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const data = yield this.bannerFooterModel.findById(id);
            if (!data) {
                throw new common_1.HttpException("Data not found", 400);
            }
            const image = {
                web: ((_a = data === null || data === void 0 ? void 0 : data.image) === null || _a === void 0 ? void 0 : _a.web) || "",
                apps: ((_b = data === null || data === void 0 ? void 0 : data.image) === null || _b === void 0 ? void 0 : _b.apps) || "",
            };
            if (webUrl) {
                image.web = webUrl;
            }
            if (appUrl) {
                image.apps = appUrl;
            }
            console.log({ image });
            return yield this.bannerFooterModel.findByIdAndUpdate(id, Object.assign(Object.assign({}, updateBannerFooterDto), { image }));
        });
    }
    remove(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.bannerFooterModel.findByIdAndDelete(id);
        });
    }
    getBannerForHome() {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.bannerFooterModel.find({
                status: true,
                startDate: { $lte: new Date() },
                endDate: { $gte: new Date() },
            }, {}, { sort: { createdAt: -1 } });
            return !result.length ? {} : result[0];
        });
    }
};
BannerFooterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(banner_footer_schema_1.BannerFooter.name)),
    __metadata("design:paramtypes", [Object])
], BannerFooterService);
exports.BannerFooterService = BannerFooterService;
//# sourceMappingURL=banner-footer.service.js.map