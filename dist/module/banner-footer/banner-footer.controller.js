"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BannerFooterController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const path = require("path");
const amazons3_service_1 = require("../../amazon-s3/amazons3.service");
const banner_footer_service_1 = require("./banner-footer.service");
const create_banner_footer_dto_1 = require("./dto/create-banner-footer.dto");
const get_all_banner_footer_dto_1 = require("./dto/get-all-banner-footer.dto");
const update_banner_footer_dto_1 = require("./dto/update-banner-footer.dto");
let BannerFooterController = class BannerFooterController {
    constructor(bannerFooterService, s3Services) {
        this.bannerFooterService = bannerFooterService;
        this.s3Services = s3Services;
    }
    create(body, files) {
        return __awaiter(this, void 0, void 0, function* () {
            const module = "banner-footer";
            if (!files.web_image || !files.apps_image) {
                throw new common_1.HttpException("Web or apps image cannot be empty.", common_1.HttpStatus.BAD_REQUEST);
            }
            const [webUrl, appUrl] = yield Promise.all([
                yield this._uploadFile(module, files.web_image[0]),
                yield this._uploadFile(module, files.apps_image[0]),
            ]);
            const imagePayload = {
                image: {
                    web: webUrl.Location,
                    apps: appUrl.Location,
                },
            };
            return this.bannerFooterService.create(body, imagePayload);
        });
    }
    findAll(query) {
        return this.bannerFooterService.findAll(query);
    }
    findOne(id) {
        return this.bannerFooterService.findOne(id);
    }
    update(id, updateBannerFooterDto, files) {
        return __awaiter(this, void 0, void 0, function* () {
            const module = "banner-footer";
            let webUrl = "";
            let appUrl = "";
            if (files.web_image) {
                const res = yield this._uploadFile(module, files.web_image[0]);
                webUrl = res.Location;
            }
            if (files.apps_image) {
                const res = yield this._uploadFile(module, files.apps_image[0]);
                appUrl = res.Location;
            }
            return yield this.bannerFooterService.update(id, updateBannerFooterDto, webUrl, appUrl);
        });
    }
    remove(id) {
        return this.bannerFooterService.remove(id);
    }
    _uploadFile(module, file) {
        return __awaiter(this, void 0, void 0, function* () {
            const ext = path.extname(file.originalname);
            const mimetype = file.mimetype;
            return yield this.s3Services.uploadFile(file.buffer, ext, module + "/", mimetype);
        });
    }
};
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileFieldsInterceptor)([
        { name: "web_image", maxCount: 1 },
        { name: "apps_image", maxCount: 1 },
    ])),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_banner_footer_dto_1.CreateBannerFooterDto, Object]),
    __metadata("design:returntype", Promise)
], BannerFooterController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_all_banner_footer_dto_1.GetAllBannerFooterDto]),
    __metadata("design:returntype", void 0)
], BannerFooterController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BannerFooterController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileFieldsInterceptor)([
        { name: "web_image", maxCount: 1 },
        { name: "apps_image", maxCount: 1 },
    ])),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_banner_footer_dto_1.UpdateBannerFooterDto, Object]),
    __metadata("design:returntype", Promise)
], BannerFooterController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(":id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BannerFooterController.prototype, "remove", null);
BannerFooterController = __decorate([
    (0, common_1.Controller)("banner-footer"),
    (0, swagger_1.ApiTags)("Banner Footer"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:paramtypes", [banner_footer_service_1.BannerFooterService,
        amazons3_service_1.AmazonS3Services])
], BannerFooterController);
exports.BannerFooterController = BannerFooterController;
//# sourceMappingURL=banner-footer.controller.js.map