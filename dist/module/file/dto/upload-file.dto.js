"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadFileDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const enum_1 = require("../../enum");
const class_validator_1 = require("class-validator");
class UploadFileDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ enum: Object.keys(enum_1.AllowedType) }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(Object.keys(enum_1.AllowedType)),
    __metadata("design:type", String)
], UploadFileDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Max 3 files",
        type: "array",
        items: {
            type: "string",
            format: "binary",
        },
        maxItems: 3,
        minItems: 1,
    }),
    __metadata("design:type", Array)
], UploadFileDto.prototype, "images", void 0);
exports.UploadFileDto = UploadFileDto;
//# sourceMappingURL=upload-file.dto.js.map