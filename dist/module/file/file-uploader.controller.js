"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUploaderController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const path = require("path");
const amazons3_service_1 = require("../../amazon-s3/amazons3.service");
const api_body_file_decorator_1 = require("./decorator/api-body-file.decorator");
const api_body_files_decorator_1 = require("./decorator/api-body-files.decorator");
const function_util_1 = require("../../utils/function.util");
const platform_express_1 = require("@nestjs/platform-express");
const upload_file_dto_1 = require("./dto/upload-file.dto");
const enum_1 = require("../enum");
let FileUploaderController = class FileUploaderController {
    constructor(s3Services) {
        this.s3Services = s3Services;
    }
    upload(images, body) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!images.length) {
                throw new common_1.HttpException("Files cannot be empty", common_1.HttpStatus.BAD_REQUEST);
            }
            if (images.length > 3) {
                throw new common_1.HttpException("Max 3 files", common_1.HttpStatus.BAD_REQUEST);
            }
            if (!enum_1.AllowedType[body.type]) {
                throw new common_1.HttpException(`Type ${body.type} not found`, common_1.HttpStatus.BAD_REQUEST);
            }
            const allowedExt = enum_1.AllowedExt[body.type];
            const allowedMaxSize = enum_1.AllowedMaxSize[body.type];
            const folder = enum_1.AllowedType[body.type];
            yield this._validateFile(images, allowedExt, allowedMaxSize);
            return yield this._doUpload(images, folder);
        });
    }
    uploadFile(module, images) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!images.length) {
                throw new common_1.HttpException("Files cannot be empty.", common_1.HttpStatus.BAD_REQUEST);
            }
            const result = images.map((image) => __awaiter(this, void 0, void 0, function* () {
                const ext = path.extname(image.originalname);
                const mimetype = image.mimetype;
                return yield this.s3Services.uploadFile(image.buffer, ext, module + "/", mimetype);
            }));
            return yield Promise.all(result);
        });
    }
    uploadFilePdf(module, file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!file) {
                throw new common_1.HttpException("File cannot be empty.", common_1.HttpStatus.BAD_REQUEST);
            }
            const ext = path.extname(file.originalname);
            const mimetype = file.mimetype;
            return yield this.s3Services.uploadFile(file.buffer, ext, module + "/", mimetype);
        });
    }
    uploadFileCsv(module, file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!file) {
                throw new common_1.HttpException("File cannot be empty.", common_1.HttpStatus.BAD_REQUEST);
            }
            const ext = path.extname(file.originalname);
            const mimetype = file.mimetype;
            return yield this.s3Services.uploadFile(file.buffer, ext, module + "/", mimetype);
        });
    }
    _validateFile(files, allowedExt = ".jpg,.jpeg,.png", allowedMaxSize = "300 kb") {
        const errors = [];
        const allowedSize = (0, function_util_1.transformAllowedSize)(allowedMaxSize);
        files.map((file, i) => {
            const ext = path.extname(file.originalname);
            if (!allowedExt.split(",").includes(ext)) {
                errors.push(`File ${i + 1} unaccepted extension`);
            }
            else if (file.buffer.byteLength > allowedSize) {
                errors.push(`File ${i + 1} reach max size`);
            }
        });
        if (errors.length) {
            throw new common_1.HttpException({ statusCode: common_1.HttpStatus.BAD_REQUEST, message: errors }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    _doUpload(files, folder) {
        return Promise.all(files.map((image) => __awaiter(this, void 0, void 0, function* () {
            const ext = path.extname(image.originalname);
            const mimetype = image.mimetype;
            return yield this.s3Services.uploadFile(image.buffer, ext, folder + "/", mimetype);
        })));
    }
    uploadFileVideo(module, file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!file) {
                throw new common_1.HttpException("File cannot be empty.", common_1.HttpStatus.BAD_REQUEST);
            }
            const ext = path.extname(file.originalname);
            const mimetype = file.mimetype;
            return yield this.s3Services.uploadFile(file.buffer, ext, module + "/", mimetype);
        });
    }
};
__decorate([
    (0, common_1.Post)("upload"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images")),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    __param(0, (0, common_1.UploadedFiles)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, upload_file_dto_1.UploadFileDto]),
    __metadata("design:returntype", Promise)
], FileUploaderController.prototype, "upload", null);
__decorate([
    (0, common_1.Post)("/:module/upload"),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, api_body_files_decorator_1.ApiBodyFiles)("images", true, 3),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    __param(0, (0, common_1.Param)("module")),
    __param(1, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array]),
    __metadata("design:returntype", Promise)
], FileUploaderController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Post)("/pdf/:module/upload"),
    (0, api_body_file_decorator_1.ApiBodyFile)("file", true),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Param)("module")),
    __param(1, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [new common_1.FileTypeValidator({ fileType: "pdf" })],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_b = typeof Express !== "undefined" && (_a = Express.Multer) !== void 0 && _a.File) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], FileUploaderController.prototype, "uploadFilePdf", null);
__decorate([
    (0, common_1.Post)("/csv/:module/upload"),
    (0, api_body_file_decorator_1.ApiBodyFile)("file", true),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Param)("module")),
    __param(1, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [new common_1.FileTypeValidator({ fileType: "csv" })],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_d = typeof Express !== "undefined" && (_c = Express.Multer) !== void 0 && _c.File) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], FileUploaderController.prototype, "uploadFileCsv", null);
__decorate([
    (0, common_1.Post)("/video/:module/upload"),
    (0, api_body_file_decorator_1.ApiBodyFile)("file", true),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Param)("module")),
    __param(1, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [new common_1.FileTypeValidator({ fileType: /(mkv|flv|webm|avi|mov|wmv|mp4|mpg|mpeg)$/ })],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_f = typeof Express !== "undefined" && (_e = Express.Multer) !== void 0 && _e.File) === "function" ? _f : Object]),
    __metadata("design:returntype", Promise)
], FileUploaderController.prototype, "uploadFileVideo", null);
FileUploaderController = __decorate([
    (0, swagger_1.ApiTags)("Files"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)("file"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:paramtypes", [amazons3_service_1.AmazonS3Services])
], FileUploaderController);
exports.FileUploaderController = FileUploaderController;
//# sourceMappingURL=file-uploader.controller.js.map