"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogCategoryService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let BlogCategoryService = class BlogCategoryService {
    constructor(modelCategory, modelBlog) {
        this.modelCategory = modelCategory;
        this.modelBlog = modelBlog;
    }
    findAll() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.modelCategory.find({ status: 1 }, { posts: 0 }).lean();
        });
    }
    updateCategories(payload) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const blog = yield this.modelBlog.findOne({ post_id: payload.post_id });
            if (!blog) {
                throw new common_1.HttpException("Blog not found", common_1.HttpStatus.NOT_FOUND);
            }
            const category = yield this.modelCategory.findOne({ category_id: payload.category_id });
            if (!category) {
                throw new common_1.HttpException("Category not found", common_1.HttpStatus.NOT_FOUND);
            }
            let categoryContainsBlog = false;
            let blogContainsCategory = false;
            (_a = blog.categories) === null || _a === void 0 ? void 0 : _a.forEach((item) => {
                if (item.toString() === category._id.toString()) {
                    blogContainsCategory = true;
                }
            });
            category.posts.forEach((item) => {
                if (item.toString() === blog._id.toString()) {
                    categoryContainsBlog = true;
                }
            });
            if (!blogContainsCategory) {
                yield this.modelBlog.updateOne({ _id: blog._id }, { $push: { categories: category._id } }).exec();
            }
            if (!categoryContainsBlog) {
                yield this.modelCategory.updateOne({ _id: category._id }, { $push: { posts: blog._id } }).exec();
            }
            return "Update success";
        });
    }
    generateDummy() {
        return __awaiter(this, void 0, void 0, function* () {
            const items = [];
            items.forEach((item) => {
                const post_id = item.split(",")[0];
                const category_id = item.split(",")[1];
                this.updateCategories({ post_id: Number(post_id), category_id: Number(category_id) });
            });
        });
    }
};
BlogCategoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("BlogCategory")),
    __param(1, (0, mongoose_1.InjectModel)("Blog")),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], BlogCategoryService);
exports.BlogCategoryService = BlogCategoryService;
//# sourceMappingURL=blog-category.service.js.map