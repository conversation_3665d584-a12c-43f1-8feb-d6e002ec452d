"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const blog_category_service_1 = require("./blog-category.service");
const blog_tag_service_1 = require("./blog-tag.service");
const blog_service_1 = require("./blog.service");
const get_blog_dto_1 = require("./dto/get-blog.dto");
const update_blog_category_dto_1 = require("./dto/update-blog-category.dto");
const update_blog_tag_dto_1 = require("./dto/update-blog-tag.dto");
let BlogController = class BlogController {
    constructor(blogService, blogTagService, blogCategoryService) {
        this.blogService = blogService;
        this.blogTagService = blogTagService;
        this.blogCategoryService = blogCategoryService;
    }
    getBlogs(query) {
        return this.blogService.getBlogs(query);
    }
    findAll() {
        return this.blogTagService.findAll();
    }
    setTag(payload) {
        return this.blogTagService.updateTags(payload);
    }
    generateDummyTags() {
        return this.blogTagService.generateDummy();
    }
    findAllCategories() {
        return this.blogCategoryService.findAll();
    }
    setCategories(payload) {
        return this.blogCategoryService.updateCategories(payload);
    }
    generateDummies() {
        return this.blogCategoryService.generateDummy();
    }
};
__decorate([
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_blog_dto_1.GetBlogDto]),
    __metadata("design:returntype", void 0)
], BlogController.prototype, "getBlogs", null);
__decorate([
    (0, common_1.Get)("/tags"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BlogController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)("/tags"),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_blog_tag_dto_1.UpdateBlogTagDto]),
    __metadata("design:returntype", void 0)
], BlogController.prototype, "setTag", null);
__decorate([
    (0, common_1.Get)("/tags/dummy"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BlogController.prototype, "generateDummyTags", null);
__decorate([
    (0, common_1.Get)("/categories"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BlogController.prototype, "findAllCategories", null);
__decorate([
    (0, common_1.Post)("/categories"),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_blog_category_dto_1.UpdateBlogCategoryDto]),
    __metadata("design:returntype", void 0)
], BlogController.prototype, "setCategories", null);
__decorate([
    (0, common_1.Get)("/categories/dummy"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BlogController.prototype, "generateDummies", null);
BlogController = __decorate([
    (0, swagger_1.ApiTags)("Blog"),
    (0, common_1.Controller)("blog"),
    __metadata("design:paramtypes", [blog_service_1.BlogService,
        blog_tag_service_1.BlogTagService,
        blog_category_service_1.BlogCategoryService])
], BlogController);
exports.BlogController = BlogController;
//# sourceMappingURL=blog.controller.js.map