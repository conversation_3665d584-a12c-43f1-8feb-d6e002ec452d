import { Model } from "mongoose";
import { UpdateBlogCategoryDto } from "./dto/update-blog-category.dto";
import { BlogCategoryDocument } from "./schema/blog-category.schema";
import { BlogDocument } from "./schema/blog.schema";
export declare class BlogCategoryService {
    private modelCategory;
    private modelBlog;
    constructor(modelCategory: Model<BlogCategoryDocument>, modelBlog: Model<BlogDocument>);
    findAll(): Promise<import("mongoose").LeanDocument<import("./schema/blog-category.schema").BlogCategory & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    updateCategories(payload: UpdateBlogCategoryDto): Promise<string>;
    generateDummy(): Promise<void>;
}
