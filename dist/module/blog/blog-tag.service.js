"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogTagService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let BlogTagService = class BlogTagService {
    constructor(modelTag, modelBlog) {
        this.modelTag = modelTag;
        this.modelBlog = modelBlog;
    }
    findAll() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.modelTag.find().lean();
        });
    }
    updateTags(payload) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const blog = yield this.modelBlog.findOne({ post_id: payload.post_id });
            if (!blog) {
                throw new common_1.HttpException("Blog not found", common_1.HttpStatus.NOT_FOUND);
            }
            const tag = yield yield this.modelTag.findOne({ tag_id: payload.tag_id });
            if (!tag) {
                throw new common_1.HttpException("Tag not found", common_1.HttpStatus.NOT_FOUND);
            }
            let tagContainsBlog = false;
            let blogContainsTag = false;
            (_a = blog.tags) === null || _a === void 0 ? void 0 : _a.forEach((item) => {
                if (item.toString() === tag._id.toString()) {
                    blogContainsTag = true;
                }
            });
            tag.posts.forEach((item) => {
                if (item.toString() === blog._id.toString()) {
                    tagContainsBlog = true;
                }
            });
            if (!blogContainsTag) {
                yield this.modelBlog.updateOne({ _id: blog._id }, { $push: { tags: tag._id } }).exec();
            }
            if (!tagContainsBlog) {
                yield this.modelTag.updateOne({ _id: tag._id }, { $push: { posts: blog._id } }).exec();
            }
            return "Update success";
        });
    }
    generateDummy() {
        return __awaiter(this, void 0, void 0, function* () {
            const items = ["5,1"];
            items.forEach((item) => {
                const post_id = item.split(",")[0];
                const tag_id = item.split(",")[1];
                this.updateTags({ post_id: Number(post_id), tag_id: Number(tag_id) });
            });
        });
    }
};
BlogTagService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("BlogTag")),
    __param(1, (0, mongoose_1.InjectModel)("Blog")),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], BlogTagService);
exports.BlogTagService = BlogTagService;
//# sourceMappingURL=blog-tag.service.js.map