/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { BlogCollectionService } from "./blog-collection.service";
import { CreateBlogCollectionDto, GetOneBlogCollectionDto } from "./dto";
import { PaginationParamDto } from "src/common/pagination-param.dto";
export declare class BlogCollectionController {
    private readonly blogCollectionService;
    constructor(blogCollectionService: BlogCollectionService);
    create(payload: CreateBlogCollectionDto): Promise<import("mongoose").Document<unknown, any, import("./schema").BlogCollectionDocument> & Omit<import("./schema").BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    findOne(params: GetOneBlogCollectionDto): Promise<import("mongoose").Document<unknown, any, import("./schema").BlogCollectionDocument> & Omit<import("./schema").BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    findMany(queries: PaginationParamDto): Promise<import("mongoose").PaginateResult<import("mongoose").Document<unknown, any, import("./schema").BlogCollectionDocument> & Omit<import("./schema").BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, "page" | "limit" | "populate" | "projection"> & {
        page: number;
        limit: number;
        populate: {
            path: string;
            select: string;
        };
        projection: string;
    }>>;
    update(param: GetOneBlogCollectionDto, payload: CreateBlogCollectionDto): Promise<import("mongoose").Document<unknown, any, import("./schema").BlogCollectionDocument> & Omit<import("./schema").BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    delete(params: GetOneBlogCollectionDto): Promise<string>;
}
