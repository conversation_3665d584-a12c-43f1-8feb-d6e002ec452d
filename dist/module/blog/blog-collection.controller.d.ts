import { BlogCollectionService } from "./blog-collection.service";
import { CreateBlogCollectionDto, GetOneBlogCollectionDto } from "./dto";
import { PaginationParamDto } from "src/common/pagination-param.dto";
export declare class BlogCollectionController {
    private readonly blogCollectionService;
    constructor(blogCollectionService: BlogCollectionService);
    create(payload: CreateBlogCollectionDto): Promise<any>;
    findOne(params: GetOneBlogCollectionDto): Promise<any>;
    findMany(queries: PaginationParamDto): Promise<any>;
    update(param: GetOneBlogCollectionDto, payload: CreateBlogCollectionDto): Promise<any>;
    delete(params: GetOneBlogCollectionDto): Promise<string>;
}
