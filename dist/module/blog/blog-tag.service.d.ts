import { Model } from "mongoose";
import { UpdateBlogTagDto } from "./dto/update-blog-tag.dto";
import { BlogTagDocument } from "./schema/blog-tag.schema";
import { BlogDocument } from "./schema/blog.schema";
export declare class BlogTagService {
    private modelTag;
    private modelBlog;
    constructor(modelTag: Model<BlogTagDocument>, modelBlog: Model<BlogDocument>);
    findAll(): Promise<import("mongoose").LeanDocument<import("./schema/blog-tag.schema").BlogTag & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    updateTags(payload: UpdateBlogTagDto): Promise<string>;
    generateDummy(): Promise<void>;
}
