/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { BlogCategoryService } from "./blog-category.service";
import { BlogTagService } from "./blog-tag.service";
import { BlogService } from "./blog.service";
import { GetBlogDto } from "./dto/get-blog.dto";
import { UpdateBlogCategoryDto } from "./dto/update-blog-category.dto";
import { UpdateBlogTagDto } from "./dto/update-blog-tag.dto";
export declare class BlogController {
    private readonly blogService;
    private readonly blogTagService;
    private readonly blogCategoryService;
    constructor(blogService: BlogService, blogTagService: BlogTagService, blogCategoryService: BlogCategoryService);
    getBlogs(query: GetBlogDto): Promise<import("mongoose").PaginateResult<import("mongoose").Document<unknown, any, import("./schema").BlogDocument> & Omit<import("./schema").Blog & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, "page" | "limit" | "sort" | "select" | "forceCountFn"> & {
        page: number;
        limit: number;
        forceCountFn: boolean;
        select: string[];
        sort: {
            views: number;
            post_id: number;
        };
    }>>;
    findAll(): Promise<import("mongoose").LeanDocument<import("./schema").BlogTag & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    setTag(payload: UpdateBlogTagDto): Promise<string>;
    generateDummyTags(): Promise<void>;
    findAllCategories(): Promise<import("mongoose").LeanDocument<import("./schema").BlogCategory & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    setCategories(payload: UpdateBlogCategoryDto): Promise<string>;
    generateDummies(): Promise<void>;
}
