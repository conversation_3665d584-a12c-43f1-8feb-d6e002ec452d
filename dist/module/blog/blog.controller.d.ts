import { BlogCategoryService } from "./blog-category.service";
import { BlogTagService } from "./blog-tag.service";
import { BlogService } from "./blog.service";
import { GetBlogDto } from "./dto/get-blog.dto";
import { UpdateBlogCategoryDto } from "./dto/update-blog-category.dto";
import { UpdateBlogTagDto } from "./dto/update-blog-tag.dto";
export declare class BlogController {
    private readonly blogService;
    private readonly blogTagService;
    private readonly blogCategoryService;
    constructor(blogService: BlogService, blogTagService: BlogTagService, blogCategoryService: BlogCategoryService);
    getBlogs(query: GetBlogDto): Promise<any>;
    findAll(): Promise<import("mongoose").LeanDocument<import("./schema").BlogTag & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    setTag(payload: UpdateBlogTagDto): Promise<string>;
    generateDummyTags(): Promise<void>;
    findAllCategories(): Promise<import("mongoose").LeanDocument<import("./schema").BlogCategory & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    setCategories(payload: UpdateBlogCategoryDto): Promise<string>;
    generateDummies(): Promise<void>;
}
