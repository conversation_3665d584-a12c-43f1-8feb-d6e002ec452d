"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogModule = void 0;
const blog_category_service_1 = require("./blog-category.service");
const blog_tag_service_1 = require("./blog-tag.service");
const common_1 = require("@nestjs/common");
const blog_service_1 = require("./blog.service");
const blog_controller_1 = require("./blog.controller");
const mongoose_1 = require("@nestjs/mongoose");
const schema_1 = require("./schema");
const schema_2 = require("./schema");
const nestjs_elastic_apm_1 = require("../../../modules/nestjs-elastic-apm");
const blog_collection_service_1 = require("./blog-collection.service");
const blog_collection_controller_1 = require("./blog-collection.controller");
const site_configs_module_1 = require("../site-configs/site-configs.module");
const web_builder_module_1 = require("../microservices/web-builder/web-builder.module");
let BlogModule = class BlogModule {
};
BlogModule = __decorate([
    (0, common_1.Module)({
        controllers: [blog_controller_1.BlogController, blog_collection_controller_1.BlogCollectionController],
        providers: [blog_service_1.BlogService, blog_tag_service_1.BlogTagService, blog_category_service_1.BlogCategoryService, blog_collection_service_1.BlogCollectionService],
        imports: [
            mongoose_1.MongooseModule.forFeature([
                {
                    name: schema_2.Blog.name,
                    schema: schema_1.BlogSchema,
                },
                {
                    name: schema_2.BlogCategory.name,
                    schema: schema_1.BlogCategorySchema,
                },
                {
                    name: schema_2.BlogTag.name,
                    schema: schema_1.BlogTagSchema,
                },
                {
                    name: schema_2.BlogCollection.name,
                    schema: schema_1.BlogCollectionSchema,
                },
            ]),
            nestjs_elastic_apm_1.ApmModule.register(),
            site_configs_module_1.SiteConfigsModule,
            web_builder_module_1.WebBuilderModule,
        ],
        exports: [blog_collection_service_1.BlogCollectionService],
    })
], BlogModule);
exports.BlogModule = BlogModule;
//# sourceMappingURL=blog.module.js.map