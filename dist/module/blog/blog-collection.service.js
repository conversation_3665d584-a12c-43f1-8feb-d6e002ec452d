"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogCollectionService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const schema_1 = require("./schema");
const site_config_service_1 = require("../site-configs/site-config.service");
const event_emitter_1 = require("@nestjs/event-emitter");
let BlogCollectionService = class BlogCollectionService {
    constructor(blogCollectionModel, siteConfigService, eventEmitter) {
        this.blogCollectionModel = blogCollectionModel;
        this.siteConfigService = siteConfigService;
        this.eventEmitter = eventEmitter;
    }
    create(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newCollection = yield this.blogCollectionModel.create(payload);
                this.eventEmitter.emit("rebuild-home");
                return newCollection;
            }
            catch (err) {
                throw err;
            }
        });
    }
    update(id, payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const updatedCollection = yield this.blogCollectionModel.findByIdAndUpdate(id, payload, { new: true });
            this.eventEmitter.emit("rebuild-home");
            return updatedCollection;
        });
    }
    getOne(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.blogCollectionModel.findById(id, "-__v").populate("content_id", "title url");
        });
    }
    getMany(queries) {
        return __awaiter(this, void 0, void 0, function* () {
            const { page, limit, sort } = queries;
            const queryOptions = {
                page: page || 1,
                limit: limit || 10,
                populate: {
                    path: "content_id",
                    select: "title url",
                },
                projection: "-__v",
            };
            return yield this.blogCollectionModel.paginate({}, queryOptions);
        });
    }
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.blogCollectionModel.deleteOne({ _id: id });
            this.eventEmitter.emit("rebuild-home");
            return "deleted";
        });
    }
    getActiveBlogs() {
        return __awaiter(this, void 0, void 0, function* () {
            let blogCount;
            try {
                const blogCountConfig = yield this.siteConfigService.findByKey("home.blog_display_count");
                blogCount = Number(blogCountConfig.value);
            }
            catch (err) {
                blogCount = 3;
            }
            const blogCollections = yield this.blogCollectionModel
                .find({
                status: true,
                $or: [
                    {
                        $and: [
                            { startDate: { $exists: false } },
                            { endDate: { $exists: false } }
                        ]
                    },
                    {
                        $and: [
                            { startDate: null },
                            { endDate: null }
                        ]
                    },
                    {
                        $and: [
                            { startDate: { $lte: Date.now() } },
                            { endDate: { $gte: Date.now() } }
                        ]
                    }
                ],
            }, {}, {
                limit: blogCount,
                sort: { position: -1 },
            })
                .populate("content_id");
            const blogs = blogCollections.map((blog) => blog.content_id);
            return blogs;
        });
    }
};
BlogCollectionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(schema_1.BlogCollection.name)),
    __metadata("design:paramtypes", [Object, site_config_service_1.SiteConfigService,
        event_emitter_1.EventEmitter2])
], BlogCollectionService);
exports.BlogCollectionService = BlogCollectionService;
//# sourceMappingURL=blog-collection.service.js.map