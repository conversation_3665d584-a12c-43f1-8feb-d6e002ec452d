/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { PaginateModel } from "mongoose";
import { BlogCollection, BlogCollectionDocument } from "./schema";
import { CreateBlogCollectionDto } from "./dto";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { SiteConfigService } from "../site-configs/site-config.service";
import { EventEmitter2 } from "@nestjs/event-emitter";
export declare class BlogCollectionService {
    private readonly blogCollectionModel;
    private readonly siteConfigService;
    private readonly eventEmitter;
    constructor(blogCollectionModel: PaginateModel<BlogCollectionDocument>, siteConfigService: SiteConfigService, eventEmitter: EventEmitter2);
    create(payload: CreateBlogCollectionDto): Promise<import("mongoose").Document<unknown, any, BlogCollectionDocument> & Omit<BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    update(id: string, payload: CreateBlogCollectionDto): Promise<import("mongoose").Document<unknown, any, BlogCollectionDocument> & Omit<BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    getOne(id: string): Promise<import("mongoose").Document<unknown, any, BlogCollectionDocument> & Omit<BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
    getMany(queries: PaginationParamDto): Promise<import("mongoose").PaginateResult<import("mongoose").Document<unknown, any, BlogCollectionDocument> & Omit<BlogCollection & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, "page" | "limit" | "populate" | "projection"> & {
        page: number;
        limit: number;
        populate: {
            path: string;
            select: string;
        };
        projection: string;
    }>>;
    delete(id: string): Promise<string>;
    getActiveBlogs(): Promise<import("mongoose").Types.ObjectId[]>;
}
