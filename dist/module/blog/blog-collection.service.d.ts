import { EventEmitter2 } from "@nestjs/event-emitter";
import { PaginateModel } from "mongoose";
import { PaginationParamDto } from "src/common/pagination-param.dto";
import { SiteConfigService } from "../site-configs/site-config.service";
import { CreateBlogCollectionDto } from "./dto";
import { BlogCollectionDocument } from "./schema";
export declare class BlogCollectionService {
    private readonly blogCollectionModel;
    private readonly siteConfigService;
    private readonly eventEmitter;
    constructor(blogCollectionModel: PaginateModel<BlogCollectionDocument>, siteConfigService: SiteConfigService, eventEmitter: EventEmitter2);
    create(payload: CreateBlogCollectionDto): Promise<any>;
    update(id: string, payload: CreateBlogCollectionDto): Promise<any>;
    getOne(id: string): Promise<any>;
    getMany(queries: PaginationParamDto): Promise<any>;
    delete(id: string): Promise<string>;
    getActiveBlogs(): Promise<any>;
}
