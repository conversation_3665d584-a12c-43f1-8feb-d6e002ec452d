"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let BlogService = class BlogService {
    constructor(blogModel, blogTagModel, blogCategoryModel) {
        this.blogModel = blogModel;
        this.blogTagModel = blogTagModel;
        this.blogCategoryModel = blogCategoryModel;
    }
    getBlogs(payload) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const query = {};
            if (payload.keyword) {
                const regex = new RegExp(payload.keyword, "i");
                query["$or"] = [{ title: regex }, { url_key: regex }, { short_content: regex }, { full_content: regex }];
            }
            if (payload.category_id) {
                const category = yield this.blogCategoryModel.findOne({ category_id: Number(payload.category_id) });
                if (!category) {
                    throw new common_1.HttpException("Category not found", common_1.HttpStatus.NOT_FOUND);
                }
                query["categories"] = { $in: [category._id] };
            }
            if (payload.category_key) {
                const category = yield this.blogCategoryModel.findOne({ url_key: payload.category_key });
                if (!category) {
                    throw new common_1.HttpException("Category not found", common_1.HttpStatus.NOT_FOUND);
                }
                query["categories"] = { $in: [category._id] };
            }
            if (payload.tag_id) {
                const tag = yield this.blogTagModel.findOne({ tag_id: Number(payload.tag_id) });
                if (!tag) {
                    throw new common_1.HttpException("Tag not found", common_1.HttpStatus.NOT_FOUND);
                }
                query["tags"] = { $in: [tag._id] };
            }
            if (payload.tag_key) {
                const tag = yield this.blogTagModel.findOne({ url_key: payload.tag_key });
                if (!tag) {
                    throw new common_1.HttpException("Tag not found", common_1.HttpStatus.NOT_FOUND);
                }
                query["tags"] = { $in: [tag._id] };
            }
            const options = {
                page: Number((_a = payload.page) !== null && _a !== void 0 ? _a : 1),
                limit: Number((_b = payload.limit) !== null && _b !== void 0 ? _b : 10),
                forceCountFn: true,
                select: ["-full_content", "-categories", "-tags"],
                sort: { views: -1, post_id: -1 },
            };
            return this.blogModel.paginate(query, options, function (err, result) {
                result.docs.forEach(function (e) {
                    e.url_key = "/blog/" + e.url_key;
                    const dummyImageUrl = "https://www.thebodyshop.co.id/media/amasty/blog/cache/M/a/1440/589/Manfaat_Raspberry_untuk_Kulit_Wajah.jpg";
                    e.thumbnail_url = dummyImageUrl;
                    e.post_thumbnail = dummyImageUrl;
                    e.list_thumbnail = dummyImageUrl;
                });
                return result;
            });
        });
    }
};
BlogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Blog")),
    __param(1, (0, mongoose_1.InjectModel)("BlogTag")),
    __param(2, (0, mongoose_1.InjectModel)("BlogCategory")),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, mongoose_2.Model,
        mongoose_2.Model])
], BlogService);
exports.BlogService = BlogService;
//# sourceMappingURL=blog.service.js.map