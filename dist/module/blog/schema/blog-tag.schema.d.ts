import mongoose from "mongoose";
export type BlogTagDocument = BlogTag & Document;
export declare class BlogTag {
    tag_id: number;
    url_key: string;
    name: string;
    meta_title: string;
    meta_tags: string;
    meta_description: string;
    posts: mongoose.Schema.Types.ObjectId[];
}
export declare const BlogTagSchema: mongoose.Schema<BlogTag, mongoose.Model<BlogTag, any, any, any, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, BlogTag>;
