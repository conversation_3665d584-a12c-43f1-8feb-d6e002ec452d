"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogCollectionSchema = exports.BlogCollection = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const content_schema_1 = require("../../builderio/schema/content.schema");
const mongoosePaginate = require("mongoose-paginate-v2");
let BlogCollection = class BlogCollection {
};
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: mongoose_2.Types.ObjectId,
        ref: content_schema_1.Content.name,
    }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], BlogCollection.prototype, "content_id", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: Number,
    }),
    __metadata("design:type", String)
], BlogCollection.prototype, "position", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        default: false,
    }),
    __metadata("design:type", Boolean)
], BlogCollection.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Date }),
    __metadata("design:type", Date)
], BlogCollection.prototype, "startDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Date }),
    __metadata("design:type", Date)
], BlogCollection.prototype, "endDate", void 0);
BlogCollection = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
    }),
    (0, common_1.Injectable)()
], BlogCollection);
exports.BlogCollection = BlogCollection;
exports.BlogCollectionSchema = mongoose_1.SchemaFactory.createForClass(BlogCollection);
exports.BlogCollectionSchema.plugin(mongoosePaginate);
//# sourceMappingURL=blog-collection.schema.js.map