import mongoose from "mongoose";
export type BlogDocument = Blog & Document;
export declare class Blog {
    post_id: number;
    status: number;
    title: string;
    url_key: string;
    use_comments: number;
    short_content: string;
    full_content: string;
    meta_title: string;
    meta_tags: string;
    meta_description: string;
    created_at: string;
    updated_at: string;
    published_at: string;
    user_define_publish: number;
    notify_on_enable: number;
    display_short_content: number;
    comments_enabled: number;
    views: number;
    post_thumbnail: string;
    list_thumbnail: string;
    thumbnail_url: string;
    grid_class: string;
    post_thumbnail_alt: string;
    list_thumbnail_alt: string;
    product: string;
    related_post_ids: string;
    is_featured: number;
    tag: string;
    tags: mongoose.Schema.Types.ObjectId[];
    categories: mongoose.Schema.Types.ObjectId[];
    startDate: Date;
    endDate: Date;
}
export declare const BlogSchema: mongoose.Schema<Blog, mongoose.Model<Blog, any, any, any, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Blog>;
