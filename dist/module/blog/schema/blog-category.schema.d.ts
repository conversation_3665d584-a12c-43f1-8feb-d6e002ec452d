import mongoose from "mongoose";
export type BlogCategoryDocument = BlogCategory & Document;
export declare class BlogCategory {
    category_id: number;
    name: string;
    status: number;
    sort_order: number;
    meta_title: string;
    meta_tags: string;
    meta_description: string;
    url_key: string;
    posts: mongoose.Schema.Types.ObjectId[];
}
export declare const BlogCategorySchema: any;
