"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogCollectionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const blog_collection_service_1 = require("./blog-collection.service");
const dto_1 = require("./dto");
const enum_1 = require("../enum");
const pagination_param_dto_1 = require("../../common/pagination-param.dto");
let BlogCollectionController = class BlogCollectionController {
    constructor(blogCollectionService) {
        this.blogCollectionService = blogCollectionService;
    }
    create(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.blogCollectionService.create(payload);
        });
    }
    findOne(params) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.blogCollectionService.getOne(params.id);
        });
    }
    findMany(queries) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.blogCollectionService.getMany(queries);
        });
    }
    update(param, payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.blogCollectionService.update(param.id, payload);
        });
    }
    delete(params) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.blogCollectionService.delete(params.id);
        });
    }
};
__decorate([
    (0, common_1.Post)(),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.POST),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateBlogCollectionDto]),
    __metadata("design:returntype", Promise)
], BlogCollectionController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.GET),
    __param(0, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.GetOneBlogCollectionDto]),
    __metadata("design:returntype", Promise)
], BlogCollectionController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.GET),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pagination_param_dto_1.PaginationParamDto]),
    __metadata("design:returntype", Promise)
], BlogCollectionController.prototype, "findMany", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.PUT),
    __param(0, (0, common_1.Param)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.GetOneBlogCollectionDto, dto_1.CreateBlogCollectionDto]),
    __metadata("design:returntype", Promise)
], BlogCollectionController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, keycloak_connect_tbs_1.Scopes)(enum_1.Scope.DELETE),
    __param(0, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.GetOneBlogCollectionDto]),
    __metadata("design:returntype", Promise)
], BlogCollectionController.prototype, "delete", null);
BlogCollectionController = __decorate([
    (0, swagger_1.ApiTags)("Admin - Blog Collection"),
    (0, common_1.Controller)("blog-collection"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, keycloak_connect_tbs_1.Resource)(enum_1.Controllers.BLOG_COLLECTION),
    __metadata("design:paramtypes", [blog_collection_service_1.BlogCollectionService])
], BlogCollectionController);
exports.BlogCollectionController = BlogCollectionController;
//# sourceMappingURL=blog-collection.controller.js.map