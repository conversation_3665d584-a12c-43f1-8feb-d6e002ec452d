import { GetBlogDto } from "./dto/get-blog.dto";
import { Model, PaginateModel } from "mongoose";
import { BlogDocument } from "./schema/blog.schema";
import { BlogCategoryDocument } from "./schema/blog-category.schema";
import { BlogTagDocument } from "./schema/blog-tag.schema";
export declare class BlogService {
    private blogModel;
    private blogTagModel;
    private blogCategoryModel;
    constructor(blogModel: PaginateModel<BlogDocument>, blogTagModel: Model<BlogTagDocument>, blogCategoryModel: Model<BlogCategoryDocument>);
    getBlogs(payload: GetBlogDto): Promise<any>;
}
