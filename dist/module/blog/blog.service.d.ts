/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { GetBlogDto } from "./dto/get-blog.dto";
import { Model, PaginateModel } from "mongoose";
import { BlogDocument } from "./schema/blog.schema";
import { BlogCategoryDocument } from "./schema/blog-category.schema";
import { BlogTagDocument } from "./schema/blog-tag.schema";
export declare class BlogService {
    private blogModel;
    private blogTagModel;
    private blogCategoryModel;
    constructor(blogModel: PaginateModel<BlogDocument>, blogTagModel: Model<BlogTagDocument>, blogCategoryModel: Model<BlogCategoryDocument>);
    getBlogs(payload: GetBlogDto): Promise<import("mongoose").PaginateResult<import("mongoose").Document<unknown, any, BlogDocument> & Omit<import("./schema/blog.schema").Blog & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, "page" | "limit" | "sort" | "select" | "forceCountFn"> & {
        page: number;
        limit: number;
        forceCountFn: boolean;
        select: string[];
        sort: {
            views: number;
            post_id: number;
        };
    }>>;
}
