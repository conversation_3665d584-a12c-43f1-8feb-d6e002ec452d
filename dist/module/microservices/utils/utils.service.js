"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UtilsService = void 0;
const common_1 = require("@nestjs/common");
const function_util_1 = require("../../../utils/function.util");
const tbs_site_config_1 = require("tbs-site-config");
let UtilsService = class UtilsService {
    constructor(configService, siteConfigService) {
        this.configService = configService;
        this.siteConfigService = siteConfigService;
    }
    getConfigByPrefix(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const { key, castValueTo, defaultValue, completeData } = params;
            try {
                const data = yield this.configService.getByPattern(key + "*");
                if (castValueTo) {
                    data.forEach((data) => {
                        data.value = (0, function_util_1.DataCaster)(data.value, castValueTo);
                    });
                }
                if (completeData) {
                    return data;
                }
                const result = {};
                data.map((item) => (result[item.key] = item.value));
                return result;
            }
            catch (e) {
                let val = defaultValue;
                if (castValueTo)
                    val = (0, function_util_1.DataCaster)(val, castValueTo);
                return val;
            }
        });
    }
    getConfig(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const data = JSON.parse(yield this.siteConfigService.get(params.key));
                if (params.completeData) {
                    return data;
                }
                return data.value;
            }
            catch (e) {
                console.log(e);
                return params.defaultValue;
            }
        });
    }
};
UtilsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tbs_site_config_1.TbsSiteConfigService,
        tbs_site_config_1.TbsSiteConfigService])
], UtilsService);
exports.UtilsService = UtilsService;
//# sourceMappingURL=utils.service.js.map