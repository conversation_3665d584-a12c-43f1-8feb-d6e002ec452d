import { TbsSiteConfigService } from "tbs-site-config";
export interface IgetConfigData {
    key: string;
    defaultValue?: string;
    completeData?: boolean;
    castValueTo?: "string" | "number" | "date";
}
export declare class UtilsService {
    private readonly configService;
    private readonly siteConfigService;
    constructor(configService: TbsSiteConfigService, siteConfigService: TbsSiteConfigService);
    getConfigByPrefix(params: IgetConfigData): Promise<{}>;
    getConfig(params: IgetConfigData): Promise<any>;
}
