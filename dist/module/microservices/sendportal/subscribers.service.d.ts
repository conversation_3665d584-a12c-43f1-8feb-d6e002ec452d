import { SendPortalService } from "./sendportal.service";
import { HttpService } from "@nestjs/axios";
export declare class SubscribersService {
    private readonly sendPortalService;
    private readonly httpService;
    constructor(sendPortalService: SendPortalService, httpService: HttpService);
    private host;
    private token;
    getSubscribers(): Promise<any>;
    getOneSubscriber(id: number): Promise<any>;
}
