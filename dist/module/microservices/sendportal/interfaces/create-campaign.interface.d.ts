type BooleanOption = "0" | "1";
export interface ICreateCampaign {
    name: string;
    subject: string;
    content?: string;
    template_id: string;
    email_service_id?: string;
    from_name?: string;
    from_email?: string;
    is_open_tracking?: BooleanOption;
    is_click_tracking?: BooleanOption;
    send_to_all?: BooleanOption;
    save_as_draft?: BooleanOption;
    send_to_card_number?: BooleanOption;
    scheduled_at?: string;
    params?: Array<Record<string, any>>;
    tags?: Array<number>;
}
export {};
