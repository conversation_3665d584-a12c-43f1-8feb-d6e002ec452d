"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendPortalModule = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const nestjs_elastic_apm_1 = require("../../../../modules/nestjs-elastic-apm");
const sendportal_service_1 = require("./sendportal.service");
const template_service_1 = require("./template.service");
const campaigns_service_1 = require("./campaigns.service");
const tags_service_1 = require("./tags.service");
const subscribers_service_1 = require("./subscribers.service");
let SendPortalModule = class SendPortalModule {
};
SendPortalModule = __decorate([
    (0, common_1.Module)({
        imports: [axios_1.HttpModule, config_1.ConfigModule, nestjs_elastic_apm_1.ApmModule.register()],
        controllers: [],
        providers: [sendportal_service_1.SendPortalService, template_service_1.TemplateService, campaigns_service_1.CampaignsService, tags_service_1.TagsService, subscribers_service_1.SubscribersService],
        exports: [sendportal_service_1.SendPortalService, template_service_1.TemplateService, campaigns_service_1.CampaignsService, tags_service_1.TagsService, subscribers_service_1.SubscribersService],
    })
], SendPortalModule);
exports.SendPortalModule = SendPortalModule;
//# sourceMappingURL=sendportal.module.js.map