import { SendPortalService } from "./sendportal.service";
import { HttpService } from "@nestjs/axios";
export declare class TemplateService {
    private readonly sendPortalService;
    private readonly httpService;
    constructor(sendPortalService: SendPortalService, httpService: HttpService);
    private host;
    private requestConfig;
    getTemplates(keyword: string): Promise<any>;
    getOneTemplate(id: number): Promise<any>;
}
