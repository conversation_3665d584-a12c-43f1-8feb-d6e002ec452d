import { HttpService } from "@nestjs/axios";
import { SendPortalService } from "./sendportal.service";
import { ICreateCampaign } from "./interfaces/create-campaign.interface";
import { ICampaignRecipients } from "./interfaces/create-campaign-recipients.interface";
export declare class CampaignsService {
    private readonly sendPortalService;
    private readonly httpService;
    constructor(sendPortalService: SendPortalService, httpService: HttpService);
    private host;
    private requestConfig;
    getCampaigns(): Promise<any>;
    getOneCampaign(id: number): Promise<any>;
    createCampaign(payload: ICreateCampaign): Promise<any>;
    updateCampaign(id: number, payload: ICreateCampaign): Promise<any>;
    setCampaignRecipients(campaignId: number, payload: ICampaignRecipients): Promise<any>;
    sendCampaign(campaignId: number): Promise<any>;
    resetCampaign(campaignId: number): Promise<any>;
}
