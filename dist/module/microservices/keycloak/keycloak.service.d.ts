import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { ICreatePermission } from "../../keycloak-generator/keycloak-generator.interface";
export declare class KeycloakMSService {
    private readonly httpService;
    private readonly configService;
    constructor(httpService: HttpService, configService: ConfigService);
    private readonly host;
    private readonly realm;
    private readonly clientId;
    private readonly clientSecret;
    private readonly baseUrl;
    private readonly wrapperHost;
    prefixRes: string;
    prefixPerm: string;
    getAllResources(): Promise<any>;
    getDetailResources(id: string): Promise<any>;
    createResources(name: string, resource_scopes: Array<string>): Promise<any>;
    getDetailClient(): Promise<any>;
    getAllPermissions(client_id: string): Promise<any>;
    createPermissions(body: ICreatePermission): Promise<any>;
    getAllScopes(client_id: string): Promise<any>;
    getAllPolicy(client_id: string): Promise<any>;
    _genPAT(): Promise<string>;
}
