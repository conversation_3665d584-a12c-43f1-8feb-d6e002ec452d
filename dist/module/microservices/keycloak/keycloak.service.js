"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeycloakMSService = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const config_1 = require("@nestjs/config");
let KeycloakMSService = class KeycloakMSService {
    constructor(httpService, configService) {
        this.httpService = httpService;
        this.configService = configService;
        this.host = this.configService.get("KEYCLOAK_HOST");
        this.realm = this.configService.get("KEYCLOAK_REALM");
        this.clientId = this.configService.get("KEYCLOAK_CLIENTID");
        this.clientSecret = this.configService.get("KEYCLOAK_SECRET");
        this.baseUrl = this.host + "/realms/" + this.realm;
        this.wrapperHost = this.configService.get("KEYCLOAK_WRAPPER_HOST");
        this.prefixRes = "res:utils:";
        this.prefixPerm = "scope:utils:";
    }
    getAllResources() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.baseUrl + "/authz/protection/resource_set";
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .get(url, { params: { name: this.prefixRes }, headers: { Authorization: token } })
                    .pipe((0, rxjs_1.map)((response) => response.data)));
            }
            catch (err) {
                console.log(err);
                throw err;
            }
        });
    }
    getDetailResources(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.baseUrl + "/authz/protection/resource_set/" + id;
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService.get(url, { headers: { Authorization: token } }).pipe((0, rxjs_1.map)((response) => response.data)));
            }
            catch (err) {
                console.log(err);
                throw err;
            }
        });
    }
    createResources(name, resource_scopes) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.baseUrl + "/authz/protection/resource_set";
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .post(url, { name, resource_scopes }, { headers: { Authorization: token } })
                    .pipe((0, rxjs_1.map)((response) => response.data)));
            }
            catch (err) {
                throw err;
            }
        });
    }
    getDetailClient() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.wrapperHost + "/api/v1/client/detail";
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .get(url, {
                    params: { client_id: this.clientId, realm_name: this.realm },
                    headers: { Authorization: token },
                })
                    .pipe((0, rxjs_1.map)((response) => { var _a; return (_a = response.data) === null || _a === void 0 ? void 0 : _a.data; })));
            }
            catch (err) {
                throw err;
            }
        });
    }
    getAllPermissions(client_id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.wrapperHost + "/api/v1/permission";
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .get(url, {
                    params: { client_id, page: 1, limit: 10000 },
                    headers: { Authorization: token },
                })
                    .pipe((0, rxjs_1.map)((response) => { var _a, _b; return ((_b = (_a = response.data) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.docs) || []; })));
            }
            catch (err) {
                throw err;
            }
        });
    }
    createPermissions(body) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const mock = Object.assign(Object.assign({}, body), { type: "scope", decision_strategy: "0", logic: "0" });
                const url = this.wrapperHost + "/api/v1/permission";
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .post(url, mock, { headers: { Authorization: token } })
                    .pipe((0, rxjs_1.map)((response) => { var _a; return ((_a = response.data) === null || _a === void 0 ? void 0 : _a.data) || {}; })));
            }
            catch (err) {
                console.log(err.response.data);
                throw err;
            }
        });
    }
    getAllScopes(client_id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.wrapperHost + "/api/v1/scope/all-by-client/" + client_id;
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .get(url, { params: { client_id }, headers: { Authorization: token } })
                    .pipe((0, rxjs_1.map)((response) => { var _a; return ((_a = response.data) === null || _a === void 0 ? void 0 : _a.data) || []; })));
            }
            catch (err) {
                throw err;
            }
        });
    }
    getAllPolicy(client_id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.wrapperHost + "/api/v1/permission/policy";
                const token = yield this._genPAT();
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .get(url, {
                    params: { client_id, page: 1, limit: 10000 },
                    headers: { Authorization: token },
                })
                    .pipe((0, rxjs_1.map)((response) => { var _a, _b; return ((_b = (_a = response.data) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.docs) || []; })));
            }
            catch (err) {
                throw err;
            }
        });
    }
    _genPAT() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = this.baseUrl + "/protocol/openid-connect/token";
                const params = {
                    grant_type: "client_credentials",
                    client_id: this.clientId,
                    client_secret: this.clientSecret,
                };
                return yield (0, rxjs_1.lastValueFrom)(this.httpService
                    .post(url, new URLSearchParams(params), {
                    headers: { "Content-Type": "application/x-www-form-urlencoded" },
                })
                    .pipe((0, rxjs_1.map)((response) => { var _a; return "Bearer " + ((_a = response.data) === null || _a === void 0 ? void 0 : _a.access_token); })));
            }
            catch (err) {
                throw err;
            }
        });
    }
};
KeycloakMSService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService, config_1.ConfigService])
], KeycloakMSService);
exports.KeycloakMSService = KeycloakMSService;
//# sourceMappingURL=keycloak.service.js.map