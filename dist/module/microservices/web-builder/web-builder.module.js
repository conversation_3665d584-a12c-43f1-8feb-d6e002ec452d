"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebBuilderModule = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const nestjs_elastic_apm_1 = require("../../../../modules/nestjs-elastic-apm");
const web_builder_listener_1 = require("./listener/web-builder.listener");
const web_builder_service_1 = require("./web-builder.service");
let WebBuilderModule = class WebBuilderModule {
};
WebBuilderModule = __decorate([
    (0, common_1.Module)({
        imports: [axios_1.HttpModule, config_1.ConfigModule, nestjs_elastic_apm_1.ApmModule.register()],
        controllers: [],
        providers: [web_builder_service_1.WebBuilderService, web_builder_listener_1.WebBuilderListener],
        exports: [web_builder_service_1.WebBuilderService, web_builder_listener_1.WebBuilderListener],
    })
], WebBuilderModule);
exports.WebBuilderModule = WebBuilderModule;
//# sourceMappingURL=web-builder.module.js.map