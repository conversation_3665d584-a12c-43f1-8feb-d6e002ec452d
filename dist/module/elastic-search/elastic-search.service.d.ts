import { ElasticsearchService } from "@nestjs/elasticsearch";
import { IBulkInsert, ISearchData, IUpdateById, IUpdateData } from "./elastic-search.interface";
import { GetRequest } from "@elastic/elasticsearch/lib/api/types";
export declare class ElasticSearchService {
    private readonly elasticService;
    constructor(elasticService: ElasticsearchService);
    bulkInsertDocument(params: IBulkInsert): Promise<import("@elastic/elasticsearch/lib/api/types").BulkResponse>;
    find(params: ISearchData): Promise<{
        docs: Record<string, any>[];
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number;
        nextPage: number;
    } | import("@elastic/elasticsearch/lib/api/types").SearchHitsMetadata<unknown>>;
    findOne(params: GetRequest): Promise<Record<string, any>>;
    update(params: IUpdateData): Promise<import("@elastic/elasticsearch/lib/api/types").UpdateByQueryResponse>;
    updateById(params: IUpdateById): Promise<import("@elastic/elasticsearch/lib/api/types").UpdateResponse<unknown>>;
    private _createIndexNoExist;
    private _prepareUpdateBody;
    private _validateId;
}
