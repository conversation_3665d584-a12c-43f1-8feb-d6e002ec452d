"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticSearchService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const function_util_1 = require("../../utils/function.util");
let ElasticSearchService = class ElasticSearchService {
    constructor(elasticService) {
        this.elasticService = elasticService;
    }
    bulkInsertDocument(params) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this._validateId(params.data);
            yield this._createIndexNoExist(params.index);
            const operations = params.data.flatMap((doc) => [{ index: { _index: params.index, _id: doc.id } }, doc]);
            return yield this.elasticService.bulk({ refresh: true, operations });
        });
    }
    find(params) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const paginateResponse = params.paginateResponse;
            delete params.paginateResponse;
            const data = yield this.elasticService.search(params);
            const page = params.from / params.size + 1;
            if (paginateResponse) {
                return (0, function_util_1.PaginateFormat)({
                    data: data.hits.hits.map((item) => item._source),
                    total: (_a = data.hits.total) === null || _a === void 0 ? void 0 : _a["value"],
                    offset: params.from,
                    limit: params.size,
                    page,
                });
            }
            return data.hits;
        });
    }
    findOne(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = yield this.elasticService.get(params, { ignore: [404, 400] });
            if (data.found)
                return data._source;
            return {};
        });
    }
    update(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const script = this._prepareUpdateBody(params.body);
            return yield this.elasticService.updateByQuery({ index: params.index, query: params.query, script });
        });
    }
    updateById(params) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.elasticService.update({ id: params.id, index: params.index, doc: params.data });
        });
    }
    _createIndexNoExist(index) {
        return __awaiter(this, void 0, void 0, function* () {
            const check = yield this.elasticService.indices.exists({ index });
            if (!check)
                yield this.elasticService.indices.create({ index });
        });
    }
    _prepareUpdateBody(body) {
        const source = [];
        Object.keys(body).map((key) => {
            source.push(`ctx._source.${key}=params.${key}`);
        });
        return { lang: "painless", source: source.join(";"), params: body };
    }
    _validateId(data) {
        return __awaiter(this, void 0, void 0, function* () {
            let isError = false;
            for (const item of data) {
                if (!item.id) {
                    isError = true;
                    break;
                }
            }
            if (isError)
                throw Error("Id cannot be empty");
            return true;
        });
    }
};
ElasticSearchService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticSearchService);
exports.ElasticSearchService = ElasticSearchService;
//# sourceMappingURL=elastic-search.service.js.map