"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManagerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const cache_manager_service_1 = require("./cache-manager.service");
const cache_manager_dto_1 = require("./cache-manager.dto");
let CacheManagerController = class CacheManagerController {
    constructor(cacheManagerService) {
        this.cacheManagerService = cacheManagerService;
    }
    purge() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.cacheManagerService.purge();
        });
    }
    purgePattern(pattern) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.cacheManagerService.purgePattern(pattern);
        });
    }
};
__decorate([
    (0, common_1.Get)("purge"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CacheManagerController.prototype, "purge", null);
__decorate([
    (0, common_1.Get)("purge-pattern"),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [cache_manager_dto_1.CacheManagerDto]),
    __metadata("design:returntype", Promise)
], CacheManagerController.prototype, "purgePattern", null);
CacheManagerController = __decorate([
    (0, swagger_1.ApiTags)("Cache Manager"),
    (0, common_1.Controller)("cache-manager"),
    (0, keycloak_connect_tbs_1.InternalAccess)(),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:paramtypes", [cache_manager_service_1.CacheManagerService])
], CacheManagerController);
exports.CacheManagerController = CacheManagerController;
//# sourceMappingURL=cache-manager.controller.js.map