"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManagerService = void 0;
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
const common_1 = require("@nestjs/common");
const ioredis_1 = require("ioredis");
let CacheManagerService = class CacheManagerService {
    constructor(redisService) {
        this.redisService = redisService;
    }
    purge() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield this.redisService.flushall();
                return result;
            }
            catch (e) {
                throw new common_1.HttpException("No data", common_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    purgePattern(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let { pattern, keyword } = params;
                let keywordToPurge = "";
                if (keyword) {
                    keyword = keyword.replace(/^\//, "");
                    keywordToPurge = `*${keyword}*`;
                }
                if (pattern) {
                    pattern = pattern.replace(/^\//, "");
                    keywordToPurge = pattern;
                }
                const keys = yield this.redisService.keys(keywordToPurge);
                const result = yield this.redisService.del(keys);
                return result;
            }
            catch (e) {
                return "no cache";
            }
        });
    }
};
CacheManagerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, nestjs_redis_1.InjectRedis)()),
    __metadata("design:paramtypes", [ioredis_1.default])
], CacheManagerService);
exports.CacheManagerService = CacheManagerService;
//# sourceMappingURL=cache-manager.service.js.map