"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManagerModule = void 0;
const common_1 = require("@nestjs/common");
const cache_manager_service_1 = require("./cache-manager.service");
const cache_manager_controller_1 = require("./cache-manager.controller");
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
let CacheManagerModule = class CacheManagerModule {
};
CacheManagerModule = __decorate([
    (0, common_1.Module)({
        controllers: [cache_manager_controller_1.CacheManagerController],
        providers: [cache_manager_service_1.CacheManagerService],
        imports: [nestjs_redis_1.RedisModule.forRoot({ config: { url: process.env.REDIS_HOST + ":" + process.env.REDIS_PORT_PUBLIC } })],
    })
], CacheManagerModule);
exports.CacheManagerModule = CacheManagerModule;
//# sourceMappingURL=cache-manager.module.js.map