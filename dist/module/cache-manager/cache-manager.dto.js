"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManagerDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const cache_manager_values_enum_1 = require("../../enum/cache-manager-values.enum");
class CacheManagerDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, enum: cache_manager_values_enum_1.CacheManagerValuesEnum }),
    __metadata("design:type", String)
], CacheManagerDto.prototype, "pattern", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], CacheManagerDto.prototype, "keyword", void 0);
exports.CacheManagerDto = CacheManagerDto;
//# sourceMappingURL=cache-manager.dto.js.map