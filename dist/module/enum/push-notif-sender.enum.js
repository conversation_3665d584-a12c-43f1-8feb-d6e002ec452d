"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatedFrom = exports.PushNotifTopic = exports.PushNotifType = exports.PushNotifTransport = void 0;
var PushNotifTransport;
(function (PushNotifTransport) {
    PushNotifTransport["TOPIC"] = "topic";
    PushNotifTransport["DEVICE_ID"] = "device_id";
})(PushNotifTransport = exports.PushNotifTransport || (exports.PushNotifTransport = {}));
var PushNotifType;
(function (PushNotifType) {
    PushNotifType["PROMOTION"] = "promotion";
    PushNotifType["ORDER"] = "order";
    PushNotifType["PURCHASE"] = "purchase";
})(PushNotifType = exports.PushNotifType || (exports.PushNotifType = {}));
exports.PushNotifTopic = {
    PUSH_BY_TOPIC: process.env.KAFKA_TOPIC_PREFIX + "-push.topic",
    PUSH_BY_SENDER: process.env.KAFKA_TOPIC_PREFIX + "-push.sender",
};
var CreatedFrom;
(function (CreatedFrom) {
    CreatedFrom["CMS"] = "cms";
    CreatedFrom["USER_ACTION"] = "user-action";
})(CreatedFrom = exports.CreatedFrom || (exports.CreatedFrom = {}));
//# sourceMappingURL=push-notif-sender.enum.js.map