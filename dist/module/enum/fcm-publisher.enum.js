"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KafkaClientName = exports.KafkaTopic = exports.TopicSendMapping = exports.FcmPublisherMongoTopicReverse = exports.FcmPublisherMongoTopic = exports.FcmPublisherTopic = exports.UserDefinedTopic = exports.FcmPublisherType = void 0;
var FcmPublisherType;
(function (FcmPublisherType) {
    FcmPublisherType["MEMBER_TIER"] = "MEMBER_TIER";
    FcmPublisherType["CARD_NUMBER"] = "CARD_NUMBER";
    FcmPublisherType["GENDER"] = "GENDER";
    FcmPublisherType["DOB"] = "DOB";
    FcmPublisherType["UNSUBSCRIBE_ALL"] = "UNSUBSCRIBE_ALL";
    FcmPublisherType["ADD_CART"] = "ADD_CART";
    FcmPublisherType["REMOVE_CART"] = "REMOVE_CART";
    FcmPublisherType["REMOVE_ALL_CART"] = "REMOVE_ALL_CART";
    FcmPublisherType["ADD_WISHLIST"] = "ADD_WISHLIST";
    FcmPublisherType["REMOVE_WISHLIST"] = "REMOVE_WISHLIST";
    FcmPublisherType["LAST_PURCHASE_DATE"] = "LAST_PURCHASE_DATE";
    FcmPublisherType["LAST_PURCHASE_CITY"] = "LAST_PURCHASE_CITY";
    FcmPublisherType["LAST_PURCHASE_REGION"] = "LAST_PURCHASE_REGION";
    FcmPublisherType["ALL"] = "ALL";
})(FcmPublisherType = exports.FcmPublisherType || (exports.FcmPublisherType = {}));
var UserDefinedTopic;
(function (UserDefinedTopic) {
    UserDefinedTopic["MEMBER_TIER"] = "MEMBER_TIER";
    UserDefinedTopic["CARD_NUMBER"] = "CARD_NUMBER";
    UserDefinedTopic["GENDER"] = "GENDER";
    UserDefinedTopic["DOB"] = "DOB";
    UserDefinedTopic["ALL"] = "ALL";
})(UserDefinedTopic = exports.UserDefinedTopic || (exports.UserDefinedTopic = {}));
exports.FcmPublisherTopic = {
    ALL: process.env.KAFKA_TOPIC_PREFIX + "-fcm.all",
    MEMBER_TIER: process.env.KAFKA_TOPIC_PREFIX + "-fcm.member",
    CARD_NUMBER: process.env.KAFKA_TOPIC_PREFIX + "-fcm.card_number",
    GENDER: process.env.KAFKA_TOPIC_PREFIX + "-fcm.gender",
    DOB: process.env.KAFKA_TOPIC_PREFIX + "-fcm.dob",
    UNSUBSCRIBE_ALL: process.env.KAFKA_TOPIC_PREFIX + "-fcm.unsubscribe_all",
    ADD_CART: process.env.KAFKA_TOPIC_PREFIX + "-fcm.add_to_cart",
    REMOVE_CART: process.env.KAFKA_TOPIC_PREFIX + "-fcm.remove_from_cart",
    REMOVE_ALL_CART: process.env.KAFKA_TOPIC_PREFIX + "-fcm.remove_all_cart",
    ADD_WISHLIST: process.env.KAFKA_TOPIC_PREFIX + "-fcm.add_to_wishlist",
    REMOVE_WISHLIST: process.env.KAFKA_TOPIC_PREFIX + "-fcm.remove_from_wishlist",
    LAST_PURCHASE_DATE: process.env.KAFKA_TOPIC_PREFIX + "-fcm.last_purchase_date",
    LAST_PURCHASE_CITY: process.env.KAFKA_TOPIC_PREFIX + "-fcm.last_purchase_city",
    LAST_PURCHASE_REGION: process.env.KAFKA_TOPIC_PREFIX + "-fcm.last_purchase_region",
};
var FcmPublisherMongoTopic;
(function (FcmPublisherMongoTopic) {
    FcmPublisherMongoTopic["ALL"] = "all";
    FcmPublisherMongoTopic["MEMBER_TIER"] = "member";
    FcmPublisherMongoTopic["CARD_NUMBER"] = "card_number";
    FcmPublisherMongoTopic["GENDER"] = "gender";
    FcmPublisherMongoTopic["DOB"] = "dob";
    FcmPublisherMongoTopic["ADD_CART"] = "add_to_cart";
    FcmPublisherMongoTopic["REMOVE_CART"] = "add_to_cart";
    FcmPublisherMongoTopic["REMOVE_ALL_CART"] = "add_to_cart";
    FcmPublisherMongoTopic["ADD_WISHLIST"] = "add_to_wishlist";
    FcmPublisherMongoTopic["REMOVE_WISHLIST"] = "add_to_wishlist";
    FcmPublisherMongoTopic["LAST_PURCHASE_DATE"] = "last_purchase_date";
    FcmPublisherMongoTopic["LAST_PURCHASE_CITY"] = "last_purchase_city";
    FcmPublisherMongoTopic["LAST_PURCHASE_REGION"] = "last_purchase_region";
})(FcmPublisherMongoTopic = exports.FcmPublisherMongoTopic || (exports.FcmPublisherMongoTopic = {}));
exports.FcmPublisherMongoTopicReverse = {
    all: "ALL",
    member: "MEMBER_TIER",
    card_number: "CARD_NUMBER",
    gender: "GENDER",
    dob: "DOB",
    add_to_cart: "ADD_CART",
    add_to_wishlist: "ADD_WISHLIST",
    last_purchase_date: "LAST_PURCHASE_DATE",
    last_purchase_city: "LAST_PURCHASE_CITY",
    last_purchase_region: "LAST_PURCHASE_REGION",
};
exports.TopicSendMapping = {
    ALL: true,
    MEMBER_TIER: true,
    CARD_NUMBER: false,
    GENDER: true,
    DOB: false,
    ADD_CART: false,
    ADD_WISHLIST: false,
    LAST_PURCHASE_DATE: false,
    LAST_PURCHASE_CITY: false,
    LAST_PURCHASE_REGION: false,
};
exports.KafkaTopic = {
    USER_FIRST_LOGIN_MOBILE: process.env.KAFKA_TOPIC_PREFIX + "-user.first-login-mobile",
};
exports.KafkaClientName = "kafka-client";
//# sourceMappingURL=fcm-publisher.enum.js.map