"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelHeaderReverse = exports.ChannelHeader = void 0;
var ChannelHeader;
(function (ChannelHeader) {
    ChannelHeader["WEB"] = "web";
    ChannelHeader["MOBILE"] = "mobile";
    ChannelHeader["POS"] = "pos";
    ChannelHeader["BC"] = "bc";
    ChannelHeader["MARKETPLACE"] = "marketplace";
})(ChannelHeader = exports.ChannelHeader || (exports.ChannelHeader = {}));
exports.ChannelHeaderReverse = {
    web: "34999",
    mobile: "34997",
    marketplace: "34996",
    lazada: "35902",
    elevania: "35903",
    blibli: "35904",
    shopee: "35905",
    tokopedia: "35906",
    jd_indonesia: "35907",
    zalora: "35908",
    b2b: "35909",
    tiktok: "35910",
    sociolla: "60001",
};
//# sourceMappingURL=channel-header.enum.js.map