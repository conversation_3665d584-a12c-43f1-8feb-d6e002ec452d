"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllowedMaxSize = exports.AllowedExt = exports.AllowedType = void 0;
var AllowedType;
(function (AllowedType) {
    AllowedType["PRODUCT_REVIEW"] = "product/review";
    AllowedType["PRODUCT_IMAGE"] = "product/image";
    AllowedType["PRODUCT_FLASH_SALE"] = "product/flash-sale";
    AllowedType["USER_PROFILE"] = "user/profile";
    AllowedType["PAYMENT_METHOD"] = "payment/method";
    AllowedType["E_RECEIPT"] = "payment/e-receipt";
    AllowedType["CAROUSEL"] = "util/carousel";
    AllowedType["POS"] = "util/pos";
    AllowedType["MEGA_MENU"] = "util/mega-menu";
    AllowedType["NOTIFICATION"] = "notification-assets";
})(AllowedType = exports.AllowedType || (exports.AllowedType = {}));
var AllowedExt;
(function (AllowedExt) {
    AllowedExt["PRODUCT_REVIEW"] = ".jpg,.jpeg,.png,.gif";
    AllowedExt["PRODUCT_IMAGE"] = ".jpg,.jpeg,.png,.gif";
    AllowedExt["PRODUCT_FLASH_SALE"] = ".jpg,.jpeg,.png,.gif";
    AllowedExt["USER_PROFILE"] = ".jpg,.jpeg,.png,.gif";
    AllowedExt["PAYMENT_METHOD"] = ".jpg,.jpeg,.png,.gif";
    AllowedExt["E_RECEIPT"] = ".pdf";
    AllowedExt["CAROUSEL"] = ".jpg,.jpeg,.png,.gif";
    AllowedExt["POS"] = ".jpg,.jpeg,.png,.gif,.mp4";
    AllowedExt["MEGA_MENU"] = ".jpg,.jpeg,.png,.gif";
    AllowedExt["NOTIFICATION"] = ".jpg,.jpeg,.png";
})(AllowedExt = exports.AllowedExt || (exports.AllowedExt = {}));
var AllowedMaxSize;
(function (AllowedMaxSize) {
    AllowedMaxSize["PRODUCT_REVIEW"] = "300 kb";
    AllowedMaxSize["PRODUCT_IMAGE"] = "300 kb";
    AllowedMaxSize["PRODUCT_FLASH_SALE"] = "300 kb";
    AllowedMaxSize["USER_PROFILE"] = "300 kb";
    AllowedMaxSize["PAYMENT_METHOD"] = "300 kb";
    AllowedMaxSize["E_RECEIPT"] = "300 kb";
    AllowedMaxSize["CAROUSEL"] = "300 kb";
    AllowedMaxSize["POS"] = "300 kb";
    AllowedMaxSize["MEGA_MENU"] = "300 kb";
    AllowedMaxSize["NOTIFICATION"] = "1 mb";
})(AllowedMaxSize = exports.AllowedMaxSize || (exports.AllowedMaxSize = {}));
//# sourceMappingURL=file.enum.js.map