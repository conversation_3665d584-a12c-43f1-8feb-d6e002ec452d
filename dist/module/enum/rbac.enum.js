"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Controllers = exports.Scope = void 0;
var Scope;
(function (Scope) {
    Scope["POST"] = "create";
    Scope["PUT"] = "update";
    Scope["PATCH"] = "update";
    Scope["GET"] = "read";
    Scope["DELETE"] = "delete";
})(Scope = exports.Scope || (exports.Scope = {}));
var Controllers;
(function (Controllers) {
    Controllers["BLOG"] = "res:utils:blog";
    Controllers["CARBON"] = "res:utils:carbon";
    Controllers["CAROUSEL"] = "res:utils:carousel";
    Controllers["FILE_UPLOADER"] = "res:utils:file-uploader";
    Controllers["HOME"] = "res:utils:home";
    Controllers["NEWSLETTER"] = "res:utils:newsletter";
    Controllers["OFFERS"] = "res:utils:offers";
    Controllers["ADMIN_SITE_CONFIGS"] = "res:utils:admin-site-configs";
    Controllers["SITE_CONFIGS"] = "res:utils:site-configs";
    Controllers["ADMIN_STORE"] = "res:utils:admin-store";
    Controllers["STORE"] = "res:utils:store";
    Controllers["ADMIN_SCREEN"] = "res:utils:admin-screen";
    Controllers["SCREEN"] = "res:utils:screen";
    Controllers["ADMIN_BUILDERIO"] = "res:utils:admin-builder-io";
    Controllers["INBOX"] = "res:utils:inbox";
    Controllers["BLOG_COLLECTION"] = "res:utils:blog-collection";
    Controllers["PUSH_NOTIFICATION"] = "res:utils:push-push-notif-sender";
    Controllers["FCM_PUBLISHER"] = "res:utils:fcm-publisher";
    Controllers["ADMIN_CRM_HOME_CONFIG"] = "res:utils:crm-admin";
    Controllers["ADMIN_NOTIFICATION"] = "res:utils:admin-notification";
    Controllers["NOTIFICATION"] = "res:utils:notification";
    Controllers["EMAIL_NOTIFICATION"] = "res:utils:email-notification";
    Controllers["EMAIL_NOTIFICATION_TEMPLATE"] = "res:utils:email-template";
    Controllers["ADMIN_STORE_GROUP"] = "res:utils:admin-store-group";
    Controllers["STORE_GROUP"] = "res:utils:store-group";
    Controllers["ADMIN_IN_STORE_SERVICE"] = "res:utils:admin-in-store-service";
    Controllers["IN_STORE_SERVICE"] = "res:utils:in-store-service";
    Controllers["ADMIN_PLAYLIST"] = "res:utils:admin-playlist";
    Controllers["PLAYLIST"] = "res:utils:playlist";
})(Controllers = exports.Controllers || (exports.Controllers = {}));
//# sourceMappingURL=rbac.enum.js.map