export declare enum FcmPublisherType {
    MEMBER_TIER = "MEMBER_TIER",
    CARD_NUMBER = "CARD_NUMBER",
    GENDER = "GENDER",
    DOB = "DOB",
    UNSUBSCRIBE_ALL = "UNSUBSCRIBE_ALL",
    ADD_CART = "ADD_CART",
    REMOVE_CART = "REMOVE_CART",
    REMOVE_ALL_CART = "REMOVE_ALL_CART",
    ADD_WISHLIST = "ADD_WISHLIST",
    REMOVE_WISHLIST = "REMOVE_WISHLIST",
    LAST_PURCHASE_DATE = "LAST_PURCHASE_DATE",
    LAST_PURCHASE_CITY = "LAST_PURCHASE_CITY",
    LAST_PURCHASE_REGION = "LAST_PURCHASE_REGION",
    ALL = "ALL"
}
export declare enum UserDefinedTopic {
    MEMBER_TIER = "MEMBER_TIER",
    CARD_NUMBER = "CARD_NUMBER",
    GENDER = "GENDER",
    DOB = "DOB",
    ALL = "ALL"
}
export declare const FcmPublisherTopic: {
    ALL: string;
    MEMBER_TIER: string;
    CARD_NUMBER: string;
    GENDER: string;
    DOB: string;
    UNSUBSCRIBE_ALL: string;
    ADD_CART: string;
    REMOVE_CART: string;
    REMOVE_ALL_CART: string;
    ADD_WISHLIST: string;
    REMOVE_WISHLIST: string;
    LAST_PURCHASE_DATE: string;
    LAST_PURCHASE_CITY: string;
    LAST_PURCHASE_REGION: string;
};
export declare enum FcmPublisherMongoTopic {
    ALL = "all",
    MEMBER_TIER = "member",
    CARD_NUMBER = "card_number",
    GENDER = "gender",
    DOB = "dob",
    ADD_CART = "add_to_cart",
    REMOVE_CART = "add_to_cart",
    REMOVE_ALL_CART = "add_to_cart",
    ADD_WISHLIST = "add_to_wishlist",
    REMOVE_WISHLIST = "add_to_wishlist",
    LAST_PURCHASE_DATE = "last_purchase_date",
    LAST_PURCHASE_CITY = "last_purchase_city",
    LAST_PURCHASE_REGION = "last_purchase_region"
}
export declare const FcmPublisherMongoTopicReverse: {
    all: string;
    member: string;
    card_number: string;
    gender: string;
    dob: string;
    add_to_cart: string;
    add_to_wishlist: string;
    last_purchase_date: string;
    last_purchase_city: string;
    last_purchase_region: string;
};
export declare const TopicSendMapping: {
    ALL: boolean;
    MEMBER_TIER: boolean;
    CARD_NUMBER: boolean;
    GENDER: boolean;
    DOB: boolean;
    ADD_CART: boolean;
    ADD_WISHLIST: boolean;
    LAST_PURCHASE_DATE: boolean;
    LAST_PURCHASE_CITY: boolean;
    LAST_PURCHASE_REGION: boolean;
};
export declare const KafkaTopic: {
    USER_FIRST_LOGIN_MOBILE: string;
};
export declare const KafkaClientName = "kafka-client";
