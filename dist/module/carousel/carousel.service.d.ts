/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { PaginateModel } from "mongoose";
import { CreateCarouselDto } from "./dto/create-carousel.dto";
import { GetCarouselDto } from "./dto/get-carousel-dto";
import { UpdateCarouselDto } from "./dto/update-carousel.dto";
import { CarouselDocument } from "./schema/carousel.schema";
export declare class CarouselService {
    private carouselModel;
    constructor(carouselModel: PaginateModel<CarouselDocument>);
    findAllAdmin(params: GetCarouselDto): Promise<import("mongoose").PaginateResult<import("./schema/carousel.schema").Carousel & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>>;
    findOneAdmin(id: string): Promise<import("./schema/carousel.schema").Carousel & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    create(payload: CreateCarouselDto): Promise<import("./schema/carousel.schema").Carousel & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    update(_id: string, data: UpdateCarouselDto): Promise<import("./schema/carousel.schema").Carousel & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    remove(_id: string): Promise<import("./schema/carousel.schema").Carousel & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    findAllPublic(): Promise<(import("./schema/carousel.schema").Carousel & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    })[]>;
    carouselStatusDeactivator(): Promise<any>;
}
