import { PaginateModel } from "mongoose";
import { CreateCarouselDto } from "./dto/create-carousel.dto";
import { GetCarouselDto } from "./dto/get-carousel-dto";
import { UpdateCarouselDto } from "./dto/update-carousel.dto";
import { CarouselDocument } from "./schema/carousel.schema";
export declare class CarouselService {
    private carouselModel;
    constructor(carouselModel: PaginateModel<CarouselDocument>);
    findAllAdmin(params: GetCarouselDto): Promise<any>;
    findOneAdmin(id: string): Promise<any>;
    create(payload: CreateCarouselDto): Promise<any>;
    update(_id: string, data: UpdateCarouselDto): Promise<any>;
    remove(_id: string): Promise<any>;
    findAllPublic(): Promise<any>;
    carouselStatusDeactivator(): Promise<any>;
}
