import { EventEmitter2 } from "@nestjs/event-emitter";
import { CarouselService } from "./carousel.service";
import { CreateCarouselDto } from "./dto/create-carousel.dto";
import { GetCarouselDto } from "./dto/get-carousel-dto";
export declare class CarouselController {
    private readonly carouselService;
    private readonly eventEmitter;
    constructor(carouselService: CarouselService, eventEmitter: EventEmitter2);
    find(pagination: GetCarouselDto): Promise<any>;
    findOne(_id: string): Promise<any>;
    create(payload: CreateCarouselDto): Promise<any>;
    update(id: string, payload: CreateCarouselDto): Promise<any>;
    remove(_id: string): Promise<any>;
    findAllPublic(): Promise<any>;
    carouselStatusDeactivator(): Promise<any>;
}
