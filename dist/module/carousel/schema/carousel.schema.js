"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarouselSchema = exports.Carousel = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoosePaginate = require("mongoose-paginate-v2");
const function_util_1 = require("../../../utils/function.util");
let Carousel = class Carousel extends mongoose_2.Document {
};
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, get: function_util_1.ConvertPathToUrl, set: function_util_1.ConvertUrlToPath }),
    __metadata("design:type", String)
], Carousel.prototype, "web_image", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String, get: function_util_1.ConvertPathToUrl, set: function_util_1.ConvertUrlToPath }),
    __metadata("design:type", String)
], Carousel.prototype, "mobile_image", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Boolean }),
    __metadata("design:type", Boolean)
], Carousel.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "link", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "tnc", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Date }),
    __metadata("design:type", Date)
], Carousel.prototype, "start_date", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Date }),
    __metadata("design:type", Date)
], Carousel.prototype, "end_date", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Number }),
    __metadata("design:type", Number)
], Carousel.prototype, "position", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "title_color", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "desc_color", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "button_color", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], Carousel.prototype, "button_txt_color", void 0);
Carousel = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        toJSON: { getters: true },
        toObject: { getters: true },
    })
], Carousel);
exports.Carousel = Carousel;
exports.CarouselSchema = mongoose_1.SchemaFactory.createForClass(Carousel);
exports.CarouselSchema.plugin(mongoosePaginate);
//# sourceMappingURL=carousel.schema.js.map