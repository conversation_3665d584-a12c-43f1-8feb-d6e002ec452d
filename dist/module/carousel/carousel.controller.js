"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarouselController = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const enum_1 = require("../enum");
const carousel_service_1 = require("./carousel.service");
const create_carousel_dto_1 = require("./dto/create-carousel.dto");
const get_carousel_dto_1 = require("./dto/get-carousel-dto");
let CarouselController = class CarouselController {
    constructor(carouselService, eventEmitter) {
        this.carouselService = carouselService;
        this.eventEmitter = eventEmitter;
    }
    find(pagination) {
        return this.carouselService.findAllAdmin(pagination);
    }
    findOne(_id) {
        return this.carouselService.findOneAdmin(_id);
    }
    create(payload) {
        this.eventEmitter.emit("rebuild-home");
        return this.carouselService.create(payload);
    }
    update(id, payload) {
        this.eventEmitter.emit("rebuild-home");
        return this.carouselService.update(id, payload);
    }
    remove(_id) {
        this.eventEmitter.emit("rebuild-home");
        return this.carouselService.remove(_id);
    }
    findAllPublic() {
        return this.carouselService.findAllPublic();
    }
    carouselStatusDeactivator() {
        this.eventEmitter.emit("rebuild-home");
        return this.carouselService.carouselStatusDeactivator();
    }
};
__decorate([
    (0, common_1.Get)("/admin"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_carousel_dto_1.GetCarouselDto]),
    __metadata("design:returntype", void 0)
], CarouselController.prototype, "find", null);
__decorate([
    (0, common_1.Get)("/admin/:id"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CarouselController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)("/admin"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_carousel_dto_1.CreateCarouselDto]),
    __metadata("design:returntype", void 0)
], CarouselController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)("/admin/:id"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_carousel_dto_1.CreateCarouselDto]),
    __metadata("design:returntype", void 0)
], CarouselController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)("/admin/:id"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [enum_1.Role.Admin, `realm:app-${enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CarouselController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CarouselController.prototype, "findAllPublic", null);
__decorate([
    (0, common_1.Get)("/deactivate"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CarouselController.prototype, "carouselStatusDeactivator", null);
CarouselController = __decorate([
    (0, swagger_1.ApiTags)("Carousel"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)("carousel"),
    __metadata("design:paramtypes", [carousel_service_1.CarouselService, typeof (_a = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _a : Object])
], CarouselController);
exports.CarouselController = CarouselController;
//# sourceMappingURL=carousel.controller.js.map