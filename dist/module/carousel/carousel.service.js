"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarouselService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
let CarouselService = class CarouselService {
    constructor(carouselModel) {
        this.carouselModel = carouselModel;
    }
    findAllAdmin(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            if (params.title)
                filter.title = new RegExp(params.title, "i");
            if (params.description)
                filter.description = new RegExp(params.description, "i");
            if (typeof params.status === "boolean")
                filter.is_active = params.status;
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            const result = yield this.carouselModel.paginate(filter, options);
            return result;
        });
    }
    findOneAdmin(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.carouselModel.findById(id);
            return result;
        });
    }
    create(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.carouselModel.create(Object.assign({}, payload));
        });
    }
    update(_id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.carouselModel.findOneAndUpdate({ _id }, data, { new: true }).exec();
        });
    }
    remove(_id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.carouselModel.findOneAndDelete({ _id }).exec();
        });
    }
    findAllPublic() {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.carouselModel
                .find({
                is_active: true,
                $and: [{ start_date: { $lte: Date.now() } }, { end_date: { $gte: Date.now() } }],
            })
                .sort({ position: 1 })
                .exec();
            for (const data of result) {
                if (!data.mobile_image) {
                    data.mobile_image = data.web_image;
                }
            }
            return result;
        });
    }
    carouselStatusDeactivator() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.carouselModel
                .updateMany({ end_date: { $lt: new Date() }, status: true }, { status: false }, { new: true })
                .exec();
            yield this.carouselModel
                .updateMany({ $and: [{ start_date: { $lte: Date.now() } }, { end_date: { $gte: Date.now() } }], status: false }, { status: true }, { new: true })
                .exec();
        });
    }
};
CarouselService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Carousel")),
    __metadata("design:paramtypes", [Object])
], CarouselService);
exports.CarouselService = CarouselService;
//# sourceMappingURL=carousel.service.js.map