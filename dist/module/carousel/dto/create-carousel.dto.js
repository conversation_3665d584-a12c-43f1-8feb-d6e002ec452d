"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCarouselDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const image_url_validator_1 = require("../../../decorator/image-url-validator");
const transform_boolean_decorator_1 = require("../../../decorator/transform-boolean.decorator");
class CreateCarouselDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: "TBS Carousel", required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "https://icarus-s3.s3.ap-southeast-3.amazonaws.com/carousels/c2b8bffb-30cf-4827-a7ff-35dd6d4f2ec8.jpeg",
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsUrl)(),
    (0, image_url_validator_1.IsImageUrlRegistered)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "web_image", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "https://icarus-s3.s3.ap-southeast-3.amazonaws.com/carousels/c2b8bffb-30cf-4827-a7ff-35dd6d4f2ec8.jpeg",
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsUrl)(),
    (0, image_url_validator_1.IsImageUrlRegistered)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "mobile_image", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Tbs Promo New Year", required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    (0, transform_boolean_decorator_1.TransformBoolean)(),
    __metadata("design:type", Boolean)
], CreateCarouselDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "any-2-edt-for-899k", required: false, description: "expected value is url_key" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "link", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Belanja minimal 100k", required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "tnc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Transform)(({ value }) => new Date(value)),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CreateCarouselDto.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Transform)(({ value }) => new Date(value)),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CreateCarouselDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: true, type: Number }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateCarouselDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, type: String }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsHexColor)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "title_color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, type: String }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsHexColor)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "desc_color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, type: String }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsHexColor)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "button_color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, type: String }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsHexColor)(),
    __metadata("design:type", String)
], CreateCarouselDto.prototype, "button_txt_color", void 0);
exports.CreateCarouselDto = CreateCarouselDto;
//# sourceMappingURL=create-carousel.dto.js.map