"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrmService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const schema_1 = require("./schema");
let CrmService = class CrmService {
    constructor(crmContentmodel) {
        this.crmContentmodel = crmContentmodel;
    }
    create(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.crmContentmodel.create(payload);
        });
    }
    findOne(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.crmContentmodel.findById(id);
        });
    }
    findMany(queries) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { status, keyword, limit = 15, page = 1, sort } = queries;
                const filter = {};
                if (![undefined, null].includes(status))
                    filter["is_active"] = status;
                if (keyword) {
                    filter["$or"] = [{ title: { $regex: keyword, $options: "i" } }, { url: { $regex: keyword, $options: "i" } }];
                }
                const options = {
                    page: Number(page),
                    limit: Number(limit),
                    forceCountFn: true,
                    sort: { position: 1 },
                };
                return yield this.crmContentmodel.paginate(filter, options);
            }
            catch (err) {
                throw err;
            }
        });
    }
    update(id, payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.crmContentmodel.findByIdAndUpdate({ _id: id }, payload);
        });
    }
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.crmContentmodel.findByIdAndDelete({ _id: id });
        });
    }
    findActiveContents() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.crmContentmodel.find({
                is_active: true,
                $or: [
                    {
                        $and: [{ startDate: { $exists: false } }, { endDate: { $exists: false } }],
                    },
                    {
                        $and: [{ startDate: null }, { endDate: null }],
                    },
                    {
                        $and: [{ startDate: { $lte: Date.now() } }, { endDate: { $gte: Date.now() } }],
                    },
                ],
            }, {}, { sort: { position: 1 } });
        });
    }
};
CrmService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(schema_1.CRMContent.name)),
    __metadata("design:paramtypes", [Object])
], CrmService);
exports.CrmService = CrmService;
//# sourceMappingURL=crm.service.js.map