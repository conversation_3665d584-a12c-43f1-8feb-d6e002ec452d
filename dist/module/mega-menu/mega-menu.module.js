"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaMenuModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mega_menu_controller_1 = require("./mega-menu.controller");
const mega_menu_service_1 = require("./mega-menu.service");
const mega_menu_schema_1 = require("./schema/mega-menu.schema");
const product_group_module_1 = require("../microservices/product/product-group.module");
const config_1 = require("@nestjs/config");
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
const axios_1 = require("@nestjs/axios");
let MegaMenuModule = class MegaMenuModule {
};
MegaMenuModule = __decorate([
    (0, common_1.Module)({
        controllers: [mega_menu_controller_1.MegaMenuController],
        providers: [mega_menu_service_1.MegaMenuService],
        imports: [
            mongoose_1.MongooseModule.forFeature([
                {
                    name: "MegaMenu",
                    schema: mega_menu_schema_1.MegaMenuSchema,
                },
            ]),
            product_group_module_1.ProductGroupModule,
            config_1.ConfigModule,
            nestjs_redis_1.RedisModule.forRoot({ config: { url: process.env.REDIS_HOST + ":" + process.env.REDIS_PORT_PUBLIC } }),
            axios_1.HttpModule,
        ],
    })
], MegaMenuModule);
exports.MegaMenuModule = MegaMenuModule;
//# sourceMappingURL=mega-menu.module.js.map