"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MegaMenu_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaMenuSchema = exports.MegaMenu = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mega_menu_align_enum_1 = require("../enum/mega-menu-align-enum");
let MegaMenu = MegaMenu_1 = class MegaMenu extends mongoose_2.Document {
};
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, unique: true }),
    __metadata("design:type", String)
], MegaMenu.prototype, "menu_id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], MegaMenu.prototype, "product_group_id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, enum: mega_menu_align_enum_1.MegaMenuType }),
    __metadata("design:type", String)
], MegaMenu.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String }),
    __metadata("design:type", String)
], MegaMenu.prototype, "url_key", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String }),
    __metadata("design:type", String)
], MegaMenu.prototype, "customName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: String, enum: mega_menu_align_enum_1.MegaMenuAlignEnum, default: mega_menu_align_enum_1.MegaMenuAlignEnum.left }),
    __metadata("design:type", String)
], MegaMenu.prototype, "align", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Boolean }),
    __metadata("design:type", Boolean)
], MegaMenu.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [
            {
                img_url: {
                    type: String,
                    required: true,
                },
                path_url: {
                    type: String,
                    required: true,
                },
            },
        ],
        _id: false,
    }),
    __metadata("design:type", Array)
], MegaMenu.prototype, "image", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Number, default: 1 }),
    __metadata("design:type", Number)
], MegaMenu.prototype, "position", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: mongoose_2.SchemaTypes.Mixed, ref: MegaMenu_1.name }),
    __metadata("design:type", Array)
], MegaMenu.prototype, "children", void 0);
MegaMenu = MegaMenu_1 = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
    })
], MegaMenu);
exports.MegaMenu = MegaMenu;
exports.MegaMenuSchema = mongoose_1.SchemaFactory.createForClass(MegaMenu);
//# sourceMappingURL=mega-menu.schema.js.map