/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { Document } from "mongoose";
import { MegaMenuAlignEnum } from "../enum/mega-menu-align-enum";
import { IMegaMenuImageInterface } from "../interface/image-interface";
export declare class MegaMenu extends Document {
    menu_id: string;
    product_group_id: string;
    type: string;
    url_key: string;
    customName: string;
    align: MegaMenuAlignEnum;
    status: boolean;
    image: IMegaMenuImageInterface[];
    position: number;
    children: Array<Record<string, any>>;
}
export type MegaMenuDocument = MegaMenu & Document;
export declare const MegaMenuSchema: import("mongoose").Schema<MegaMenu, import("mongoose").Model<MegaMenu, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, MegaMenu>;
