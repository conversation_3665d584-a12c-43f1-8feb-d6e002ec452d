"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaMenuService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const product_group_service_1 = require("../microservices/product/product-group.service");
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
const ioredis_1 = require("ioredis");
const rxjs_1 = require("rxjs");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const purge_enum_1 = require("../home/<USER>/purge.enum");
let MegaMenuService = class MegaMenuService {
    constructor(model, productGroupMs, redisService, configService, httpService) {
        this.model = model;
        this.productGroupMs = productGroupMs;
        this.redisService = redisService;
        this.configService = configService;
        this.httpService = httpService;
    }
    create(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const megaMenu = [];
            try {
                for (const data of payload.data) {
                    if (data.image) {
                        for (const image of data.image) {
                            image.img_url = image.img_url.split(process.env.AWS_S3_BASE_URL)[1];
                        }
                    }
                    megaMenu.push(yield this.model.findOneAndUpdate({ menu_id: data.menu_id }, data, { new: true, upsert: true }).exec());
                }
                yield this.model.deleteMany({ _id: { $in: payload.delete } }).exec();
            }
            catch (e) {
                console.log(e);
                throw new common_1.HttpException("Failed to create / edit mega menu", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return megaMenu;
        });
    }
    getMegaMenuCustomer(req) {
        return __awaiter(this, void 0, void 0, function* () {
            const purge = req.headers.purge || purge_enum_1.PurgeEnum.keep;
            const cacheKey = `megamenu-home`;
            if (purge == purge_enum_1.PurgeEnum.keep) {
                const home = yield this.redisService.get(cacheKey);
                if (home)
                    return JSON.parse(home);
            }
            const result = yield this.model.find({ status: true }).sort({ position: 1 }).lean();
            if (result.length <= 0) {
                return [];
            }
            const product_group_id = [];
            for (const data of result) {
                if (data.product_group_id != "") {
                    product_group_id.push(data.product_group_id);
                }
                for (const firstChildren of data.children) {
                    if (firstChildren.product_group_id != "") {
                        product_group_id.push(firstChildren.product_group_id);
                    }
                    for (const secondChildren of firstChildren.children) {
                        if (secondChildren.product_group_id != "") {
                            product_group_id.push(secondChildren.product_group_id);
                        }
                    }
                }
            }
            const multipleId = product_group_id.toString().replace(/,/g, ";");
            const categories = yield this.productGroupMs.getAllResources(multipleId);
            const productRange = [];
            const categoryMap = categories.data.reduce((final, tmp) => {
                final[tmp._id] = {
                    url_key: tmp.url_key,
                    name: tmp.name,
                };
                return final;
            }, {});
            const finalResult = result.map((menu1) => {
                let { product_group_id: cat1, children: child1 } = menu1, rest1 = __rest(menu1, ["product_group_id", "children"]);
                if (menu1.image) {
                    for (const data of menu1.image) {
                        data.img_url = process.env.AWS_S3_BASE_URL + data.img_url;
                    }
                }
                const category1 = categoryMap[cat1];
                child1 = child1.map((menu2) => {
                    let { product_group_id: cat2, children: child2 } = menu2, rest2 = __rest(menu2, ["product_group_id", "children"]);
                    const category2 = categoryMap[cat2];
                    child2 = child2.map((menu3) => {
                        const { product_group_id: cat3 } = menu3, rest3 = __rest(menu3, ["product_group_id"]);
                        const category3 = categoryMap[cat3];
                        return Object.assign(Object.assign({ product_group_id: cat3 }, category3), rest3);
                    });
                    return Object.assign(Object.assign(Object.assign({ product_group_id: cat2 }, category2), rest2), { children: child2 });
                });
                return Object.assign(Object.assign(Object.assign({ product_group_id: cat1 }, category1), rest1), { children: child1 });
            });
            finalResult.map((item) => {
                if (item.url_key == "range") {
                    item.children = productRange.map((range) => {
                        range["menu_id"] = "111";
                        range["position"] = "1";
                        range["type"] = "product_group";
                        range["customName"] = "";
                        range["children"] = [];
                        range["status"] = true;
                        return range;
                    });
                }
                return item;
            });
            yield this.redisService.set(cacheKey, JSON.stringify(finalResult), "EX", process.env.REDIS_URL_CHECKER_CACHED_LIFETIME);
            return finalResult;
        });
    }
    getMegaMenuAdmin() {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.model.find().sort({ position: 1 }).lean();
            if (result.length <= 0) {
                return [];
            }
            const product_group_id = [];
            for (const data of result) {
                if (data.product_group_id != "") {
                    product_group_id.push(data.product_group_id);
                }
                for (const firstChildren of data.children) {
                    if (firstChildren.product_group_id != "") {
                        product_group_id.push(firstChildren.product_group_id);
                    }
                    for (const secondChildren of firstChildren.children) {
                        if (secondChildren.product_group_id != "") {
                            product_group_id.push(secondChildren.product_group_id);
                        }
                    }
                }
            }
            const multipleId = product_group_id.toString().replace(/,/g, ";");
            const categories = yield this.productGroupMs.getAllResources(multipleId);
            const categoryMap = categories.data.reduce((final, tmp) => {
                final[tmp._id] = {
                    url_key: tmp.url_key,
                    name: tmp.name,
                };
                return final;
            }, {});
            const finalResult = result.map((menu1) => {
                let { product_group_id: cat1, children: child1 } = menu1, rest1 = __rest(menu1, ["product_group_id", "children"]);
                if (menu1.image) {
                    for (const data of menu1.image) {
                        data.img_url = process.env.AWS_S3_BASE_URL + data.img_url;
                    }
                }
                const category1 = categoryMap[cat1];
                child1 = child1.map((menu2) => {
                    let { product_group_id: cat2, children: child2 } = menu2, rest2 = __rest(menu2, ["product_group_id", "children"]);
                    const category2 = categoryMap[cat2];
                    child2 = child2.map((menu3) => {
                        const { product_group_id: cat3 } = menu3, rest3 = __rest(menu3, ["product_group_id"]);
                        const category3 = categoryMap[cat3];
                        return Object.assign(Object.assign({ product_group_id: cat3 }, category3), rest3);
                    });
                    return Object.assign(Object.assign(Object.assign({ product_group_id: cat2 }, category2), rest2), { children: child2 });
                });
                return Object.assign(Object.assign(Object.assign({ product_group_id: cat1 }, category1), rest1), { children: child1 });
            });
            return finalResult;
        });
    }
    _getAllRange(req) {
        return __awaiter(this, void 0, void 0, function* () {
            const host = this.configService.get("OMS_HOST");
            const url = `${host}/api/v1/product-group?page=1&limit=100&is_range=true&is_lean=true`;
            try {
                const requestUrl = url;
                const requestConfig = {
                    headers: {
                        Authorization: req.headers.authorization,
                    },
                };
                const responseData = yield (0, rxjs_1.lastValueFrom)(this.httpService.get(requestUrl, requestConfig).pipe((0, rxjs_1.map)((response) => {
                    return response.data;
                })));
                return responseData.data.docs;
            }
            catch (e) {
                console.error(e);
                throw new common_1.HttpException(e, e.response.status);
            }
            return [];
        });
    }
};
MegaMenuService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("MegaMenu")),
    __param(2, (0, nestjs_redis_1.InjectRedis)()),
    __metadata("design:paramtypes", [mongoose_2.Model,
        product_group_service_1.ProductGroupMSService, typeof (_a = typeof ioredis_1.default !== "undefined" && ioredis_1.default) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof axios_1.HttpService !== "undefined" && axios_1.HttpService) === "function" ? _c : Object])
], MegaMenuService);
exports.MegaMenuService = MegaMenuService;
//# sourceMappingURL=mega-menu.service.js.map