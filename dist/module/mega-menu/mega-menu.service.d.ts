import { Model } from "mongoose";
import { ProductGroupMSService } from "../microservices/product/product-group.service";
import { MegaMenuDto } from "./dto/mega-menu.dto";
import { MegaMenuDocument } from "./schema/mega-menu.schema";
import Redis from "ioredis";
import { ConfigService } from "@nestjs/config";
import { HttpService } from "@nestjs/axios";
export declare class MegaMenuService {
    private model;
    private readonly productGroupMs;
    private readonly redisService;
    private configService;
    private readonly httpService;
    constructor(model: Model<MegaMenuDocument>, productGroupMs: ProductGroupMSService, redisService: Redis, configService: ConfigService, httpService: HttpService);
    create(payload: MegaMenuDto): Promise<any[]>;
    getMegaMenuCustomer(req: any): Promise<any>;
    getMegaMenuAdmin(): Promise<any[]>;
    private _getAllRange;
}
