"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaMenuController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const role_enum_1 = require("../enum/role.enum");
const mega_menu_dto_1 = require("./dto/mega-menu.dto");
const mega_menu_service_1 = require("./mega-menu.service");
const purge_enum_1 = require("../home/<USER>/purge.enum");
const event_emitter_1 = require("@nestjs/event-emitter");
let MegaMenuController = class MegaMenuController {
    constructor(megaMenuService, eventEmitter) {
        this.megaMenuService = megaMenuService;
        this.eventEmitter = eventEmitter;
    }
    create(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const megaMenu = yield this.megaMenuService.create(payload);
            this.eventEmitter.emit("rebuild-home");
            return megaMenu;
        });
    }
    getAdminMegaMenu() {
        return this.megaMenuService.getMegaMenuAdmin();
    }
    getWebMegaMenu(req) {
        return this.megaMenuService.getMegaMenuCustomer(req);
    }
};
__decorate([
    (0, common_1.Post)("/admin"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [role_enum_1.Role.Admin, `realm:app-${role_enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    (0, swagger_1.ApiBody)({ type: mega_menu_dto_1.MegaMenuDto, description: "Create / Edit Mega Menu" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [mega_menu_dto_1.MegaMenuDto]),
    __metadata("design:returntype", Promise)
], MegaMenuController.prototype, "create", null);
__decorate([
    (0, common_1.Get)("/admin"),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [role_enum_1.Role.Admin, `realm:app-${role_enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MegaMenuController.prototype, "getAdminMegaMenu", null);
__decorate([
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, swagger_1.ApiHeader)({ name: "purge", enum: purge_enum_1.PurgeEnum }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MegaMenuController.prototype, "getWebMegaMenu", null);
MegaMenuController = __decorate([
    (0, swagger_1.ApiTags)("Mega Menu"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)("mega-menu"),
    __metadata("design:paramtypes", [mega_menu_service_1.MegaMenuService, event_emitter_1.EventEmitter2])
], MegaMenuController);
exports.MegaMenuController = MegaMenuController;
//# sourceMappingURL=mega-menu.controller.js.map