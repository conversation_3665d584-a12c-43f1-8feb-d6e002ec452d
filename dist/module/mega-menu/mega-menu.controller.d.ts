import { MegaMenuDto } from "./dto/mega-menu.dto";
import { MegaMenuService } from "./mega-menu.service";
import { EventEmitter2 } from "@nestjs/event-emitter";
export declare class MegaMenuController {
    private readonly megaMenuService;
    private readonly eventEmitter;
    constructor(megaMenuService: MegaMenuService, eventEmitter: EventEmitter2);
    create(payload: MegaMenuDto): Promise<any[]>;
    getAdminMegaMenu(): Promise<any[]>;
    getWebMegaMenu(req: any): Promise<any>;
}
