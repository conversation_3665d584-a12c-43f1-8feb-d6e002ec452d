"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaMenuDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const create_mega_menu_dto_1 = require("./create-mega-menu.dto");
class MegaMenuDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [create_mega_menu_dto_1.CreateMegaMenuDto], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_transformer_1.Type)(() => create_mega_menu_dto_1.CreateMegaMenuDto),
    (0, class_validator_1.ValidateNested)(),
    __metadata("design:type", Array)
], MegaMenuDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsMongoId)({ each: true }),
    __metadata("design:type", Array)
], MegaMenuDto.prototype, "delete", void 0);
exports.MegaMenuDto = MegaMenuDto;
//# sourceMappingURL=mega-menu.dto.js.map