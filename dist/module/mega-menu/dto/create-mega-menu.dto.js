"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateMegaMenuDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const mega_menu_align_enum_1 = require("../enum/mega-menu-align-enum");
const mega_menu_children_dto_1 = require("./mega-menu-children.dto");
const mega_menu_image_dto_1 = require("./mega-menu-image.dto");
const mongoose_1 = require("mongoose");
const transform_boolean_decorator_1 = require("../../../decorator/transform-boolean.decorator");
class CreateMegaMenuDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMegaMenuDto.prototype, "menu_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: mongoose_1.default.Schema.Types.ObjectId, required: false }),
    (0, class_validator_1.ValidateIf)((obj) => obj.product_group_id),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsMongoId)(),
    __metadata("design:type", String)
], CreateMegaMenuDto.prototype, "product_group_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, required: false }),
    (0, class_validator_1.ValidateIf)((obj) => obj.type === mega_menu_align_enum_1.MegaMenuType.custom),
    (0, class_validator_1.IsDefined)({ message: "Type should be custom if you want to add url_key" }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMegaMenuDto.prototype, "url_key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateMegaMenuDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Mega Menu Tyoe",
        enum: mega_menu_align_enum_1.MegaMenuType,
        required: true,
    }),
    (0, class_validator_1.IsEnum)(mega_menu_align_enum_1.MegaMenuType),
    __metadata("design:type", String)
], CreateMegaMenuDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, required: true }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMegaMenuDto.prototype, "customName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Enum Align Mega Menu",
        enum: mega_menu_align_enum_1.MegaMenuAlignEnum,
    }),
    (0, class_validator_1.IsEnum)(mega_menu_align_enum_1.MegaMenuAlignEnum),
    __metadata("design:type", String)
], CreateMegaMenuDto.prototype, "align", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsBoolean)(),
    (0, transform_boolean_decorator_1.TransformBoolean)(),
    __metadata("design:type", Boolean)
], CreateMegaMenuDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [mega_menu_image_dto_1.MegaMenuImageDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => mega_menu_image_dto_1.MegaMenuImageDto),
    (0, class_validator_1.ValidateNested)(),
    (0, class_validator_1.ArrayMaxSize)(2, { message: "Maximum number for image is 2" }),
    __metadata("design:type", Array)
], CreateMegaMenuDto.prototype, "image", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [mega_menu_children_dto_1.MegaMenuChildrenDto], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_transformer_1.Type)(() => mega_menu_children_dto_1.MegaMenuChildrenDto),
    (0, class_validator_1.ValidateNested)(),
    __metadata("design:type", Array)
], CreateMegaMenuDto.prototype, "children", void 0);
exports.CreateMegaMenuDto = CreateMegaMenuDto;
//# sourceMappingURL=create-mega-menu.dto.js.map