import { Repository } from "typeorm";
import { AnalyticsData } from "./entities/analytics-data.entity";
import { AnalyticsQueryDto } from "./dto/analytics-query.dto";
export declare class AnalyticsService {
    private readonly analyticsRepository;
    constructor(analyticsRepository: Repository<AnalyticsData>);
    findAll(queryDto: AnalyticsQueryDto): Promise<{
        data: AnalyticsData[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNextPage: boolean;
            hasPrevPage: boolean;
        };
    }>;
    findById(id: number): Promise<AnalyticsData>;
    getStatistics(): Promise<{
        totalRecords: number;
        categoryStats: any[];
        statusStats: any[];
    }>;
    executeCustomQuery(query: string, parameters?: any[]): Promise<{
        success: boolean;
        data: any;
        rowCount: any;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: any;
        rowCount?: undefined;
    }>;
}
