"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const analytics_data_entity_1 = require("./entities/analytics-data.entity");
let AnalyticsService = class AnalyticsService {
    constructor(analyticsRepository) {
        this.analyticsRepository = analyticsRepository;
    }
    findAll(queryDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { category, status, startDate, endDate, page = 1, limit = 10 } = queryDto;
            const queryBuilder = this.analyticsRepository.createQueryBuilder("analytics");
            if (category) {
                queryBuilder.andWhere("analytics.category = :category", { category });
            }
            if (status) {
                queryBuilder.andWhere("analytics.status = :status", { status });
            }
            if (startDate) {
                queryBuilder.andWhere("analytics.created_date >= :startDate", { startDate });
            }
            if (endDate) {
                queryBuilder.andWhere("analytics.created_date <= :endDate", { endDate });
            }
            const skip = (page - 1) * limit;
            queryBuilder.skip(skip).take(limit);
            queryBuilder.orderBy("analytics.created_date", "DESC");
            const [data, total] = yield queryBuilder.getManyAndCount();
            return {
                data,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit),
                    hasNextPage: page < Math.ceil(total / limit),
                    hasPrevPage: page > 1,
                },
            };
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.analyticsRepository.findOne({ where: { id } });
        });
    }
    getStatistics() {
        return __awaiter(this, void 0, void 0, function* () {
            const totalRecords = yield this.analyticsRepository.count();
            const categoryStats = yield this.analyticsRepository
                .createQueryBuilder("analytics")
                .select("analytics.category", "category")
                .addSelect("COUNT(*)", "count")
                .groupBy("analytics.category")
                .getRawMany();
            const statusStats = yield this.analyticsRepository
                .createQueryBuilder("analytics")
                .select("analytics.status", "status")
                .addSelect("COUNT(*)", "count")
                .groupBy("analytics.status")
                .getRawMany();
            return {
                totalRecords,
                categoryStats,
                statusStats,
            };
        });
    }
    executeCustomQuery(query, parameters = []) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield this.analyticsRepository.query(query, parameters);
                return {
                    success: true,
                    data: result,
                    rowCount: result.length,
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    data: null,
                };
            }
        });
    }
};
AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(analytics_data_entity_1.AnalyticsData)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AnalyticsService);
exports.AnalyticsService = AnalyticsService;
//# sourceMappingURL=analytics.service.js.map