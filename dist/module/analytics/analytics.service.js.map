{"version": 3, "file": "analytics.service.js", "sourceRoot": "", "sources": ["../../../src/module/analytics/analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAyD;AACzD,4EAAiE;AAI1D,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YAEmB,mBAA8C;QAA9C,wBAAmB,GAAnB,mBAAmB,CAA2B;IAC9D,CAAC;IAEE,OAAO,CAAC,QAA2B;;YACvC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,QAAQ,CAAC;YAEhF,MAAM,YAAY,GAAsC,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAGjH,IAAI,QAAQ,EAAE;gBACZ,YAAY,CAAC,QAAQ,CAAC,gCAAgC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;aACvE;YAED,IAAI,MAAM,EAAE;gBACV,YAAY,CAAC,QAAQ,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;aACjE;YAED,IAAI,SAAS,EAAE;gBACb,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;aAC9E;YAED,IAAI,OAAO,EAAE;gBACX,YAAY,CAAC,QAAQ,CAAC,oCAAoC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;aAC1E;YAGD,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAChC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAGpC,YAAY,CAAC,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;YAEvD,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAE3D,OAAO;gBACL,IAAI;gBACJ,UAAU,EAAE;oBACV,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACpC,WAAW,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBAC5C,WAAW,EAAE,IAAI,GAAG,CAAC;iBACtB;aACF,CAAC;QACJ,CAAC;KAAA;IAEK,QAAQ,CAAC,EAAU;;YACvB,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;KAAA;IAEK,aAAa;;YACjB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;YAE5D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB;iBACjD,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,MAAM,CAAC,oBAAoB,EAAE,UAAU,CAAC;iBACxC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;iBAC9B,OAAO,CAAC,oBAAoB,CAAC;iBAC7B,UAAU,EAAE,CAAC;YAEhB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB;iBAC/C,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,MAAM,CAAC,kBAAkB,EAAE,QAAQ,CAAC;iBACpC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;iBAC9B,OAAO,CAAC,kBAAkB,CAAC;iBAC3B,UAAU,EAAE,CAAC;YAEhB,OAAO;gBACL,YAAY;gBACZ,aAAa;gBACb,WAAW;aACZ,CAAC;QACJ,CAAC;KAAA;IAEK,kBAAkB,CAAC,KAAa,EAAE,aAAoB,EAAE;;YAO5D,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACvE,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,MAAM,CAAC,MAAM;iBACxB,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,IAAI,EAAE,IAAI;iBACX,CAAC;aACH;QACH,CAAC;KAAA;CACF,CAAA;AApGY,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,qCAAa,CAAC,CAAA;qCACM,oBAAU;GAHvC,gBAAgB,CAoG5B;AApGY,4CAAgB"}