"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const analytics_service_1 = require("./analytics.service");
const analytics_query_dto_1 = require("./dto/analytics-query.dto");
let AnalyticsController = class AnalyticsController {
    constructor(analyticsService) {
        this.analyticsService = analyticsService;
    }
    findAll(queryDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.analyticsService.findAll(queryDto);
        });
    }
    getStatistics() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.analyticsService.getStatistics();
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.analyticsService.findById(id);
        });
    }
    executeCustomQuery(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const { query, parameters = [] } = body;
            return this.analyticsService.executeCustomQuery(query, parameters);
        });
    }
};
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get analytics data with filtering and pagination" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Analytics data retrieved successfully" }),
    (0, swagger_1.ApiQuery)({ name: "category", required: false, description: "Filter by category" }),
    (0, swagger_1.ApiQuery)({ name: "status", required: false, description: "Filter by status" }),
    (0, swagger_1.ApiQuery)({ name: "startDate", required: false, description: "Start date (YYYY-MM-DD)" }),
    (0, swagger_1.ApiQuery)({ name: "endDate", required: false, description: "End date (YYYY-MM-DD)" }),
    (0, swagger_1.ApiQuery)({ name: "page", required: false, description: "Page number", example: 1 }),
    (0, swagger_1.ApiQuery)({ name: "limit", required: false, description: "Items per page", example: 10 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.AnalyticsQueryDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)("statistics"),
    (0, swagger_1.ApiOperation)({ summary: "Get analytics statistics and summary" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Statistics retrieved successfully" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Get analytics data by ID" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Analytics data found" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "Analytics data not found" }),
    __param(0, (0, common_1.Param)("id", common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "findById", null);
__decorate([
    (0, common_1.Post)("custom-query"),
    (0, swagger_1.ApiOperation)({
        summary: "Execute custom SQL query",
        description: "WARNING: This endpoint is for demonstration only. In production, implement proper validation and security measures.",
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Query executed successfully" }),
    (0, swagger_1.ApiResponse)({ status: 400, description: "Query execution failed" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "executeCustomQuery", null);
AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)("Analytics"),
    (0, common_1.Controller)("analytics"),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService])
], AnalyticsController);
exports.AnalyticsController = AnalyticsController;
//# sourceMappingURL=analytics.controller.js.map