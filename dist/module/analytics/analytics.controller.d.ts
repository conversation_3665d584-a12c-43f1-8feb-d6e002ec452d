import { AnalyticsService } from "./analytics.service";
import { AnalyticsQueryDto } from "./dto/analytics-query.dto";
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    findAll(queryDto: AnalyticsQueryDto): Promise<{
        data: import("./entities/analytics-data.entity").AnalyticsData[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNextPage: boolean;
            hasPrevPage: boolean;
        };
    }>;
    getStatistics(): Promise<{
        totalRecords: number;
        categoryStats: any[];
        statusStats: any[];
    }>;
    findById(id: number): Promise<import("./entities/analytics-data.entity").AnalyticsData>;
    executeCustomQuery(body: {
        query: string;
        parameters?: any[];
    }): Promise<{
        success: boolean;
        data: any;
        rowCount: any;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: any;
        rowCount?: undefined;
    }>;
}
