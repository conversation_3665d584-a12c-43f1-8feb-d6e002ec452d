"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreGroupService = void 0;
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const ioredis_1 = require("ioredis");
const mongoose_2 = require("mongoose");
const slugify_1 = require("slugify");
const store_service_1 = require("../store/store.service");
let StoreGroupService = class StoreGroupService {
    constructor(storeGroupModel, storeService, redisService) {
        this.storeGroupModel = storeGroupModel;
        this.storeService = storeService;
        this.redisService = redisService;
    }
    create(params) {
        return __awaiter(this, void 0, void 0, function* () {
            params["stores"] = yield this.getStoreCodes(params.inclStore, params.exclStore);
            params["storeGroupCode"] = yield this._generateStoreGroupCode(params["name"]);
            const storeGroup = new this.storeGroupModel(params);
            yield storeGroup.save();
            yield this.flushAllCache();
            return storeGroup;
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const storeGroup = yield this.storeGroupModel.findById(id);
                if (!storeGroup) {
                    throw new common_1.HttpException("store group is not found", common_1.HttpStatus.BAD_REQUEST);
                }
                return storeGroup;
            }
            catch (e) {
                throw new common_1.HttpException("store group is not found", common_1.HttpStatus.BAD_REQUEST);
            }
        });
    }
    findAll(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            if (params.name) {
                filter.name = { $regex: new RegExp(params.name), $options: "i" };
            }
            if (params.store) {
                filter.stores = { $regex: new RegExp(params.store), $options: "i" };
            }
            if (params.keyword) {
                filter.$or = [
                    { name: { $regex: new RegExp(params.keyword), $options: "i" } },
                    { stores: { $regex: new RegExp(params.keyword), $options: "i" } },
                ];
            }
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            const result = yield this.storeGroupModel.paginate(filter, options);
            return result;
        });
    }
    update(id, params) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            params["stores"] = yield this.getStoreCodes(params.inclStore, params.exclStore);
            const extStoreGroup = yield this.storeGroupModel.findById({ _id: id });
            if (!extStoreGroup.storeGroupCode) {
                params["storeGroupCode"] = yield this._generateStoreGroupCode(params["name"]);
            }
            const storeGroup = yield this.storeGroupModel.findOneAndUpdate({ _id: id }, params, {
                new: true,
            });
            if (!storeGroup) {
                throw "Update data failed.";
            }
            yield this.flushAllCache();
            return storeGroup;
        });
    }
    remove(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const deleting = yield this.storeGroupModel.deleteOne({ _id: id }).exec();
            yield this.flushAllCache();
            return deleting;
        });
    }
    refreshAllStore() {
        return __awaiter(this, void 0, void 0, function* () {
            const storeGroups = yield this.storeGroupModel.find({});
            storeGroups.map((s) => __awaiter(this, void 0, void 0, function* () {
                yield this.update(s._id, {
                    name: s.name,
                    description: s.description,
                    inclStore: s.inclStore,
                    exclStore: s.exclStore,
                });
            }));
            yield this.flushAllCache();
        });
    }
    flushAllCache() {
        return __awaiter(this, void 0, void 0, function* () {
            const keys = yield this.redisService.keys("store-group-*");
            if (keys.length > 0) {
                yield this.redisService.del(keys);
            }
        });
    }
    getStoreGroups(storeCode) {
        return __awaiter(this, void 0, void 0, function* () {
            const cacheKey = !storeCode ? "store-group-all" : `store-group-${storeCode}`;
            const cachedStores = yield this.redisService.get(cacheKey);
            if (cachedStores) {
                return JSON.parse(cachedStores);
            }
            const filter = !storeCode ? { stores: { $not: { $size: 0 } } } : { stores: storeCode };
            const storeGroups = yield this.storeGroupModel.find(filter);
            if (storeGroups.length > 0) {
                yield this.redisService.set(cacheKey, JSON.stringify(storeGroups), "EX", process.env.REDIS_STORE_GROUP_CACHED_LIFETIME || 3600);
            }
            else {
                yield this.redisService.del(cacheKey);
            }
            return storeGroups;
        });
    }
    getStoreGroupByIds(param) {
        return __awaiter(this, void 0, void 0, function* () {
            const storeGroups = yield this.storeGroupModel.find({ _id: { $in: param.store_group } });
            return storeGroups;
        });
    }
    getStoreCodes(incl, excl) {
        return __awaiter(this, void 0, void 0, function* () {
            let stores = [];
            let _tmpStores = [];
            const allStores = yield this.storeService.getAllStoreCodes();
            if (incl == "*") {
                _tmpStores = allStores;
            }
            else {
                const _incl = yield this._splitStore(incl);
                allStores.map((s) => {
                    const _prefix2 = s.substring(0, 2);
                    const _prefix3 = s.substring(0, 3);
                    if (_incl.exact.includes(s) || _incl.prefix.includes(_prefix2) || _incl.prefix.includes(_prefix3)) {
                        _tmpStores.push(s);
                    }
                });
            }
            if (!excl) {
                stores = _tmpStores;
            }
            else if (excl == "*") {
                stores = [];
            }
            else {
                const _excl = yield this._splitStore(excl);
                _tmpStores.map((s) => {
                    const _prefix2 = s.substring(0, 2);
                    const _prefix3 = s.substring(0, 3);
                    if (!_excl.exact.includes(s) && !_excl.prefix.includes(_prefix2) && !_excl.prefix.includes(_prefix3)) {
                        stores.push(s);
                    }
                });
            }
            return stores;
        });
    }
    _splitStore(stores) {
        return __awaiter(this, void 0, void 0, function* () {
            const exact = [];
            const prefix = [];
            const codes = stores.split(",");
            codes.map((c) => {
                if (!c.match(/\*/g)) {
                    exact.push(c);
                }
                else {
                    prefix.push(c.replace("*", ""));
                }
            });
            return { exact: exact, prefix: prefix };
        });
    }
    _generateStoreGroupCode(storeGroupName) {
        return __awaiter(this, void 0, void 0, function* () {
            let slug = (0, slugify_1.default)(storeGroupName, "_");
            const storeGroups = yield this.storeGroupModel.find({ storeGroupCode: { $regex: `^${slug}` } });
            if (!storeGroups.length) {
                return slug;
            }
            let _lastSlug = "";
            storeGroups.map((s) => {
                if (_lastSlug.localeCompare(s.storeGroupCode) == -1) {
                    const restPart = s.storeGroupCode.replace(slug, "");
                    if (s.storeGroupCode !== slug && !restPart.match(/[a-z]/)) {
                        _lastSlug = s.storeGroupCode;
                    }
                }
            });
            const _lastNumber = _lastSlug.split("_").slice(-1);
            if (!_lastNumber[0].match(/[a-z]/)) {
                slug += "_" + (Number(_lastNumber[0]) + 1);
            }
            return slug;
        });
    }
};
StoreGroupService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("StoreGroup")),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => store_service_1.StoreService))),
    __param(2, (0, nestjs_redis_1.InjectRedis)()),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, store_service_1.StoreService, typeof (_b = typeof ioredis_1.default !== "undefined" && ioredis_1.default) === "function" ? _b : Object])
], StoreGroupService);
exports.StoreGroupService = StoreGroupService;
//# sourceMappingURL=store-group.service.js.map