import Redis from "ioredis";
import { PaginateModel } from "mongoose";
import { StoreService } from "../store/store.service";
import { CreateStoreGroupDto } from "./dto/create-store-group.dto";
import { GetSelectedStoreGroupDto, GetStoreGroupDto } from "./dto/get-store-group.dto";
import { StoreGroupDocument } from "./schema/store-group.schema";
export declare class StoreGroupService {
    private storeGroupModel;
    private readonly storeService;
    private readonly redisService;
    constructor(storeGroupModel: PaginateModel<StoreGroupDocument>, storeService: StoreService, redisService: Redis);
    create(params: CreateStoreGroupDto): Promise<any>;
    findById(id: string): Promise<any>;
    findAll(params: GetStoreGroupDto): Promise<any>;
    update(id: string, params: CreateStoreGroupDto): Promise<any>;
    remove(id: string): Promise<any>;
    refreshAllStore(): Promise<void>;
    flushAllCache(): Promise<void>;
    getStoreGroups(storeCode?: string): Promise<any>;
    getStoreGroupByIds(param: GetSelectedStoreGroupDto): Promise<any>;
    getStoreCodes(incl: string, excl?: string): Promise<any[]>;
    _splitStore(stores: string): Promise<{
        exact: any[];
        prefix: any[];
    }>;
    _generateStoreGroupCode(storeGroupName: string): Promise<any>;
}
