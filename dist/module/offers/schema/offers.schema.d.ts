/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { Document } from "mongoose";
export declare class Offers extends Document {
    id: number;
    created_at: Date;
    image: string;
    position: number;
    response_id: string;
    response_type: string;
    start_date: Date;
    status: string;
    title: string;
    updated_at: Date;
    link: string;
    id_promo_banner_category: number;
}
export type OffersDocument = Offers & Document;
export declare const OffersSchema: import("mongoose").Schema<Offers, import("mongoose").Model<Offers, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Offers>;
