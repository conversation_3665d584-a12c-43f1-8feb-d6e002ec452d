"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarbonResultService = void 0;
const class_validator_1 = require("class-validator");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let CarbonResultService = class CarbonResultService {
    constructor(resultModel, answerModel) {
        this.resultModel = resultModel;
        this.answerModel = answerModel;
    }
    get(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!(0, class_validator_1.isMongoId)(userId)) {
                throw new common_1.HttpException("Invalid user id", common_1.HttpStatus.BAD_REQUEST);
            }
            return yield this.resultModel.findOne({ user_id: userId }).sort({ createdAt: -1 }).lean().exec();
        });
    }
    create(dto) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!dto.answers || dto.answers.length === 0) {
                throw new common_1.HttpException("Invalid answers", common_1.HttpStatus.BAD_REQUEST);
            }
            let total = 0;
            const taskQueue = [];
            dto.answers.forEach((item) => __awaiter(this, void 0, void 0, function* () {
                taskQueue.push(this.answerModel
                    .findById(item)
                    .exec()
                    .then((result) => {
                    total += result.score;
                }));
            }));
            yield Promise.all(taskQueue);
            const newResult = new this.resultModel({
                score: total,
                user_id: dto.userId,
                answers: dto.answers,
            });
            return newResult.save();
        });
    }
};
CarbonResultService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("CarbonResult")),
    __param(1, (0, mongoose_1.InjectModel)("CarbonAnswer")),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], CarbonResultService);
exports.CarbonResultService = CarbonResultService;
//# sourceMappingURL=carbon-result.service.js.map