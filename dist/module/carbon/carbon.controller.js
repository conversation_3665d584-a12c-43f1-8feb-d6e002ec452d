"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarbonController = void 0;
const carbon_result_service_1 = require("./carbon-result.service");
const create_result_dto_1 = require("./dto/create-result.dto");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const carbon_answer_service_1 = require("./carbon-answer.service");
const carbon_quiz_service_1 = require("./carbon-quiz.service");
const create_answer_dto_1 = require("./dto/create-answer.dto");
const create_quiz_dto_1 = require("./dto/create-quiz.dto");
const update_quiz_dto_1 = require("./dto/update-quiz.dto");
let CarbonController = class CarbonController {
    constructor(answerService, quizService, resultService) {
        this.answerService = answerService;
        this.quizService = quizService;
        this.resultService = resultService;
    }
    getQuestions() {
        return this.quizService.findAll();
    }
    createResult(payload) {
        return this.resultService.create(payload);
    }
    getMyScore(userId) {
        return this.resultService.get(userId);
    }
    addQuestions(payload) {
        return this.answerService.create(payload);
    }
    addQuiz(payload) {
        return this.quizService.create(payload);
    }
    updateQuiz(payload) {
        return this.quizService.update(payload);
    }
};
__decorate([
    (0, common_1.Get)("questions"),
    (0, keycloak_connect_tbs_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CarbonController.prototype, "getQuestions", null);
__decorate([
    (0, common_1.Post)("result"),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_result_dto_1.CreateResultDto]),
    __metadata("design:returntype", void 0)
], CarbonController.prototype, "createResult", null);
__decorate([
    (0, common_1.Get)("score/:id"),
    (0, keycloak_connect_tbs_1.Public)(),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CarbonController.prototype, "getMyScore", null);
__decorate([
    (0, common_1.Post)("admin/answer"),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, swagger_1.ApiBody)({ type: create_answer_dto_1.CreateAnswerDto, description: "Create answers" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_answer_dto_1.CreateAnswerDto]),
    __metadata("design:returntype", void 0)
], CarbonController.prototype, "addQuestions", null);
__decorate([
    (0, common_1.Post)("admin/quiz"),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, swagger_1.ApiBody)({ type: create_quiz_dto_1.CreateQuizDto, description: "Create questions" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_quiz_dto_1.CreateQuizDto]),
    __metadata("design:returntype", void 0)
], CarbonController.prototype, "addQuiz", null);
__decorate([
    (0, common_1.Patch)("admin/quiz"),
    (0, keycloak_connect_tbs_1.Public)(),
    (0, swagger_1.ApiBody)({ type: update_quiz_dto_1.UpdateQuizDto, description: "Update questions" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_quiz_dto_1.UpdateQuizDto]),
    __metadata("design:returntype", void 0)
], CarbonController.prototype, "updateQuiz", null);
CarbonController = __decorate([
    (0, swagger_1.ApiTags)("Carbon Footprint"),
    (0, common_1.Controller)("carbon"),
    __metadata("design:paramtypes", [carbon_answer_service_1.CarbonAnswerService,
        carbon_quiz_service_1.CarbonQuizService,
        carbon_result_service_1.CarbonResultService])
], CarbonController);
exports.CarbonController = CarbonController;
//# sourceMappingURL=carbon.controller.js.map