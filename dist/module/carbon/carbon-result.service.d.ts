import { Model } from "mongoose";
import { CarbonResultDocument } from "./schema/carbon-result.schema";
import { CreateResultDto } from "./dto/create-result.dto";
import { CarbonAnswerDocument } from "./schema/carbon-answer.schema";
export declare class CarbonResultService {
    private resultModel;
    private answerModel;
    constructor(resultModel: Model<CarbonResultDocument>, answerModel: Model<CarbonAnswerDocument>);
    get(userId: string): Promise<any>;
    create(dto: CreateResultDto): Promise<any>;
}
