/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { CarbonResultService } from "./carbon-result.service";
import { CreateResultDto } from "./dto/create-result.dto";
import { CarbonAnswerService } from "./carbon-answer.service";
import { CarbonQuizService } from "./carbon-quiz.service";
import { CreateAnswerDto } from "./dto/create-answer.dto";
import { CreateQuizDto } from "./dto/create-quiz.dto";
import { UpdateQuizDto } from "./dto/update-quiz.dto";
export declare class CarbonController {
    private readonly answerService;
    private readonly quizService;
    private readonly resultService;
    constructor(answerService: CarbonAnswerService, quizService: CarbonQuizService, resultService: CarbonResultService);
    getQuestions(): Promise<import("mongoose").LeanDocument<import("./schema/carbon-quiz.schema").CarbonQuiz & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    createResult(payload: CreateResultDto): Promise<any>;
    getMyScore(userId: string): Promise<any>;
    addQuestions(payload: CreateAnswerDto): Promise<any>;
    addQuiz(payload: CreateQuizDto): Promise<any>;
    updateQuiz(payload: UpdateQuizDto): Promise<import("./schema/carbon-quiz.schema").CarbonQuiz>;
}
