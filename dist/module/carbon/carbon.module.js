"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarbonModule = void 0;
const carbon_result_service_1 = require("./carbon-result.service");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const carbon_answer_service_1 = require("./carbon-answer.service");
const carbon_quiz_service_1 = require("./carbon-quiz.service");
const carbon_controller_1 = require("./carbon.controller");
const carbon_answer_schema_1 = require("./schema/carbon-answer.schema");
const carbon_quiz_schema_1 = require("./schema/carbon-quiz.schema");
const carbon_result_schema_1 = require("./schema/carbon-result.schema");
const nestjs_elastic_apm_1 = require("../../../modules/nestjs-elastic-apm");
let CarbonModule = class CarbonModule {
};
CarbonModule = __decorate([
    (0, common_1.Module)({
        controllers: [carbon_controller_1.CarbonController],
        providers: [carbon_quiz_service_1.CarbonQuizService, carbon_answer_service_1.CarbonAnswerService, carbon_result_service_1.CarbonResultService],
        imports: [
            mongoose_1.MongooseModule.forFeature([
                {
                    name: "CarbonAnswer",
                    schema: carbon_answer_schema_1.CarbonAnswerSchema,
                },
                {
                    name: "CarbonQuiz",
                    schema: carbon_quiz_schema_1.CarbonQuizSchema,
                },
                {
                    name: "CarbonResult",
                    schema: carbon_result_schema_1.CarbonResultSchema,
                },
            ]),
            nestjs_elastic_apm_1.ApmModule.register(),
        ],
    })
], CarbonModule);
exports.CarbonModule = CarbonModule;
//# sourceMappingURL=carbon.module.js.map