import mongoose, { Model } from "mongoose";
import { CarbonAnswer, CarbonAnswerDocument } from "./schema/carbon-answer.schema";
import { UpdateAnswerDto } from "./dto/update-answer.dto";
import { CreateAnswerDto } from "./dto/create-answer.dto";
export declare class CarbonAnswerService {
    private answerModel;
    constructor(answerModel: Model<CarbonAnswerDocument>);
    create(dto: CreateAnswerDto): Promise<any>;
    findById(id: string): Promise<any>;
    findAll(): Promise<(mongoose.Document<unknown, any, CarbonAnswerDocument> & Omit<CarbonAnswer & Document & {
        _id: mongoose.Types.ObjectId;
    }, never>)[]>;
    update(id: string, payload: UpdateAnswerDto): Promise<CarbonAnswer | null>;
    remove(id: string): Promise<any>;
}
