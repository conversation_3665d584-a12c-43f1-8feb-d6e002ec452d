import { UpdateQuizDto } from "./dto/update-quiz.dto";
import { CreateQuizDto } from "./dto/create-quiz.dto";
import mongoose, { Model } from "mongoose";
import { CarbonQuiz, CarbonQuizDocument } from "./schema/carbon-quiz.schema";
export declare class CarbonQuizService {
    private quizModel;
    constructor(quizModel: Model<CarbonQuizDocument>);
    create(dto: CreateQuizDto): Promise<any>;
    findAll(): Promise<mongoose.LeanDocument<CarbonQuiz & Document & {
        _id: mongoose.Types.ObjectId;
    }>[]>;
    findById(id: string): Promise<any>;
    update(payload: UpdateQuizDto): Promise<CarbonQuiz | null>;
    remove(id: string): Promise<any>;
}
