{"version": 3, "file": "carbon-answer.service.js", "sourceRoot": "", "sources": ["../../../src/module/carbon/carbon-answer.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,+CAA+C;AAC/C,uCAA2C;AAMpC,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEuC,WAAwC;QAAxC,gBAAW,GAAX,WAAW,CAA6B;IAC5E,CAAC;IAEE,MAAM,CAAC,GAAoB;;YAC/B,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAE1C,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;QACxB,CAAC;KAAA;IAEK,QAAQ,CAAC,EAAU;;YACvB,IAAI,CAAC,kBAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACxC,MAAM,IAAI,sBAAa,CAAC,2BAA2B,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aAC9E;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAEnD,OAAO,KAAK,CAAC;QACf,CAAC;KAAA;IAEK,OAAO;;YACX,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QACxD,CAAC;KAAA;IAEK,MAAM,CAAC,EAAU,EAAE,OAAwB;;YAC/C,IAAI,CAAC,kBAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACxC,MAAM,IAAI,sBAAa,CAAC,2BAA2B,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aAC9E;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;YAE7E,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,qBAAqB,CAAC;aAC7B;YAED,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC;KAAA;IAEK,MAAM,CAAC,EAAU;;YACrB,IAAI,CAAC,kBAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACxC,MAAM,IAAI,sBAAa,CAAC,2BAA2B,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aAC9E;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAEtE,OAAO,QAAQ,CAAC;QAClB,CAAC;KAAA;CACF,CAAA;AAhDY,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,sBAAW,EAAC,cAAc,CAAC,CAAA;qCAAsB,gBAAK;GAH9C,mBAAmB,CAgD/B;AAhDY,kDAAmB"}