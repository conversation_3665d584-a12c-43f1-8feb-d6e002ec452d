"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarbonAnswerService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let CarbonAnswerService = class CarbonAnswerService {
    constructor(answerModel) {
        this.answerModel = answerModel;
    }
    create(dto) {
        return __awaiter(this, void 0, void 0, function* () {
            const newData = new this.answerModel(dto);
            return newData.save();
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const Store = this.answerModel.findById(id).exec();
            return Store;
        });
    }
    findAll() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.answerModel.find({ is_active: 1 }).exec();
        });
    }
    update(id, payload) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const update = yield this.answerModel.updateOne({ _id: id }, payload).exec();
            if (!update) {
                throw "Update data failed.";
            }
            return this.answerModel.findById(id);
        });
    }
    remove(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const deleting = yield this.answerModel.deleteOne({ _id: id }).exec();
            return deleting;
        });
    }
};
CarbonAnswerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("CarbonAnswer")),
    __metadata("design:paramtypes", [mongoose_2.Model])
], CarbonAnswerService);
exports.CarbonAnswerService = CarbonAnswerService;
//# sourceMappingURL=carbon-answer.service.js.map