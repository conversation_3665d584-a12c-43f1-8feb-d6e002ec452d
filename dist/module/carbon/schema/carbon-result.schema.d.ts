import mongoose from "mongoose";
export declare class CarbonResult {
    score: number;
    user_id: mongoose.Schema.Types.ObjectId;
    answers: mongoose.Schema.Types.ObjectId[];
}
export type CarbonResultDocument = CarbonResult & Document;
export declare const CarbonResultSchema: mongoose.Schema<CarbonResult, mongoose.Model<CarbonResult, any, any, any, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, CarbonResult>;
