"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarbonQuizSchema = exports.CarbonQuiz = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const common_1 = require("@nestjs/common");
const mongoose_2 = require("mongoose");
let CarbonQuiz = class CarbonQuiz {
};
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], CarbonQuiz.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], CarbonQuiz.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [{ type: mongoose_2.default.Schema.Types.ObjectId, ref: "CarbonAnswer" }] }),
    __metadata("design:type", Array)
], CarbonQuiz.prototype, "answers", void 0);
CarbonQuiz = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
    }),
    (0, common_1.Injectable)()
], CarbonQuiz);
exports.CarbonQuiz = CarbonQuiz;
exports.CarbonQuizSchema = mongoose_1.SchemaFactory.createForClass(CarbonQuiz);
//# sourceMappingURL=carbon-quiz.schema.js.map