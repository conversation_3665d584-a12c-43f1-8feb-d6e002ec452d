"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarbonAnswerSchema = exports.CarbonAnswer = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
let CarbonAnswer = class CarbonAnswer {
};
__decorate([
    (0, mongoose_1.Prop)({ required: true, default: 0 }),
    __metadata("design:type", Number)
], CarbonAnswer.prototype, "score", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, default: "" }),
    __metadata("design:type", String)
], CarbonAnswer.prototype, "text", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], CarbonAnswer.prototype, "is_active", void 0);
CarbonAnswer = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
    }),
    (0, common_1.Injectable)()
], CarbonAnswer);
exports.CarbonAnswer = CarbonAnswer;
exports.CarbonAnswerSchema = mongoose_1.SchemaFactory.createForClass(CarbonAnswer);
//# sourceMappingURL=carbon-answer.schema.js.map