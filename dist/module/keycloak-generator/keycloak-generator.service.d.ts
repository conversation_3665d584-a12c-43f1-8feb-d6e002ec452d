/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
import { Keycloak, KeycloakDocument } from "./schema/keycloak-generator.schema";
import { Model } from "mongoose";
import { KeycloakGeneratorFindAllDto } from "./dto/keycloak-generator-find-all.dto";
import { KeycloakGeneratorCreateDto } from "./dto/keycloak-generator-create.dto";
import { KeycloakMSService } from "../microservices/keycloak/keycloak.service";
export declare class KeycloakGeneratorService {
    private readonly keycloak;
    private readonly keycloakMs;
    constructor(keycloak: Model<KeycloakDocument>, keycloakMs: KeycloakMSService);
    private readonly logger;
    generate(paths: Array<Record<string, any>>): Promise<void>;
    findAll(params: KeycloakGeneratorFindAllDto): Promise<import("mongoose").LeanDocument<Keycloak & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    create(data: KeycloakGeneratorCreateDto): Promise<import("mongoose").Document<unknown, any, KeycloakDocument> & Omit<Keycloak & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
}
