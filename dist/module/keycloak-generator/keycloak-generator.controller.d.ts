/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
import { KeycloakGeneratorService } from "./keycloak-generator.service";
import { KeycloakGeneratorFindAllDto } from "./dto/keycloak-generator-find-all.dto";
import { KeycloakGeneratorCreateDto } from "./dto/keycloak-generator-create.dto";
export declare class KeycloakGeneratorController {
    private readonly keycloakService;
    constructor(keycloakService: KeycloakGeneratorService);
    generate(paths: Array<Record<string, any>>): Promise<void>;
    findAll(data: KeycloakGeneratorFindAllDto): Promise<import("mongoose").LeanDocument<import("./schema/keycloak-generator.schema").Keycloak & Document & {
        _id: import("mongoose").Types.ObjectId;
    }>[]>;
    create(data: KeycloakGeneratorCreateDto): Promise<import("mongoose").Document<unknown, any, import("./schema/keycloak-generator.schema").KeycloakDocument> & Omit<import("./schema/keycloak-generator.schema").Keycloak & Document & {
        _id: import("mongoose").Types.ObjectId;
    }, never>>;
}
