"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeycloakGeneratorService = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const keycloak_generator_schema_1 = require("./schema/keycloak-generator.schema");
const mongoose_2 = require("mongoose");
const keycloak_service_1 = require("../microservices/keycloak/keycloak.service");
const _ = require("lodash");
const common_1 = require("@nestjs/common");
let KeycloakGeneratorService = class KeycloakGeneratorService {
    constructor(keycloak, keycloakMs) {
        this.keycloak = keycloak;
        this.keycloakMs = keycloakMs;
        this.logger = new common_1.Logger("Keycloak Generator");
    }
    generate(paths) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const predefinePolicy = process.env.KEYCLOAK_PREDEFINE_POLICY;
                const resources = {};
                const permissions = {};
                const hashResource = {};
                let hashPermissions = {};
                const clientDetail = yield this.keycloakMs.getDetailClient();
                if (!clientDetail) {
                    this.logger.error("Client not found");
                }
                const [dbResource, keycloakResource, dbPermission, keycloakPermission, keycloakScope, keycloakPolicy] = yield Promise.all([
                    this.findAll({ type: "resource" }),
                    this.keycloakMs.getAllResources(),
                    this.findAll({ type: "permission" }),
                    this.keycloakMs.getAllPermissions(clientDetail.id),
                    this.keycloakMs.getAllScopes(clientDetail.id),
                    this.keycloakMs.getAllPolicy(clientDetail.id),
                ]);
                yield Promise.all(keycloakPermission.map((item) => (hashPermissions[item.id] = item)));
                const resourceDiff = _.difference(keycloakResource, dbResource.map((data) => data.keycloak_id));
                const permissionDiff = _.difference(keycloakPermission.map((data) => data.id), dbPermission.map((data) => data.keycloak_id));
                if (resourceDiff.length) {
                    yield Promise.all(resourceDiff.map((diff) => __awaiter(this, void 0, void 0, function* () {
                        const detail = yield this.keycloakMs.getDetailResources(diff);
                        const create = yield this.create({
                            keycloak_id: detail._id,
                            type: "resource",
                            name: detail.name,
                        });
                        dbResource.push(create);
                    })));
                }
                if (permissionDiff.length) {
                    yield Promise.all(permissionDiff.map((diff) => __awaiter(this, void 0, void 0, function* () {
                        const detail = hashPermissions[diff];
                        const create = yield this.create({
                            keycloak_id: detail.id,
                            type: "permission",
                            name: detail.name,
                        });
                        dbPermission.push(create);
                    })));
                }
                hashPermissions = {};
                dbResource.map((data) => (hashResource[data.name] = data));
                dbPermission.map((data) => (hashPermissions[data.name] = data));
                const scope = keycloakScope.map((item) => item.name);
                const adminPolicy = [];
                keycloakPolicy.map((data) => {
                    if (predefinePolicy.split(",").includes(data.name)) {
                        adminPolicy.push(data.id);
                    }
                });
                yield Promise.all(paths.map((path) => {
                    Object.keys(path).map((key) => {
                        var _a, _b, _c;
                        let controller = path[key].operationId.split("_")[0];
                        controller = controller.replace("Controller", "");
                        controller = controller.replace(/\.?([A-Z]+)/g, (x, y) => "-" + y.toLowerCase()).replace(/^-/, "");
                        controller = this.keycloakMs.prefixRes + controller;
                        resources[controller] = { name: controller, scope };
                        for (let i = 0; i < keycloakScope.length; i++) {
                            let name = controller.replace(this.keycloakMs.prefixRes, "");
                            name = this.keycloakMs.prefixPerm + name + "-" + ((_a = keycloakScope[i]) === null || _a === void 0 ? void 0 : _a.name);
                            permissions[name] = {
                                name: name,
                                description: "Permission for " + controller + " with scope " + ((_b = keycloakScope[i]) === null || _b === void 0 ? void 0 : _b.name),
                                type: "scope",
                                decision_strategy: 0,
                                logic: 0,
                                client_id: clientDetail.id,
                                policies: adminPolicy,
                                resources: controller,
                                scopes: [(_c = keycloakScope[i]) === null || _c === void 0 ? void 0 : _c.id],
                            };
                        }
                    });
                }));
                const diffResKeycloak = _.difference(Object.keys(resources), Object.keys(hashResource));
                const diffPermKeycloak = _.difference(Object.keys(permissions), Object.keys(hashPermissions));
                const limit = 15;
                const batchRes = Math.ceil(diffResKeycloak.length / limit);
                for (let i = 0; i < batchRes; i++) {
                    const res = diffResKeycloak.splice(0, limit);
                    yield Promise.all(res.map((key) => __awaiter(this, void 0, void 0, function* () {
                        const data = resources[key];
                        const res = yield this.keycloakMs.createResources(data.name, data.scope);
                        hashResource[data.name] = yield this.create({
                            keycloak_id: res._id,
                            type: "resource",
                            name: data.name,
                        });
                    })));
                }
                const batchPerm = Math.ceil(diffPermKeycloak.length / limit);
                for (let i = 0; i < batchPerm; i++) {
                    const perm = diffPermKeycloak.splice(0, limit);
                    yield Promise.all(perm.map((key) => __awaiter(this, void 0, void 0, function* () {
                        const data = permissions[key];
                        const resource = hashResource[data.resources];
                        data.resources = [resource.keycloak_id];
                        const res = yield this.keycloakMs.createPermissions(data);
                        hashResource[data.name] = yield this.create({
                            keycloak_id: res.id,
                            type: "permission",
                            name: data.name,
                        });
                    })));
                }
                return;
            }
            catch (err) {
                this.logger.error("Error with detail " + err.message);
                throw err;
            }
        });
    }
    findAll(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            if (params.type)
                filter.type = params.type;
            if (params.keycloak_id)
                filter.keycloak_id = params.keycloak_id;
            if (params.name)
                filter.name = { $regex: new RegExp(params.name, "i") };
            return this.keycloak.find(filter).lean();
        });
    }
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.keycloak.create(data);
        });
    }
};
KeycloakGeneratorService = __decorate([
    __param(0, (0, mongoose_1.InjectModel)(keycloak_generator_schema_1.Keycloak.name)),
    __metadata("design:paramtypes", [mongoose_2.Model, typeof (_a = typeof keycloak_service_1.KeycloakMSService !== "undefined" && keycloak_service_1.KeycloakMSService) === "function" ? _a : Object])
], KeycloakGeneratorService);
exports.KeycloakGeneratorService = KeycloakGeneratorService;
//# sourceMappingURL=keycloak-generator.service.js.map