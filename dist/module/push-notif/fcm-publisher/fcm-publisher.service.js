"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FcmPublisherService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const fcm_publisher_schema_1 = require("./schema/fcm-publisher.schema");
const mongoose_2 = require("mongoose");
const enum_1 = require("../../enum");
const firebase_util_1 = require("../../../utils/firebase.util");
const elastic_search_service_1 = require("../../elastic-search/elastic-search.service");
const config_1 = require("@nestjs/config");
const uuid_1 = require("uuid");
const fcm_user_mapper_schema_1 = require("../fcm-user-mapper/schema/fcm-user-mapper.schema");
const fcm_publisher_error_logs_schema_1 = require("./schema/fcm-publisher-error-logs.schema");
const event_emitter_1 = require("@nestjs/event-emitter");
const fcm_publisher_listener_1 = require("./listener/fcm-publisher.listener");
const tbs_site_config_1 = require("tbs-site-config");
const site_config_schema_1 = require("../../site-configs/schema/site-config.schema");
const fcm_publisher_queue_service_1 = require("./fcm-publisher-queue.service");
let FcmPublisherService = class FcmPublisherService {
    constructor(fcmPublisher, fcmMapper, fcmErrorLogs, configModel, elasticService, configService, eventEmitter, siteConfigService, queueService) {
        this.fcmPublisher = fcmPublisher;
        this.fcmMapper = fcmMapper;
        this.fcmErrorLogs = fcmErrorLogs;
        this.configModel = configModel;
        this.elasticService = elasticService;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.siteConfigService = siteConfigService;
        this.queueService = queueService;
        this.fcmHistoryIndex = this.configService.get("FCM_SUBSCRIPTION_HISTORY");
        this.fcmErrorToRemove = [
            "messaging/invalid-registration-token",
            "messaging/registration-token-not-registered",
        ];
        this.siteConfigKey = "util.resync-fcm-topic";
    }
    all(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, ["true"], enum_1.FcmPublisherType.ALL, false);
        });
    }
    cardNumber(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.card_number], enum_1.FcmPublisherType.CARD_NUMBER, true);
        });
    }
    memberTier(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.tier.toLowerCase()], enum_1.FcmPublisherType.MEMBER_TIER, true);
        });
    }
    gender(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.gender.toLowerCase()], enum_1.FcmPublisherType.GENDER, true);
        });
    }
    dob(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const [day, month, year] = body.dob.split("-");
            const value = [];
            value.push(day + "-" + month);
            value.push(month);
            value.push(year);
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [value], enum_1.FcmPublisherType.DOB, true);
        });
    }
    unsubscribeAll(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcm = yield this.fcmPublisher.find({ fcm_token: body.fcmToken }).lean();
            if (!(fcm === null || fcm === void 0 ? void 0 : fcm.length))
                return;
            const mockElastic = [];
            yield Promise.all(fcm.map((tops) => __awaiter(this, void 0, void 0, function* () {
                for (const key of Object.keys(tops.topics)) {
                    const topic = enum_1.FcmPublisherMongoTopicReverse[key];
                    for (const item of tops.topics[key]) {
                        yield firebase_util_1.FirebaseAdmin.messaging().unsubscribeFromTopic(body.fcmToken, enum_1.FcmPublisherTopic[topic] + "." + item);
                        mockElastic.push({
                            id: (0, uuid_1.v4)(),
                            type: "unsubscribe",
                            user_id: body.user_id,
                            fcm_token: body.fcmToken,
                            topic,
                            value: item,
                            timestamp: new Date().toISOString(),
                        });
                    }
                }
            })));
            yield this.fcmPublisher.deleteMany({ fcm_token: body.fcmToken });
            yield this.fcmMapper.updateMany({ fcm_tokens: body.fcmToken }, { $pull: { fcm_tokens: body.fcmToken } });
            yield this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: mockElastic });
        });
    }
    addCart(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.sku], enum_1.FcmPublisherType.ADD_CART, false);
        });
    }
    removeCart(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.sku], enum_1.FcmPublisherType.REMOVE_CART, false);
        });
    }
    cleanUpCart(user_id) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, "all", enum_1.FcmPublisherType.REMOVE_ALL_CART, false);
        });
    }
    addWishlist(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.sku], enum_1.FcmPublisherType.ADD_WISHLIST, false);
        });
    }
    removeWishlist(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.sku], enum_1.FcmPublisherType.REMOVE_WISHLIST, false);
        });
    }
    lastPurchaseDate(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const [year, month, day] = body.date.split("-");
            const value = [];
            value.push(month + "-" + day);
            value.push(month);
            value.push(year);
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, value, enum_1.FcmPublisherType.LAST_PURCHASE_DATE, true);
        });
    }
    lastPurchaseCity(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.city.toLowerCase()], enum_1.FcmPublisherType.LAST_PURCHASE_CITY, true);
        });
    }
    lastPurchaseRegion(body) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmMap = yield this._getFcmTokens(body.user_id);
            if (!(fcmMap === null || fcmMap === void 0 ? void 0 : fcmMap._id))
                return;
            return this._doSubscribeUnSubscribe(fcmMap, [body.region.toLowerCase()], enum_1.FcmPublisherType.LAST_PURCHASE_REGION, true);
        });
    }
    reSyncSubscription() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const status = yield this.siteConfigService.get(this.siteConfigKey);
            if (status && ((_a = JSON.parse(status)) === null || _a === void 0 ? void 0 : _a.value) === "active") {
                throw new common_1.HttpException("Process already running", common_1.HttpStatus.TOO_MANY_REQUESTS);
            }
            yield Promise.all([
                this.siteConfigService.updateValue(this.siteConfigKey, "active"),
                this.configModel.updateOne({ key: this.siteConfigKey }, { value: "active" }),
            ]);
            this.eventEmitter.emit(fcm_publisher_listener_1.FcmPublisherEvent.re_sync_fcm_topic);
            return "Re Sync All Fcm Tokens to FCM Topics in progress";
        });
    }
    _getFcmTokens(user_id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.fcmMapper.findOne({ userId: user_id }).exec();
        });
    }
    _doSubscribeUnSubscribe(fcmMap, value, type, replaceLastValue) {
        return __awaiter(this, void 0, void 0, function* () {
            const invalidFcms = [];
            yield Promise.all(fcmMap.fcm_tokens.map((fcm_token) => __awaiter(this, void 0, void 0, function* () {
                var _a;
                const exist = yield this.fcmPublisher.findOne({ fcm_token }).lean();
                const topic = enum_1.FcmPublisherTopic[type];
                const topicForMongo = enum_1.FcmPublisherMongoTopic[type];
                if (!/REMOVE/.test(type)) {
                    return yield this._subscribe({
                        exist,
                        topic,
                        fcm_token,
                        user_id: fcmMap.userId,
                        prefix: topic + ".",
                        value: value === "all" ? [value] : value,
                        replaceValue: replaceLastValue,
                        invalidFcms,
                        subscribeToFcm: enum_1.TopicSendMapping[type],
                    });
                }
                else {
                    return yield this._unsubscribe({
                        exist: exist,
                        topic: type,
                        fcm_token: fcm_token,
                        user_id: fcmMap.userId,
                        prefix: topic + ".",
                        value: value !== "all" ? value : (_a = exist === null || exist === void 0 ? void 0 : exist.topics) === null || _a === void 0 ? void 0 : _a[topicForMongo],
                        invalidFcms: invalidFcms,
                    });
                }
            })));
            if (invalidFcms.length) {
                yield this.fcmMapper.findByIdAndUpdate(fcmMap._id, { $pull: { fcm_tokens: { $in: invalidFcms } } });
            }
        });
    }
    _subscribe(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const { fcm_token, subscribeToFcm, user_id, replaceValue, value } = params;
            let { topic } = params;
            topic = topic.replace(process.env.KAFKA_TOPIC_PREFIX + "-", "");
            const topicForMongo = topic.replace(/.*\./g, "");
            const subscribes = [];
            const formattedTopic = topic.split(".");
            formattedTopic.splice(0, 1);
            yield Promise.all(value.map((item) => __awaiter(this, void 0, void 0, function* () {
                if (enum_1.TopicSendMapping[topicForMongo.toUpperCase()]) {
                    yield this.queueService.addQueue({
                        fcm_token,
                        user_id,
                        topic: topicForMongo,
                        value: item,
                        type: "subscribe",
                        subscribeToFcm,
                        replaceValue,
                    });
                }
                else {
                    const exist = yield this.fcmPublisher.findOne({ fcm_token }).lean();
                    yield this.queueService.insertToMongo("subscribe", exist, {
                        topic: topicForMongo,
                        value: item,
                        fcm_token,
                    }, replaceValue);
                }
            })));
            return subscribes;
        });
    }
    _unsubscribe(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const { invalidFcms, fcm_token, exist, user_id, prefix, value, topic, updateDb = true } = params;
            const topicForMongo = enum_1.FcmPublisherMongoTopic[topic];
            if (exist) {
                const lastValue = exist.topics[topicForMongo];
                const pullValue = [];
                if (lastValue) {
                    const topicsToUnsubscribe = [];
                    lastValue.map((item, i) => {
                        if (value.includes(item))
                            topicsToUnsubscribe.push({ data: item, index: i });
                    });
                    let errorSubscribe = [];
                    const bulkElastic = [];
                    yield Promise.all(topicsToUnsubscribe.map((item, i) => __awaiter(this, void 0, void 0, function* () {
                        var _a;
                        const unsubscribe = yield firebase_util_1.FirebaseAdmin.messaging().unsubscribeFromTopic(fcm_token, prefix + ((item === null || item === void 0 ? void 0 : item.data) || item));
                        if (unsubscribe.errors.length && this.fcmErrorToRemove.includes((_a = unsubscribe.errors[0].error) === null || _a === void 0 ? void 0 : _a.code)) {
                            invalidFcms.push(fcm_token);
                        }
                        if (unsubscribe.failureCount > 0) {
                            errorSubscribe = errorSubscribe.concat(unsubscribe.errors.map((err) => err.error.message));
                        }
                        else {
                            bulkElastic.push({
                                id: (0, uuid_1.v4)(),
                                type: "unsubscribe",
                                user_id,
                                fcm_token,
                                topic,
                                value: item,
                                timestamp: new Date().toISOString(),
                            });
                        }
                        yield firebase_util_1.FirebaseAdmin.messaging().unsubscribeFromTopic(fcm_token, prefix + item.data);
                        pullValue.push(item.data);
                        exist.topics[topicForMongo].splice(item.index, 1 - i);
                        bulkElastic.push({
                            id: (0, uuid_1.v4)(),
                            type: "unsubscribe",
                            user_id,
                            fcm_token,
                            topic,
                            value: item,
                            timestamp: new Date().toISOString(),
                        });
                    })));
                    if (pullValue.length && updateDb) {
                        yield this.fcmPublisher.updateOne({ _id: exist._id }, { $pull: { [`topics.${topicForMongo}`]: { $in: pullValue } } });
                    }
                    if (bulkElastic.length) {
                        yield this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: bulkElastic });
                    }
                }
            }
        });
    }
};
FcmPublisherService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(fcm_publisher_schema_1.FcmPublisher.name)),
    __param(1, (0, mongoose_1.InjectModel)(fcm_user_mapper_schema_1.FcmUserMapper.name)),
    __param(2, (0, mongoose_1.InjectModel)(fcm_publisher_error_logs_schema_1.FcmPublisherErrorLogs.name)),
    __param(3, (0, mongoose_1.InjectModel)(site_config_schema_1.SiteConfig.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        elastic_search_service_1.ElasticSearchService, typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _b : Object, typeof (_c = typeof tbs_site_config_1.TbsSiteConfigService !== "undefined" && tbs_site_config_1.TbsSiteConfigService) === "function" ? _c : Object, fcm_publisher_queue_service_1.FcmPublisherQueueService])
], FcmPublisherService);
exports.FcmPublisherService = FcmPublisherService;
//# sourceMappingURL=fcm-publisher.service.js.map