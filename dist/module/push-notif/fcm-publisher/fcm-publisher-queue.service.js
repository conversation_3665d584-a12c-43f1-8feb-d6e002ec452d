"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FcmPublisherQueueService = void 0;
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const schedule_1 = require("@nestjs/schedule");
const crypto_1 = require("crypto");
const ioredis_1 = require("ioredis");
const mongoose_2 = require("mongoose");
const tbs_site_config_1 = require("tbs-site-config");
const firebase_util_1 = require("../../../utils/firebase.util");
const elastic_search_service_1 = require("../../elastic-search/elastic-search.service");
const enum_1 = require("../../enum");
const fcm_publisher_queue_enum_1 = require("../../enum/fcm-publisher-queue.enum");
const fcm_publisher_error_logs_schema_1 = require("./schema/fcm-publisher-error-logs.schema");
const fcm_publisher_queue_schema_1 = require("./schema/fcm-publisher-queue.schema");
const fcm_publisher_schema_1 = require("./schema/fcm-publisher.schema");
let FcmPublisherQueueService = class FcmPublisherQueueService {
    constructor(queueModel, fcmErrorLogs, fcmPublisher, redisService, configService, siteConfigService, elasticService) {
        this.queueModel = queueModel;
        this.fcmErrorLogs = fcmErrorLogs;
        this.fcmPublisher = fcmPublisher;
        this.redisService = redisService;
        this.configService = configService;
        this.siteConfigService = siteConfigService;
        this.elasticService = elasticService;
        this.fcmHistoryIndex = this.configService.get("FCM_SUBSCRIPTION_HISTORY");
        this.fcmErrorToRemove = [
            "messaging/invalid-registration-token",
            "messaging/registration-token-not-registered",
        ];
        this.processing = false;
    }
    onApplicationShutdown() {
        return __awaiter(this, void 0, void 0, function* () {
        });
    }
    addQueue(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            let redisKey = "";
            let id = "";
            try {
                const script = `
        -- set keys from params
        local queueListKey = KEYS[1]
        local availableQueueKey = KEYS[2]
        local newKey = KEYS[3]
        
       -- set values from params
        local limit = tonumber(ARGV[1])
        local data = ARGV[2]
        
        -- get the available key of processed topic
        local availableQueue = redis.call('GET', availableQueueKey)
        local finalKey = ''
        
        -- if available topic exists or not equal to null
        if availableQueue then
          -- then push current queue to existing key
          redis.call('RPUSH', availableQueue, data)
          finalKey = availableQueue
        else
          -- else push current queue to new key
          redis.call('RPUSH', newKey, data)
          finalKey = newKey
        end
        
        -- get final key from queue list
        redis.call('LREM', queueListKey, 0, finalKey)
        redis.call('RPUSH', queueListKey, finalKey)
        
        -- get the count of final key value
        local count = redis.call('LLEN', finalKey)
        
        -- if count greater than or equal to limit
        if count >= limit then
          -- then delete the key, so next queue will create new key
          redis.call('DEL', availableQueueKey)
        else
          redis.call('SET', availableQueueKey, finalKey)
        end
        
        return {finalKey, availableQueueKey, availableQueue}
    `;
                const limit = yield this._getConfig("utils.fcm-topic-subscribe-limit", 3);
                const typeAndTopic = payload.type + "-" + payload.topic;
                const prefix = fcm_publisher_queue_enum_1.processingKey + typeAndTopic;
                if (typeof payload.value === "string")
                    payload.value = [payload.value];
                if (typeof payload.value === "string")
                    payload.value = [payload.value];
                const insert = yield this.queueModel.create(payload);
                id = insert._id;
                redisKey = (yield this.redisService.eval(script, 3, `${fcm_publisher_queue_enum_1.queueListKey}${typeAndTopic}`, `${fcm_publisher_queue_enum_1.availableQueueKey}${typeAndTopic}`, `${prefix}-${(0, crypto_1.randomUUID)()}`, +limit, id));
                if (!redisKey)
                    return "failed";
            }
            catch (err) {
                console.log(err);
                if (redisKey)
                    yield this.redisService.lrem(redisKey, 1, id);
            }
        });
    }
    processQueue() {
        return __awaiter(this, void 0, void 0, function* () {
            let processedKey = "";
            let lastTopicProceed = "";
            try {
                if (this.processing)
                    return;
                this.processing = true;
                yield this._cleansingQueueLock();
                const scriptGetProcessedKey = `
        local queueListKey = KEYS[1]
        local lockKey = KEYS[2]
        local availableKey = KEYS[3]
        
        local queueKeyLen = redis.call('LLEN', queueListKey)
        if queueKeyLen == 0 then
          return nil
        end

        local queueKey = redis.call('LPOP', queueListKey)
        local available = redis.call('GET', availableKey)
        
        if queueKey then
          redis.call('SET', lockKey, queueKey)
          
          if available == queueKey then
            redis.call('DEL', availableKey)
          end
          
          return queueKey
        end
        
        return nil
      `;
                const rawTopic = Object.keys(enum_1.TopicSendMapping)
                    .filter((key) => !!enum_1.TopicSendMapping[key])
                    .map((key) => enum_1.FcmPublisherMongoTopic[key]);
                const topics = rawTopic.flatMap((topic) => ["unsubscribe-" + topic, "subscribe-" + topic]);
                for (const topicMongo of topics) {
                    const _lockKey = fcm_publisher_queue_enum_1.lockKey + (0, crypto_1.randomUUID)();
                    const [type] = topicMongo.split("-");
                    let redisMock = "";
                    try {
                        processedKey = (yield this.redisService.eval(scriptGetProcessedKey, 3, `${fcm_publisher_queue_enum_1.queueListKey}${topicMongo}`, _lockKey, `${fcm_publisher_queue_enum_1.availableQueueKey}${topicMongo}`));
                        if (!processedKey)
                            continue;
                        redisMock = JSON.stringify({
                            processedKey: processedKey,
                            lockKey: _lockKey,
                            timestamp: Date.now(),
                        });
                    }
                    catch (err) {
                        console.log(err);
                        console.log({ redisMock, processedKey });
                    }
                    lastTopicProceed = topicMongo;
                    const idsList = yield this.redisService.lrange(processedKey, 0, -1);
                    if (redisMock) {
                        yield this.redisService.rpush(fcm_publisher_queue_enum_1.workerListKey, redisMock);
                    }
                    const data = yield this.queueModel.find({ _id: idsList }).lean();
                    if (!(data === null || data === void 0 ? void 0 : data.length)) {
                        yield this._cleanUpLock(redisMock, _lockKey, processedKey);
                        continue;
                    }
                    const reservedTopic = enum_1.FcmPublisherMongoTopicReverse[data[0].topic];
                    const topic = enum_1.FcmPublisherTopic[reservedTopic] + "." + data[0].value;
                    const replaceLastValue = data[0].replaceValue;
                    const fcmTopics = data.map((item) => item.fcm_token);
                    let subscribe;
                    let successData = [];
                    const errorsIndex = [];
                    const errorsMsg = [];
                    const bulkElastic = [];
                    if (data[0].subscribeToFcm) {
                        if (type === "subscribe") {
                            subscribe = yield firebase_util_1.FirebaseAdmin.messaging().subscribeToTopic(fcmTopics, topic);
                        }
                        if (type === "unsubscribe") {
                            subscribe = yield firebase_util_1.FirebaseAdmin.messaging().unsubscribeFromTopic(fcmTopics, topic);
                        }
                        subscribe.errors.map((err) => {
                            if (this.fcmErrorToRemove.includes(err.error.code)) {
                                errorsIndex.push(err.index);
                                errorsMsg.push(err.error.code);
                            }
                        });
                        data.map((item, i) => {
                            if (!errorsIndex.includes(i)) {
                                successData.push(item);
                                bulkElastic.push({
                                    id: (0, crypto_1.randomUUID)(),
                                    type: type,
                                    user_id: item.user_id,
                                    fcm_token: item.fcm_token,
                                    topic: topic,
                                    value: item.value,
                                    timestamp: new Date().toISOString(),
                                });
                            }
                        });
                    }
                    else {
                        successData = data;
                    }
                    const unsubscribe = {};
                    yield Promise.all(successData.map((item) => __awaiter(this, void 0, void 0, function* () {
                        var _a;
                        const check = yield this.fcmPublisher.findOne({ fcm_token: item.fcm_token }).lean();
                        if (replaceLastValue && data[0].subscribeToFcm) {
                            const topic = (_a = check.topics[item.topic]) === null || _a === void 0 ? void 0 : _a[0];
                            if (topic && topic !== data[0].value[0]) {
                                unsubscribe[topic] || (unsubscribe[topic] = { fcm: [], topic: enum_1.FcmPublisherTopic[data[0].topic.toUpperCase()] + "." + topic });
                                unsubscribe[topic].fcm.push(item.fcm_token);
                            }
                        }
                        yield this.insertToMongo(type, check, item, replaceLastValue);
                    })));
                    yield Promise.all(Object.values(unsubscribe).map((unsub) => __awaiter(this, void 0, void 0, function* () {
                        yield firebase_util_1.FirebaseAdmin.messaging().unsubscribeFromTopic(unsub.fcm, unsub.topic);
                    })));
                    yield this.queueModel.deleteMany({ _id: idsList });
                    const mockErrors = errorsIndex.map((dataIndex, i) => (Object.assign(Object.assign({}, (data[dataIndex] || {})), { topic, errors: errorsMsg[i] })));
                    yield this.fcmErrorLogs.insertMany(mockErrors);
                    if (bulkElastic.length) {
                        yield this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: bulkElastic });
                    }
                    yield this._cleanUpLock(redisMock, _lockKey, processedKey);
                }
            }
            catch (err) {
                if (processedKey && lastTopicProceed) {
                    const key = fcm_publisher_queue_enum_1.queueListKey + lastTopicProceed;
                    try {
                        yield this.redisService.lpush(key, processedKey);
                    }
                    catch (error) {
                        console.log("processing queue FCM", error);
                        throw error;
                    }
                }
                console.log("processing queue FCM", err);
                throw err;
            }
            finally {
                this.processing = false;
            }
        });
    }
    insertToMongo(type, exist, queueData, replaceValue) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const value = typeof queueData.value === "string" ? [queueData.value] : queueData.value;
            if (type === "subscribe") {
                if (exist) {
                    if (((_b = (_a = exist.topics) === null || _a === void 0 ? void 0 : _a[queueData.topic]) === null || _b === void 0 ? void 0 : _b.length) && !replaceValue) {
                        const add = typeof queueData.value === "string" ? queueData.value : { $each: queueData.value };
                        yield this.fcmPublisher.updateOne({ fcm_token: queueData.fcm_token }, { $addToSet: { ["topics." + queueData.topic]: add } });
                    }
                    else {
                        yield this.fcmPublisher.updateOne({ fcm_token: queueData.fcm_token }, { ["topics." + queueData.topic]: value });
                    }
                }
                else {
                    try {
                        yield this.fcmPublisher.create({
                            fcm_token: queueData.fcm_token,
                            topics: { [queueData.topic]: value },
                        });
                    }
                    catch (err) {
                        if (err.code === 11000) {
                            yield this.fcmPublisher.updateOne({ fcm_token: queueData.fcm_token }, { [`topics.${queueData.topic}`]: value });
                        }
                    }
                }
            }
            if (type === "unsubscribe") {
                yield this.fcmPublisher.updateOne({ fcm_token: queueData.fcm_token }, { $pull: { ["topics." + queueData.topic]: queueData.value } });
            }
        });
    }
    _cleansingQueueLock() {
        return __awaiter(this, void 0, void 0, function* () {
            const timeToLock = yield this._getConfig("utils.fcm-topic-lock-time", 60);
            const processedWorker = yield this.redisService.lrange(fcm_publisher_queue_enum_1.workerListKey, 0, -1);
            yield Promise.all(processedWorker.map((item) => __awaiter(this, void 0, void 0, function* () {
                const parsed = JSON.parse(item);
                const key = parsed.key || parsed.lockKey;
                if (Math.round((Date.now() - parsed.timestamp) / 1000 / 60) >= +timeToLock) {
                    const script = `
            local workerListKey = KEYS[1]
            local lockKey = KEYS[2]
            local queueListKey = KEYS[3]
            local workerListVal = ARGV[1]
            
            local queueKey = redis.call('GET', lockKey)
            
            redis.call('LREM', workerListKey, 1, workerListVal)
            redis.call('DEL', lockKey)
            redis.call('LPUSH', queueListKey, queueKey)
          `;
                    yield this.redisService.eval(script, 3, fcm_publisher_queue_enum_1.workerListKey, key, fcm_publisher_queue_enum_1.queueListKey, item);
                }
            })));
        });
    }
    _cleanUpLock(workerListMock, lockKey, processedKey) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield Promise.all([
                this.redisService.lrem(fcm_publisher_queue_enum_1.workerListKey, 1, workerListMock),
                this.redisService.del(lockKey),
                this.redisService.del(processedKey),
            ]);
        });
    }
    _getConfig(key, _default) {
        return __awaiter(this, void 0, void 0, function* () {
            let result;
            try {
                const config = yield this.siteConfigService.get(key);
                result = JSON.parse(config).value;
            }
            catch (err) {
                result = _default;
            }
            return result;
        });
    }
};
__decorate([
    (0, schedule_1.Interval)(FcmPublisherQueueService.name, 1000),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FcmPublisherQueueService.prototype, "processQueue", null);
FcmPublisherQueueService = __decorate([
    __param(0, (0, mongoose_1.InjectModel)(fcm_publisher_queue_schema_1.FcmPublisherQueue.name)),
    __param(1, (0, mongoose_1.InjectModel)(fcm_publisher_error_logs_schema_1.FcmPublisherErrorLogs.name)),
    __param(2, (0, mongoose_1.InjectModel)(fcm_publisher_schema_1.FcmPublisher.name)),
    __param(3, (0, nestjs_redis_1.InjectRedis)()),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        ioredis_1.default,
        config_1.ConfigService,
        tbs_site_config_1.TbsSiteConfigService,
        elastic_search_service_1.ElasticSearchService])
], FcmPublisherQueueService);
exports.FcmPublisherQueueService = FcmPublisherQueueService;
//# sourceMappingURL=fcm-publisher-queue.service.js.map