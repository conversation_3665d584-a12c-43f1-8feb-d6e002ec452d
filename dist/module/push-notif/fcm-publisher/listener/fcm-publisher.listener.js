"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FcmPublisherListener = exports.FcmPublisherEvent = void 0;
const event_emitter_1 = require("@nestjs/event-emitter");
const enum_1 = require("../../../enum");
const firebase_util_1 = require("../../../../utils/firebase.util");
const mongoose_1 = require("@nestjs/mongoose");
const fcm_publisher_schema_1 = require("../schema/fcm-publisher.schema");
const mongoose_2 = require("mongoose");
const fcm_user_mapper_schema_1 = require("../../fcm-user-mapper/schema/fcm-user-mapper.schema");
const elastic_search_service_1 = require("../../../elastic-search/elastic-search.service");
const config_1 = require("@nestjs/config");
const crypto_1 = require("crypto");
const tbs_site_config_1 = require("tbs-site-config");
const site_config_schema_1 = require("../../../site-configs/schema/site-config.schema");
var FcmPublisherEvent;
(function (FcmPublisherEvent) {
    FcmPublisherEvent["re_sync_fcm_topic"] = "re_sync_fcm_topic";
})(FcmPublisherEvent = exports.FcmPublisherEvent || (exports.FcmPublisherEvent = {}));
let FcmPublisherListener = class FcmPublisherListener {
    constructor(fcmPublisher, fcmMapper, configModel, elasticService, configService, siteConfigService) {
        this.fcmPublisher = fcmPublisher;
        this.fcmMapper = fcmMapper;
        this.configModel = configModel;
        this.elasticService = elasticService;
        this.configService = configService;
        this.siteConfigService = siteConfigService;
        this.fcmHistoryIndex = this.configService.get("FCM_SUBSCRIPTION_HISTORY");
        this.fcmErrorToRemove = [
            "messaging/invalid-registration-token",
            "messaging/invalid-registration-token",
            "messaging/registration-token-not-registered",
        ];
        this.processSync = false;
        this.siteConfigKey = "util.resync-fcm-topic";
    }
    onApplicationShutdown() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this._releaseLock();
        });
    }
    reSyncFcmTopic() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            this.processSync = true;
            console.time("Execution Time Re sync FCM Topic");
            const count = yield this.fcmPublisher.count();
            const limit = 1000;
            const batch = Math.ceil(count / limit);
            let i = -1;
            while (++i < batch) {
                console.time(`Re sync FCM Topic Subscription ${i + 1}/${batch}`);
                const data = yield this.fcmPublisher
                    .find()
                    .skip(i * limit)
                    .limit(limit)
                    .sort("_id");
                const mapTopic = new Map();
                yield Promise.all(data.map((item) => {
                    const topics = Object.keys(item.topics);
                    for (const topic of topics) {
                        const reverse = enum_1.FcmPublisherMongoTopicReverse[topic];
                        const prefix = enum_1.FcmPublisherTopic[reverse];
                        if (enum_1.TopicSendMapping[reverse]) {
                            for (const val of item.topics[topic]) {
                                const key = prefix + "." + val;
                                if (!mapTopic.has(key))
                                    mapTopic.set(key, []);
                                mapTopic.set(key, [...mapTopic.get(key), item.fcm_token]);
                            }
                        }
                    }
                }));
                for (const [key, val] of mapTopic.entries()) {
                    const fcmMapper = yield this.fcmMapper.find({ fcm_tokens: { $in: val } });
                    const hashingFcmMapper = {};
                    for (const mapper of fcmMapper) {
                        mapper.fcm_tokens.map((token) => (hashingFcmMapper[token] = mapper.userId));
                    }
                    const subscribe = yield firebase_util_1.FirebaseAdmin.messaging().subscribeToTopic(val, key);
                    const errorsIndex = [];
                    if (subscribe.errors.length && this.fcmErrorToRemove.includes((_a = subscribe.errors[0].error) === null || _a === void 0 ? void 0 : _a.code)) {
                        subscribe.errors.map((err) => {
                            if (this.fcmErrorToRemove.includes(err.error.code))
                                errorsIndex.push(err.index);
                        });
                    }
                    const bulkElastic = [];
                    yield Promise.all(val.map((fcm_token, i) => __awaiter(this, void 0, void 0, function* () {
                        const userId = hashingFcmMapper[fcm_token];
                        if (!errorsIndex.includes(i) && userId) {
                            yield this._checkAndSetEs(userId, fcm_token, key, "subscribe", bulkElastic);
                        }
                    })));
                    if (errorsIndex.length) {
                        yield Promise.all(errorsIndex.map((i) => __awaiter(this, void 0, void 0, function* () {
                            const userMap = hashingFcmMapper[val[i]];
                            if (userMap) {
                                yield this._checkAndSetEs(userMap, val[i], key, "unsubscribe", bulkElastic);
                                yield this.fcmMapper.updateOne({ userId: userMap }, { $pull: { fcm_tokens: val[i] } });
                                yield this.fcmPublisher.deleteOne({ fcm_token: val[i] });
                            }
                        })));
                    }
                    if (bulkElastic.length) {
                        yield this.elasticService.bulkInsertDocument({ index: this.fcmHistoryIndex, data: bulkElastic });
                    }
                }
                console.timeEnd(`Re sync FCM Topic Subscription ${i + 1}/${batch}`);
            }
            console.timeEnd("Execution Time Re sync FCM Topic");
            yield Promise.all([
                this.siteConfigService.updateValue(this.siteConfigKey, "inactive"),
                this.configModel.updateOne({ key: this.siteConfigKey }, { value: "inactive" }),
            ]);
            this.processSync = false;
        });
    }
    _releaseLock() {
        return __awaiter(this, void 0, void 0, function* () {
            yield Promise.all([this.siteConfigService.updateValue(this.siteConfigKey, "inactive")]);
            this.processSync = false;
        });
    }
    _checkAndSetEs(userId, fcm_token, key, type, arrRes) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const check = (yield this.elasticService.find({
                query: {
                    bool: {
                        must: [
                            { match: { user_id: userId } },
                            { match: { fcm_token: fcm_token } },
                            { match: { type: type } },
                            { match: { topic: key } },
                            { match: { value: key.split(".")[1] } },
                        ],
                    },
                },
            }));
            if (!((_a = check === null || check === void 0 ? void 0 : check.hits) === null || _a === void 0 ? void 0 : _a.length)) {
                arrRes.push({
                    id: (0, crypto_1.randomUUID)(),
                    type: type,
                    user_id: userId,
                    fcm_token: fcm_token,
                    topic: key,
                    value: key.split(".")[1],
                    timestamp: new Date().toISOString(),
                    subscribed_on_fcm: true,
                });
            }
        });
    }
};
__decorate([
    (0, event_emitter_1.OnEvent)(FcmPublisherEvent.re_sync_fcm_topic),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FcmPublisherListener.prototype, "reSyncFcmTopic", null);
FcmPublisherListener = __decorate([
    __param(0, (0, mongoose_1.InjectModel)(fcm_publisher_schema_1.FcmPublisher.name)),
    __param(1, (0, mongoose_1.InjectModel)(fcm_user_mapper_schema_1.FcmUserMapper.name)),
    __param(2, (0, mongoose_1.InjectModel)(site_config_schema_1.SiteConfig.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model, typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, elastic_search_service_1.ElasticSearchService, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof tbs_site_config_1.TbsSiteConfigService !== "undefined" && tbs_site_config_1.TbsSiteConfigService) === "function" ? _c : Object])
], FcmPublisherListener);
exports.FcmPublisherListener = FcmPublisherListener;
//# sourceMappingURL=fcm-publisher.listener.js.map