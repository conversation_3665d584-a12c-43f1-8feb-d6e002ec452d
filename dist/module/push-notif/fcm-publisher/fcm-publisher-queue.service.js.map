{"version": 3, "file": "fcm-publisher-queue.service.js", "sourceRoot": "", "sources": ["../../../../src/module/push-notif/fcm-publisher/fcm-publisher-queue.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAuD;AAEvD,2CAA+C;AAC/C,+CAA+C;AAC/C,+CAA4C;AAC5C,mCAAoC;AACpC,qCAA4B;AAC5B,uCAAiC;AACjC,qDAAuD;AACvD,gEAA6D;AAC7D,wFAAmF;AACnF,qCAAwH;AACxH,kFAM6C;AAC7C,8FAAgH;AAChH,oFAAmG;AACnG,wEAAmF;AAY5E,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAEmB,UAA4C,EAE5C,YAAkD,EAElD,YAAyC,EAEzC,YAAmB,EACnB,aAA4B,EAC5B,iBAAuC,EACvC,cAAoC;QATpC,eAAU,GAAV,UAAU,CAAkC;QAE5C,iBAAY,GAAZ,YAAY,CAAsC;QAElD,iBAAY,GAAZ,YAAY,CAA6B;QAEzC,iBAAY,GAAZ,YAAY,CAAO;QACnB,kBAAa,GAAb,aAAa,CAAe;QAC5B,sBAAiB,GAAjB,iBAAiB,CAAsB;QACvC,mBAAc,GAAd,cAAc,CAAsB;QAGtC,oBAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,0BAA0B,CAAC,CAAC;QAC7E,qBAAgB,GAAG;YAClC,sCAAsC;YACtC,6CAA6C;SAC9C,CAAC;QAGM,eAAU,GAAG,KAAK,CAAC;IATxB,CAAC;IAWE,qBAAqB;;QAkB3B,CAAC;KAAA;IAEK,QAAQ,CAAC,OAAkB;;YAC/B,IAAI,QAAQ,GAAG,EAAE,CAAC;YAClB,IAAI,EAAE,GAAG,EAAE,CAAC;YACZ,IAAI;gBACF,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyChB,CAAC;gBAEA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,iCAAiC,EAAE,CAAC,CAAC,CAAC;gBAG1E,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC;gBACxD,MAAM,MAAM,GAAG,wCAAa,GAAG,YAAY,CAAC;gBAE5C,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;oBAAE,OAAO,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAEvE,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;oBAAE,OAAO,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAEvE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrD,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC;gBAEhB,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CACtC,MAAM,EACN,CAAC,EACD,GAAG,uCAAY,GAAG,YAAY,EAAE,EAChC,GAAG,4CAAiB,GAAG,YAAY,EAAE,EACrC,GAAG,MAAM,IAAI,IAAA,mBAAU,GAAE,EAAE,EAC3B,CAAC,KAAK,EACN,EAAE,CACH,CAAW,CAAC;gBAEb,IAAI,CAAC,QAAQ;oBAAE,OAAO,QAAQ,CAAC;aAChC;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACjB,IAAI,QAAQ;oBAAE,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;aAC7D;QACH,CAAC;KAAA;IAGK,YAAY;;YAChB,IAAI,YAAY,GAAG,EAAE,CAAC;YACtB,IAAI,gBAAgB,GAAG,EAAE,CAAC;YAC1B,IAAI;gBAEF,IAAI,IAAI,CAAC,UAAU;oBAAE,OAAO;gBAE5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBAEvB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAEjC,MAAM,qBAAqB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;OAwB7B,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,uBAAgB,CAAC;qBAC3C,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,uBAAgB,CAAC,GAAG,CAAC,CAAC;qBACxC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,6BAAsB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAG7C,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,cAAc,GAAG,KAAK,EAAE,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;gBAE3F,KAAK,MAAM,UAAU,IAAI,MAAM,EAAE;oBAC/B,MAAM,QAAQ,GAAG,kCAAO,GAAG,IAAA,mBAAU,GAAE,CAAC;oBACxC,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACrC,IAAI,SAAS,GAAG,EAAE,CAAC;oBAGnB,IAAI;wBAEF,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAC1C,qBAAqB,EACrB,CAAC,EACD,GAAG,uCAAY,GAAG,UAAU,EAAE,EAC9B,QAAQ,EACR,GAAG,4CAAiB,GAAG,UAAU,EAAE,CACpC,CAAW,CAAC;wBAIb,IAAI,CAAC,YAAY;4BAAE,SAAS;wBAE5B,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;4BACzB,YAAY,EAAE,YAAY;4BAC1B,OAAO,EAAE,QAAQ;4BACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;yBACtB,CAAC,CAAC;qBACJ;oBAAC,OAAO,GAAG,EAAE;wBACZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBACjB,OAAO,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,CAAC;qBAC1C;oBAED,gBAAgB,GAAG,UAAU,CAAC;oBAG9B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACpE,IAAI,SAAS,EAAE;wBACb,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,wCAAa,EAAE,SAAS,CAAC,CAAC;qBACzD;oBAQD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;oBAEjE,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,EAAE;wBAEjB,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;wBAC3D,SAAS;qBACV;oBAED,MAAM,aAAa,GAAG,oCAA6B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;oBACnE,MAAM,KAAK,GAAG,wBAAiB,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBACrE,MAAM,gBAAgB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;oBAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAErD,IAAI,SAAS,CAAC;oBAEd,IAAI,WAAW,GAAG,EAAE,CAAC;oBACrB,MAAM,WAAW,GAAG,EAAE,CAAC;oBACvB,MAAM,SAAS,GAAG,EAAE,CAAC;oBACrB,MAAM,WAAW,GAAG,EAAE,CAAC;oBAEvB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE;wBAC1B,IAAI,IAAI,KAAK,WAAW,EAAE;4BACxB,SAAS,GAAG,MAAM,6BAAa,CAAC,SAAS,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;yBAChF;wBACD,IAAI,IAAI,KAAK,aAAa,EAAE;4BAC1B,SAAS,GAAG,MAAM,6BAAa,CAAC,SAAS,EAAE,CAAC,oBAAoB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;yBACpF;wBAGD,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;4BAC3B,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gCAClD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gCAC5B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;6BAChC;wBACH,CAAC,CAAC,CAAC;wBAGH,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;4BACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gCAC5B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gCACvB,WAAW,CAAC,IAAI,CAAC;oCACf,EAAE,EAAE,IAAA,mBAAU,GAAE;oCAChB,IAAI,EAAE,IAAI;oCACV,OAAO,EAAE,IAAI,CAAC,OAAO;oCACrB,SAAS,EAAE,IAAI,CAAC,SAAS;oCACzB,KAAK,EAAE,KAAK;oCACZ,KAAK,EAAE,IAAI,CAAC,KAAK;oCACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iCACpC,CAAC,CAAC;6BACJ;wBACH,CAAC,CAAC,CAAC;qBACJ;yBAAM;wBACL,WAAW,GAAG,IAAI,CAAC;qBACpB;oBAED,MAAM,WAAW,GAAG,EAAE,CAAC;oBACvB,MAAM,OAAO,CAAC,GAAG,CACf,WAAW,CAAC,GAAG,CAAC,CAAO,IAAI,EAAE,EAAE;;wBAC7B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;wBAEpF,IAAI,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE;4BAC9C,MAAM,KAAK,GAAG,MAAA,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,0CAAG,CAAC,CAAC,CAAC;4BAE5C,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gCACvC,WAAW,CAAC,KAAK,MAAjB,WAAW,CAAC,KAAK,IAAM,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,wBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,KAAK,EAAE,EAAC;gCACxG,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;6BAC7C;yBACF;wBAED,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBAChE,CAAC,CAAA,CAAC,CACH,CAAC;oBAEF,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAO,KAA0B,EAAE,EAAE;wBAClE,MAAM,6BAAa,CAAC,SAAS,EAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC/E,CAAC,CAAA,CAAC,CACH,CAAC;oBAGF,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;oBAGnD,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC,iCAChD,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,KAC1B,KAAK,EACL,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,IACpB,CAAC,CAAC;oBACJ,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;oBAE/C,IAAI,WAAW,CAAC,MAAM,EAAE;wBACtB,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;qBAClG;oBAGD,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;iBAG5D;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,YAAY,IAAI,gBAAgB,EAAE;oBACpC,MAAM,GAAG,GAAG,uCAAY,GAAG,gBAAgB,CAAC;oBAC5C,IAAI;wBACF,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;qBAClD;oBAAC,OAAO,KAAK,EAAE;wBACd,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;wBAC3C,MAAM,KAAK,CAAC;qBACb;iBACF;gBACD,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAM,GAAG,CAAC;aACX;oBAAS;gBACR,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;aACzB;QACH,CAAC;KAAA;IAEK,aAAa,CAAC,IAAY,EAAE,KAA0B,EAAE,SAA8B,EAAE,YAAqB;;;YACjH,MAAM,KAAK,GAAG,OAAO,SAAS,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC;YAExF,IAAI,IAAI,KAAK,WAAW,EAAE;gBACxB,IAAI,KAAK,EAAE;oBACT,IAAI,CAAA,MAAA,MAAA,KAAK,CAAC,MAAM,0CAAG,SAAS,CAAC,KAAK,CAAC,0CAAE,MAAM,KAAI,CAAC,YAAY,EAAE;wBAC5D,MAAM,GAAG,GAAG,OAAO,SAAS,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;wBAC/F,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAC/B,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE,EAClC,EAAE,SAAS,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,CACtD,CAAC;qBACH;yBAAM;wBACL,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAC/B,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE,EAClC,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CACzC,CAAC;qBACH;iBACF;qBAAM;oBACL,IAAI;wBACF,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;4BAC7B,SAAS,EAAE,SAAS,CAAC,SAAS;4BAC9B,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE;yBACrC,CAAC,CAAC;qBACJ;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE;4BACtB,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAC/B,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE,EAClC,EAAE,CAAC,UAAU,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CACzC,CAAC;yBACH;qBACF;iBACF;aACF;YAED,IAAI,IAAI,KAAK,aAAa,EAAE;gBAC1B,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAC/B,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE,EAClC,EAAE,KAAK,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,KAAK,EAAE,EAAE,CAC9D,CAAC;aACH;;KACF;IAEa,mBAAmB;;YAC/B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;YAC1E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,wCAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAE7E,MAAM,OAAO,CAAC,GAAG,CACf,eAAe,CAAC,GAAG,CAAC,CAAO,IAAI,EAAE,EAAE;gBACjC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAChC,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC;gBACzC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE;oBAC1E,MAAM,MAAM,GAAG;;;;;;;;;;;WAWd,CAAC;oBACF,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,wCAAa,EAAE,GAAG,EAAE,uCAAY,EAAE,IAAI,CAAC,CAAC;iBACjF;YACH,CAAC,CAAA,CAAC,CACH,CAAC;QACJ,CAAC;KAAA;IAEa,YAAY,CAAC,cAAsB,EAAE,OAAO,EAAE,YAAY;;YAQtE,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC;gBACvB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,wCAAa,EAAE,CAAC,EAAE,cAAc,CAAC;gBACxD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;gBAC9B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC;aACpC,CAAC,CAAC;QACL,CAAC;KAAA;IAEa,UAAU,CAAC,GAAW,EAAE,QAAa;;YACjD,IAAI,MAAW,CAAC;YAChB,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrD,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;aACnC;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,GAAG,QAAQ,CAAC;aACnB;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;KAAA;CACF,CAAA;AA3SO;IADL,IAAA,mBAAQ,EAAC,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC;;;;4DA2M7C;AApUU,wBAAwB;IAEhC,WAAA,IAAA,sBAAW,EAAC,8CAAiB,CAAC,IAAI,CAAC,CAAA;IAEnC,WAAA,IAAA,sBAAW,EAAC,uDAAqB,CAAC,IAAI,CAAC,CAAA;IAEvC,WAAA,IAAA,sBAAW,EAAC,mCAAY,CAAC,IAAI,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAW,GAAE,CAAA;qCALe,gBAAK;QAEH,gBAAK;QAEL,gBAAK;QAEL,iBAAK;QACJ,sBAAa;QACT,sCAAoB;QACvB,6CAAoB;GAZ5C,wBAAwB,CAqapC;AAraY,4DAAwB"}