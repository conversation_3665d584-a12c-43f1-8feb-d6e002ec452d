import { OnApplicationShutdown } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import Redis from "ioredis";
import { Model } from "mongoose";
import { TbsSiteConfigService } from "tbs-site-config";
import { ElasticSearchService } from "../../elastic-search/elastic-search.service";
import { FcmPublisherErrorLogsDocument } from "./schema/fcm-publisher-error-logs.schema";
import { FcmPublisherQueueDocument } from "./schema/fcm-publisher-queue.schema";
import { FcmPublisherDocument } from "./schema/fcm-publisher.schema";
interface IAddQueue {
    fcm_token: string;
    user_id: string;
    topic: string;
    value: string | Array<string>;
    type: "subscribe" | "unsubscribe";
    subscribeToFcm: boolean;
    replaceValue: boolean;
}
export declare class FcmPublisherQueueService implements OnApplicationShutdown {
    private readonly queueModel;
    private readonly fcmErrorLogs;
    private readonly fcmPublisher;
    private readonly redisService;
    private readonly configService;
    private readonly siteConfigService;
    private readonly elasticService;
    constructor(queueModel: Model<FcmPublisherQueueDocument>, fcmErrorLogs: Model<FcmPublisherErrorLogsDocument>, fcmPublisher: Model<FcmPublisherDocument>, redisService: Redis, configService: ConfigService, siteConfigService: TbsSiteConfigService, elasticService: ElasticSearchService);
    private readonly fcmHistoryIndex;
    private readonly fcmErrorToRemove;
    private processing;
    onApplicationShutdown(): Promise<void>;
    addQueue(payload: IAddQueue): Promise<string>;
    processQueue(): Promise<void>;
    insertToMongo(type: string, exist: Record<string, any>, queueData: Record<string, any>, replaceValue: boolean): Promise<void>;
    private _cleansingQueueLock;
    private _cleanUpLock;
    private _getConfig;
}
export {};
