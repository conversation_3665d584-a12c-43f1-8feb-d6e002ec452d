"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminNotificationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const role_enum_1 = require("../../enum/role.enum");
const rbac_enum_1 = require("../../enum/rbac.enum");
const notification_service_1 = require("./notification.service");
const platform_express_1 = require("@nestjs/platform-express");
const create_notification_dto_1 = require("./dto/create-notification.dto");
const global_body_validator_tbs_1 = require("global-body-validator-tbs");
const create_notification_target_dto_1 = require("./dto/create-notification-target.dto");
const update_notification_target_dto_1 = require("./dto/update-notification-target.dto");
const get_notification_target_dto_1 = require("./dto/get-notification-target.dto");
const get_notification_dto_1 = require("./dto/get-notification.dto");
let AdminNotificationController = class AdminNotificationController {
    constructor(notificationService) {
        this.notificationService = notificationService;
    }
    createnNotification(createNotificationDto, file) {
        return this.notificationService.createNotification(createNotificationDto, file);
    }
    findOne(id) {
        return this.notificationService.findNotificationById(id);
    }
    updateNotification(id, createNotificationDto, file) {
        return this.notificationService.updateNotification(id, createNotificationDto, file);
    }
    find(pagination) {
        return this.notificationService.findAllNotification(pagination);
    }
    sendNow(id) {
        return this.notificationService.sendNow(id);
    }
    resetNotification(id) {
        return this.notificationService.resetNotification(id);
    }
    removeNotification(id) {
        return this.notificationService.removeNotification(id);
    }
    createnNotificationTarget(createNotificationTargetDto) {
        return this.notificationService.createNotificationTarget(createNotificationTargetDto);
    }
    findTargetOne(id) {
        return this.notificationService.findTargetById(id);
    }
    findTarget(pagination) {
        return this.notificationService.findAllTarget(pagination);
    }
    updateNotificationTarget(id, updateNotificationTargetDto) {
        return this.notificationService.updateTarget(id, updateNotificationTargetDto);
    }
    removeNotificationTarget(id) {
        return this.notificationService.removeTarget(id);
    }
    importTarget(id, file, body) {
        body.file = file;
        return this.notificationService.importTarget(id, file, body.operation);
    }
    downloadTargetTemplate() {
        return this.notificationService.downloadTargetTemplate();
    }
};
__decorate([
    (0, common_1.Post)("create-notification"),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, swagger_1.ApiBody)({ type: create_notification_dto_1.CreateNotificationDto }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file")),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.POST),
    (0, global_body_validator_tbs_1.BypassFieldsValidation)("title", "body"),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)("file")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_notification_dto_1.CreateNotificationDto, typeof (_b = typeof Express !== "undefined" && (_a = Express.Multer) !== void 0 && _a.File) === "function" ? _b : Object]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "createnNotification", null);
__decorate([
    (0, common_1.Get)("get-notification/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)("update-notification/:id"),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, swagger_1.ApiBody)({ type: create_notification_dto_1.CreateNotificationDto }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file")),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.POST),
    (0, global_body_validator_tbs_1.BypassFieldsValidation)("title", "body"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)("file")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_notification_dto_1.CreateNotificationDto, typeof (_d = typeof Express !== "undefined" && (_c = Express.Multer) !== void 0 && _c.File) === "function" ? _d : Object]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "updateNotification", null);
__decorate([
    (0, common_1.Get)("get-notification"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_notification_dto_1.GetNotificationDto]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "find", null);
__decorate([
    (0, common_1.Get)("send-now/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "sendNow", null);
__decorate([
    (0, common_1.Get)("reset/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "resetNotification", null);
__decorate([
    (0, common_1.Delete)("/delete-notification/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.DELETE),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "removeNotification", null);
__decorate([
    (0, common_1.Post)("create-target"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.POST),
    (0, global_body_validator_tbs_1.BypassFieldsValidation)("name"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_notification_target_dto_1.CreateNotificationTargetDto]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "createnNotificationTarget", null);
__decorate([
    (0, common_1.Get)("get-target/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "findTargetOne", null);
__decorate([
    (0, common_1.Get)("get-target"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_notification_target_dto_1.GetNotificationTargetDto]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "findTarget", null);
__decorate([
    (0, common_1.Patch)("/update-target/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.PATCH),
    (0, global_body_validator_tbs_1.BypassFieldsValidation)("name"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_notification_target_dto_1.UpdateNotificationTargetDto]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "updateNotificationTarget", null);
__decorate([
    (0, common_1.Delete)("/delete-target/:id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.DELETE),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "removeNotificationTarget", null);
__decorate([
    (0, common_1.Post)("import-target/:id"),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file")),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.POST),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.UploadedFile)("file")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, create_notification_target_dto_1.ImportNotificationDto]),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "importTarget", null);
__decorate([
    (0, common_1.Get)("download-template-target"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AdminNotificationController.prototype, "downloadTargetTemplate", null);
AdminNotificationController = __decorate([
    (0, swagger_1.ApiTags)("Admin - Push Notif"),
    (0, common_1.Controller)("admin/notif-management"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, keycloak_connect_tbs_1.Resource)(rbac_enum_1.Controllers.ADMIN_NOTIFICATION),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [role_enum_1.Role.Admin, `realm:app-${role_enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __metadata("design:paramtypes", [notification_service_1.NotificationService])
], AdminNotificationController);
exports.AdminNotificationController = AdminNotificationController;
//# sourceMappingURL=admin-notification.controller.js.map