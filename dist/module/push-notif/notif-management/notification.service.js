"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const notification_schema_1 = require("./schema/notification.schema");
const notification_target_schema_1 = require("./schema/notification-target.schema");
const mongoose_2 = require("mongoose");
const path = require("path");
const amazons3_service_1 = require("../../../amazon-s3/amazons3.service");
const node_1 = require("read-excel-file/node");
const node_2 = require("write-excel-file/node");
const operation_enum_1 = require("../../enum/operation.enum");
const enum_1 = require("../../enum");
const push_notif_sender_enum_1 = require("../../enum/push-notif-sender.enum");
const push_notif_sender_service_1 = require("../push-notif-sender/push-notif-sender.service");
let NotificationService = class NotificationService {
    constructor(notificationModel, notificationTargetModel, s3Services, pushNotifSenderService) {
        this.notificationModel = notificationModel;
        this.notificationTargetModel = notificationTargetModel;
        this.s3Services = s3Services;
        this.pushNotifSenderService = pushNotifSenderService;
    }
    createNotification(createNotificationDto, file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (file) {
                const uploaded = yield this._upload(file);
                createNotificationDto.image = uploaded.Location;
            }
            if (!Array.isArray(createNotificationDto.target)) {
                createNotificationDto.target = createNotificationDto.target.split(",");
            }
            const newNotification = new this.notificationModel(createNotificationDto);
            return newNotification.save();
        });
    }
    updateNotification(id, params, file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            if (file) {
                const uploaded = yield this._upload(file);
                params.image = uploaded.Location;
            }
            if (!Array.isArray(params.target)) {
                params.target = params.target.split(",");
            }
            const update = yield this.notificationModel.findOneAndUpdate({ _id: id }, params, { new: true });
            if (!update) {
                throw "Update notification failed.";
            }
        });
    }
    findNotificationById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const notification = yield this.notificationModel
                    .findById(id)
                    .populate({ path: "target", select: { topic: 1, identifier: 1, name: 1 } });
                if (!notification) {
                    throw new common_1.HttpException("notification is not found", common_1.HttpStatus.BAD_REQUEST);
                }
                return notification;
            }
            catch (e) {
                throw new common_1.HttpException("notification is not found", common_1.HttpStatus.BAD_REQUEST);
            }
        });
    }
    findAllNotification(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            if (params.title) {
                filter.title = { $regex: new RegExp(params.title), $options: "i" };
            }
            if (params.status) {
                filter.status = params.status;
            }
            if (params.isSent) {
                filter.isSent = params.isSent;
            }
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
                populate: [{ path: "target", select: { _id: 1, name: 1, topic: 1 } }],
            };
            const result = yield this.notificationModel.paginate(filter, options);
            return result;
        });
    }
    removeNotification(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const deleting = yield this.notificationModel.deleteOne({ _id: id }).exec();
            return deleting;
        });
    }
    createNotificationTarget(createNotificationTargetDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const newAnswer = new this.notificationTargetModel(createNotificationTargetDto);
            return yield newAnswer.save();
        });
    }
    findTargetById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const target = yield this.notificationTargetModel.findById(id);
            if (!target) {
                throw new common_1.HttpException("Notification target is not found", common_1.HttpStatus.BAD_REQUEST);
            }
            return target;
        });
    }
    updateTarget(id, params) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const target = yield this.notificationTargetModel.findOneAndUpdate({ _id: id }, params, { new: true });
            if (!target) {
                throw "Update data failed.";
            }
            return target;
        });
    }
    findAllTarget(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            if (params.name) {
                filter.name = { $regex: new RegExp(params.name), $options: "i" };
            }
            if (params.topic) {
                filter.topic = params.topic;
            }
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            const result = yield this.notificationTargetModel.paginate(filter, options);
            return result;
        });
    }
    removeTarget(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const deleting = yield this.notificationTargetModel.deleteOne({ _id: id }).exec();
            return deleting;
        });
    }
    importTarget(id, file, type) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rows = yield (0, node_1.default)(Buffer.from(file.buffer));
                rows.shift();
                const identifier = [];
                rows.map((d) => {
                    if (d[0]) {
                        identifier.push(d[0].toString());
                    }
                });
                if (identifier.length < 0) {
                    throw new common_1.HttpException("Invalid format file", common_1.HttpStatus.BAD_REQUEST);
                }
                if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                    throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
                }
                const target = yield this.notificationTargetModel.findById(id);
                if (!target) {
                    throw new common_1.HttpException("Notification target is not found", common_1.HttpStatus.BAD_REQUEST);
                }
                const exIdentifier = type == operation_enum_1.Operation.Replace || target.identifier.length <= 0 ? identifier : [...target.identifier, ...identifier];
                target.identifier = exIdentifier;
                return yield target.save();
            }
            catch (e) {
                console.error(e);
                throw new common_1.HttpException(e.message, common_1.HttpStatus.BAD_REQUEST);
            }
        });
    }
    downloadTargetTemplate() {
        return __awaiter(this, void 0, void 0, function* () {
            const header = [{ value: "Identifier", fontWeight: "bold", align: "left" }];
            const row = [header];
            row.push([{ value: "51694666510000081" }]);
            row.push([{ value: "51694666510000082" }]);
            row.push([{ value: "51694666510000083" }]);
            return yield (0, node_2.default)(row, {
                columns: [{ width: 30 }],
                buffer: true,
            });
        });
    }
    sendNow(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const notification = yield this.notificationModel
                .findById(id)
                .populate({ path: "target", select: { topic: 1, identifier: 1 } });
            if (!notification) {
                throw new common_1.HttpException("notification is not found", common_1.HttpStatus.BAD_REQUEST);
            }
            return yield this._processQueueItem(notification, true);
        });
    }
    resetNotification(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const notification = yield this.notificationModel.findById(id);
            if (!notification) {
                throw new common_1.HttpException("notification is not found", common_1.HttpStatus.BAD_REQUEST);
            }
            notification.isSent = false;
            yield notification.save();
        });
    }
    queueingNotif() {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {
                $and: [
                    { status: true },
                    { isSent: false },
                    {
                        $or: [
                            {
                                startDate: { $lte: new Date() },
                            },
                        ],
                    },
                ],
            };
            const result = yield this.notificationModel
                .find(filter)
                .populate({ path: "target", select: { topic: 1, identifier: 1 } });
            if (result.length <= 0) {
                return;
            }
            let items = [];
            for (let i = 0; i < result.length; i++) {
                const part = yield this._processQueueItem(result[i], false);
                items = [...items, ...part];
            }
            return items;
        });
    }
    _processQueueItem(notification, force) {
        return __awaiter(this, void 0, void 0, function* () {
            let items = [];
            for (let i = 0; i < notification.target.length; i++) {
                const parts = yield this._splitQueueItem(notification, notification.target[i], force);
                yield this._sendToPublisher(notification, parts);
                items = [...items, ...parts];
            }
            return items;
        });
    }
    _splitQueueItem(item, target, force) {
        return __awaiter(this, void 0, void 0, function* () {
            const sitems = [];
            const itopic = [];
            if (target["topic"] == enum_1.UserDefinedTopic.ALL) {
                itopic.push({ type: enum_1.UserDefinedTopic.ALL, value: ".true" });
            }
            else {
                const listIds = yield this.chunkArrayInGroups(target["identifier"], 1000);
                listIds.map((d) => {
                    itopic.push({ type: target["topic"], value: this._addDot(target["topic"], d) });
                });
            }
            itopic.map((topic) => {
                sitems.push({
                    title: item.title,
                    body: item.body,
                    image: item.image,
                    target_url: item.target_url,
                    type: push_notif_sender_enum_1.PushNotifType.PROMOTION,
                    transport: push_notif_sender_enum_1.PushNotifTransport.TOPIC,
                    topic: [topic],
                    createdFrom: push_notif_sender_enum_1.CreatedFrom.CMS,
                    isSent: false,
                    send_date: !force ? item.startDate : false,
                });
            });
            return sitems;
        });
    }
    _addDot(type, arr) {
        const vals = [], dot = enum_1.TopicSendMapping[type];
        arr.map((item) => {
            vals.push(dot ? "." + item : item);
        });
        return vals.join(",");
    }
    _sendToPublisher(notification, items) {
        return __awaiter(this, void 0, void 0, function* () {
            for (let i = 0; i < items.length; i++) {
                yield this._addToQueue(items[i]);
            }
            notification.isSent = true;
            yield notification.save();
        });
    }
    _addToQueue(item) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.pushNotifSenderService.createApi(item);
            }
            catch (e) {
                console.error(e);
            }
        });
    }
    chunkArrayInGroups(arr, size) {
        return __awaiter(this, void 0, void 0, function* () {
            const myArray = [];
            for (let i = 0; i < arr.length; i += size) {
                myArray.push(arr.slice(i, i + size));
            }
            return myArray;
        });
    }
    _upload(file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!file) {
                throw new common_1.HttpException("File cannot be empty.", common_1.HttpStatus.BAD_REQUEST);
            }
            const ext = path.extname(file.originalname);
            const mimetype = file.mimetype;
            if (!ext.match(/(jpg|jpeg|png)$/)) {
                throw new common_1.HttpException("Invalid file format. Allowed file format: jpg, jpeg or png", common_1.HttpStatus.BAD_REQUEST);
            }
            return yield this.s3Services.uploadFile(file.buffer, ext, "notification-assets/", mimetype);
        });
    }
};
NotificationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(notification_schema_1.Notification.name)),
    __param(1, (0, mongoose_1.InjectModel)(notification_target_schema_1.NotificationTarget.name)),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, typeof (_b = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _b : Object, amazons3_service_1.AmazonS3Services,
        push_notif_sender_service_1.PushNotifSenderService])
], NotificationService);
exports.NotificationService = NotificationService;
//# sourceMappingURL=notification.service.js.map