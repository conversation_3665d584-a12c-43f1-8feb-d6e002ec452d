"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PushNotifSenderSchema = exports.PushNotifSender = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoosePaginate = require("mongoose-paginate-v2");
const push_notif_sender_enum_1 = require("../../../enum/push-notif-sender.enum");
const mongoose_2 = require("mongoose");
let PushNotifSender = class PushNotifSender {
};
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PushNotifSender.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PushNotifSender.prototype, "body", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PushNotifSender.prototype, "image", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PushNotifSender.prototype, "target_url", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: push_notif_sender_enum_1.PushNotifType }),
    __metadata("design:type", String)
], PushNotifSender.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: push_notif_sender_enum_1.PushNotifTransport }),
    __metadata("design:type", String)
], PushNotifSender.prototype, "transport", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.SchemaTypes.Mixed }),
    __metadata("design:type", Object)
], PushNotifSender.prototype, "topic", void 0);
__decorate([
    (0, mongoose_1.Prop)({ index: true, enum: push_notif_sender_enum_1.CreatedFrom }),
    __metadata("design:type", String)
], PushNotifSender.prototype, "createdFrom", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false, index: true }),
    __metadata("design:type", Boolean)
], PushNotifSender.prototype, "isSent", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PushNotifSender.prototype, "err_message", void 0);
PushNotifSender = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], PushNotifSender);
exports.PushNotifSender = PushNotifSender;
exports.PushNotifSenderSchema = mongoose_1.SchemaFactory.createForClass(PushNotifSender);
exports.PushNotifSenderSchema.plugin(mongoosePaginate);
//# sourceMappingURL=push-notif-sender.schema.js.map