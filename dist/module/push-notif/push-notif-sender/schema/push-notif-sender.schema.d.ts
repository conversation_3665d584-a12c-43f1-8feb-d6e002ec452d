/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { CreatedFrom, PushNotifTransport, PushNotifType } from "../../../enum/push-notif-sender.enum";
export declare class PushNotifSender {
    title: string;
    body: string;
    image: string;
    target_url: string;
    type: PushNotifType;
    transport: PushNotifTransport;
    topic: Record<string, any>;
    createdFrom: CreatedFrom;
    isSent: boolean;
    err_message: string;
}
export type PushNotifSenderDocument = PushNotifSender & Document;
export declare const PushNotifSenderSchema: import("mongoose").Schema<PushNotifSender, import("mongoose").Model<PushNotifSender, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, PushNotifSender>;
