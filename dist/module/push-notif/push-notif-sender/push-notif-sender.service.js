"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PushNotifSenderService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const push_notif_sender_schema_1 = require("./schema/push-notif-sender.schema");
const mongoose_2 = require("mongoose");
const config_1 = require("@nestjs/config");
const push_notif_sender_enum_1 = require("../../enum/push-notif-sender.enum");
const firebase_util_1 = require("../../../utils/firebase.util");
const enum_1 = require("../../enum");
const push_notif_sender_log_schema_1 = require("./schema/push-notif-sender-log.schema");
const fcm_publisher_schema_1 = require("../fcm-publisher/schema/fcm-publisher.schema");
const elastic_search_service_1 = require("../../elastic-search/elastic-search.service");
const fcm_user_mapper_schema_1 = require("../fcm-user-mapper/schema/fcm-user-mapper.schema");
const tbs_site_config_1 = require("tbs-site-config");
let PushNotifSenderService = class PushNotifSenderService {
    constructor(pushNotifSenderModel, logModel, fcmPublisherModel, fcmUserMapperModel, configService, elasticService, siteConfigService) {
        this.pushNotifSenderModel = pushNotifSenderModel;
        this.logModel = logModel;
        this.fcmPublisherModel = fcmPublisherModel;
        this.fcmUserMapperModel = fcmUserMapperModel;
        this.configService = configService;
        this.elasticService = elasticService;
        this.siteConfigService = siteConfigService;
        this.fcmInboxIndex = this.configService.get("FCM_INBOX");
    }
    createApi(mock) {
        return __awaiter(this, void 0, void 0, function* () {
            mock["transport"] = push_notif_sender_enum_1.PushNotifTransport.TOPIC;
            if (!mock.send_date) {
                return yield this.create({
                    body: mock.body,
                    image: mock.image,
                    target_url: mock.target_url,
                    type: mock.type,
                    title: mock.title,
                    topic: mock.topic,
                    transport: mock["transport"],
                    device_id: [],
                    isSent: true,
                });
            }
            return this.pushNotifSenderModel.create(mock);
        });
    }
    sendPushNotification() {
        return __awaiter(this, void 0, void 0, function* () {
            const count = yield this.pushNotifSenderModel.count({ isSent: false });
            let config = yield this.siteConfigService.get("pn.max_parallel_send");
            if (config) {
                config = JSON.parse(config).value;
            }
            else {
                config = "10";
            }
            const limit = +config;
            const batchTotal = Math.ceil(count / limit);
            let i = -1;
            while (++i < batchTotal) {
                const data = yield this.pushNotifSenderModel.find({ isSent: false }, {}, { sort: "_id", skip: i * limit, lean: true, limit });
                yield Promise.all(data.map((item) => __awaiter(this, void 0, void 0, function* () {
                    yield this.pushNotifSenderModel.updateOne({ _id: item._id }, { isSent: true });
                    yield this.create(item);
                })));
            }
            return "successfully send all pending";
        });
    }
    create(mock) {
        return __awaiter(this, void 0, void 0, function* () {
            let data;
            if (!(mock === null || mock === void 0 ? void 0 : mock._id)) {
                data = yield this.pushNotifSenderModel.create(mock);
            }
            else {
                data = yield this.pushNotifSenderModel.findById(mock._id);
            }
            let validate;
            if (mock.transport === push_notif_sender_enum_1.PushNotifTransport.TOPIC) {
                validate = yield this._validateCreateTopic(mock);
            }
            else if (mock.transport === push_notif_sender_enum_1.PushNotifTransport.DEVICE_ID) {
                validate = yield this._validateCreateSender(mock);
            }
            if (!validate.status) {
                data.err_message = validate.error;
            }
            else {
                if (mock.transport === push_notif_sender_enum_1.PushNotifTransport.TOPIC) {
                    yield this._sendByTopic(mock.topic, mock.type, mock.title, mock.body, mock.image, mock.target_url || "", data._id);
                }
                else if (mock.transport === push_notif_sender_enum_1.PushNotifTransport.DEVICE_ID) {
                    yield this._sendBySender(mock.device_id, mock.title, mock.body, mock.image, mock.target_url || "");
                }
            }
            return data;
        });
    }
    _saveInboxByTopic(topics) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = { topics: {} };
            topics.map((topic) => (filter.topics[enum_1.FcmPublisherTopic[topic.type]] = topic.value));
            yield this.fcmPublisherModel.find(filter);
        });
    }
    _validateGeneral(mock) {
        return __awaiter(this, void 0, void 0, function* () {
            const imageBaseUrls = [
                process.env.IMAGE_BASE_URL,
                process.env.IMAGE_BASE_URL_POS,
                process.env.AWS_S3_BASE_URL,
                ...process.env.CUSTOM_IMAGE_URL.split(","),
            ];
            const error = [];
            if (!mock.body)
                error.push("Body is required");
            if (mock.image && !imageBaseUrls.includes(mock.image)) {
                let allowed = false;
                for (const baseUrl of imageBaseUrls) {
                    if (new RegExp(`^${baseUrl}`, "g").test(mock.image)) {
                        allowed = true;
                        break;
                    }
                }
                if (!allowed)
                    error.push("Image url is not allowed");
            }
            if (error.length)
                return error;
            return error;
        });
    }
    _validateCreateTopic(mock) {
        return __awaiter(this, void 0, void 0, function* () {
            const maxTopicCombination = this.configService.get("MAX_TOPIC_COMBINATION");
            const error = yield this._validateGeneral(mock);
            if (!mock.topic)
                error.push("Topic is required");
            if (mock.topic && Object.keys(mock.topic).length > maxTopicCombination) {
                error.push(`Topic max ${maxTopicCombination} combinations`);
            }
            if (mock.topic) {
                mock.topic.map((topic) => {
                    if (!enum_1.FcmPublisherTopic[topic.type])
                        error.push(`Topic type ${topic.type} not found`);
                });
            }
            if (error.length)
                return { status: false, error: error.join(", ") };
            return { status: true, error: "" };
        });
    }
    _validateCreateSender(mock) {
        return __awaiter(this, void 0, void 0, function* () {
            const error = yield this._validateGeneral(mock);
            if (!mock.device_id.length)
                error.push("Sender ID is required");
            if (error.length)
                return { status: false, error: error.join(", ") };
            return { status: true, error: "" };
        });
    }
    _sendByTopic(topics, type, title, body, image, target_url, notifLogId) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const data = yield this._getTargetData(topics, filter);
            const notifMock = { body, title };
            if (image)
                notifMock.imageUrl = image;
            if (typeof data === "string") {
                return this._sendToFcmByTopic(data, notifMock, target_url, type, title, body, image, filter, notifLogId);
            }
            else {
                if (!(data === null || data === void 0 ? void 0 : data.length)) {
                    yield this.pushNotifSenderModel.findByIdAndUpdate(notifLogId, { $set: { isSent: false } });
                }
                return this._sendToFcmByToken(data, notifMock, target_url, type, title, body, image, filter, notifLogId);
            }
        });
    }
    _getTargetData(topics, filter) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmCondition = [];
            const mongoFilter = {};
            topics.map((topic) => {
                var _a;
                topic.value = (_a = String(topic.value)
                    .split(",")) === null || _a === void 0 ? void 0 : _a.map((item) => String(item).trim().toLowerCase());
                topic.value = [...new Set(topic.value || [])];
                if (enum_1.TopicSendMapping[topic.type]) {
                    const fcmTopic = enum_1.FcmPublisherTopic[topic.type];
                    for (const value of topic.value) {
                        fcmCondition.push(`'${fcmTopic + value}' in topics`);
                    }
                }
                else {
                    mongoFilter["topics." + enum_1.FcmPublisherMongoTopic[topic.type]] = topic.value;
                }
                const prefix = enum_1.FcmPublisherTopic[topic.type];
                filter["topics." + prefix.replace(/.*\./g, "")] = topic.value.map((item) => String(item).replace(".", ""));
            });
            if (fcmCondition.length)
                return fcmCondition.join(" && ");
            if (Object.keys(mongoFilter).length) {
                return yield this.fcmPublisherModel.find(mongoFilter).lean().exec();
            }
            return [];
        });
    }
    _sendToFcmByTopic(condition, notifMock, target_url, type, title, body, image, filter, notifLogId) {
        return __awaiter(this, void 0, void 0, function* () {
            const fcmData = { target_url };
            if (image)
                fcmData["image"] = image;
            yield firebase_util_1.FirebaseAdmin.messaging().send({
                condition: condition,
                notification: notifMock,
                apns: { payload: { aps: { sound: "default" } } },
                android: { notification: { sound: "default" } },
                data: fcmData,
            });
            yield this._sendToElastic(filter, type, title, body, image, target_url, notifLogId);
        });
    }
    _sendToFcmByToken(data, notifMock, target_url, type, title, body, image, filter, notifLogId) {
        return __awaiter(this, void 0, void 0, function* () {
            const limit = 1000;
            const batch = Math.ceil(data.length / limit);
            const fcmData = { target_url };
            if (image)
                fcmData["image"] = image;
            let i = -1;
            while (++i < batch) {
                const tokens = data.splice(0, limit).map((item) => item.fcm_token);
                yield firebase_util_1.FirebaseAdmin.messaging().sendMulticast({
                    tokens,
                    notification: notifMock,
                    apns: { payload: { aps: { sound: "default" } } },
                    android: { notification: { sound: "default" } },
                    data: fcmData,
                });
            }
            yield this._sendToElastic(filter, type, title, body, image, target_url, notifLogId);
        });
    }
    _sendBySender(device_id, title, body, image, target_url) {
        return __awaiter(this, void 0, void 0, function* () {
            const batch = Math.ceil(device_id.length / 1000);
            for (let i = 0; i < batch; i++) {
                const fcm = device_id.splice(0, 1000);
                const result = yield firebase_util_1.FirebaseAdmin.messaging().sendToDevice(fcm, {
                    notification: { title, body },
                    data: { target_url, image },
                });
                yield this.logModel.create({ result });
            }
        });
    }
    _sendToElastic(fcmFilter, type, title, body, image, target_url, notifLogId) {
        return __awaiter(this, void 0, void 0, function* () {
            const timestamp = new Date().toISOString();
            const total = yield this.fcmPublisherModel.count(fcmFilter);
            let config = yield this.siteConfigService.get("pn.populate_limit");
            if (config) {
                config = JSON.parse(config).value;
            }
            else {
                config = "1000";
            }
            const limit = +config;
            const totalBatch = Math.ceil(total / limit);
            let i = -1;
            while (++i < totalBatch) {
                const fcmTokens = yield this.fcmPublisherModel.find(fcmFilter, "fcm_token", {
                    sort: "_id",
                    skip: limit * i,
                    limit,
                });
                const users = yield this.fcmUserMapperModel.find({
                    fcm_tokens: { $in: fcmTokens.map((item) => item.fcm_token) },
                });
                const data = users.map((item) => {
                    return {
                        id: notifLogId + "-" + item._id,
                        type,
                        fcmMapId: item._id,
                        title,
                        body,
                        image,
                        target_url,
                        isRead: false,
                        timestamp,
                    };
                });
                if (data.length)
                    yield this.elasticService.bulkInsertDocument({ index: this.fcmInboxIndex, data });
            }
        });
    }
};
PushNotifSenderService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(push_notif_sender_schema_1.PushNotifSender.name)),
    __param(1, (0, mongoose_1.InjectModel)(push_notif_sender_log_schema_1.PushNotifSenderLog.name)),
    __param(2, (0, mongoose_1.InjectModel)(fcm_publisher_schema_1.FcmPublisher.name)),
    __param(3, (0, mongoose_1.InjectModel)(fcm_user_mapper_schema_1.FcmUserMapper.name)),
    __metadata("design:paramtypes", [Object, mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        config_1.ConfigService,
        elastic_search_service_1.ElasticSearchService,
        tbs_site_config_1.TbsSiteConfigService])
], PushNotifSenderService);
exports.PushNotifSenderService = PushNotifSenderService;
//# sourceMappingURL=push-notif-sender.service.js.map