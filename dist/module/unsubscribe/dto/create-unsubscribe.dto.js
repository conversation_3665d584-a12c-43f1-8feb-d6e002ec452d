"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptEmailDto = exports.CreateUnsubscribeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateUnsubscribeDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "token",
        required: true,
    }),
    __metadata("design:type", String)
], CreateUnsubscribeDto.prototype, "token", void 0);
exports.CreateUnsubscribeDto = CreateUnsubscribeDto;
class EncryptEmailDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "email address",
        required: true,
    }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], EncryptEmailDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "identifier",
        required: false,
    }),
    __metadata("design:type", String)
], EncryptEmailDto.prototype, "id", void 0);
exports.EncryptEmailDto = EncryptEmailDto;
//# sourceMappingURL=create-unsubscribe.dto.js.map