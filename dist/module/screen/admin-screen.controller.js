"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminScreenController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const screen_service_1 = require("./screen.service");
const create_screen_dto_1 = require("./dto/create-screen.dto");
const update_screen_dto_1 = require("./dto/update-screen.dto");
const get_screen_dto_1 = require("./dto/get-screen.dto");
const role_enum_1 = require("../enum/role.enum");
const rbac_enum_1 = require("../enum/rbac.enum");
const platform_express_1 = require("@nestjs/platform-express");
let AdminScreenController = class AdminScreenController {
    constructor(screenService) {
        this.screenService = screenService;
    }
    createScreen(createScreenDto, file) {
        return this.screenService.createScreen(createScreenDto, file);
    }
    findOne(id) {
        return this.screenService.findById(id);
    }
    find(pagination) {
        return this.screenService.findAllAdmin(pagination);
    }
    update(id, updateScreenDto, file) {
        return this.screenService.update(id, updateScreenDto, file);
    }
    remove(id) {
        return this.screenService.remove(id);
    }
};
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, swagger_1.ApiBody)({ type: create_screen_dto_1.CreateScreenDto }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file")),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.POST),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)("file")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_screen_dto_1.CreateScreenDto, typeof (_b = typeof Express !== "undefined" && (_a = Express.Multer) !== void 0 && _a.File) === "function" ? _b : Object]),
    __metadata("design:returntype", void 0)
], AdminScreenController.prototype, "createScreen", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminScreenController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.GET),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_screen_dto_1.GetScreenDto]),
    __metadata("design:returntype", void 0)
], AdminScreenController.prototype, "find", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, swagger_1.ApiConsumes)("multipart/form-data"),
    (0, swagger_1.ApiBody)({ type: update_screen_dto_1.UpdateScreenDto }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file")),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.PATCH),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)("file")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_screen_dto_1.UpdateScreenDto, typeof (_d = typeof Express !== "undefined" && (_c = Express.Multer) !== void 0 && _c.File) === "function" ? _d : Object]),
    __metadata("design:returntype", void 0)
], AdminScreenController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, keycloak_connect_tbs_1.Scopes)(rbac_enum_1.Scope.DELETE),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AdminScreenController.prototype, "remove", null);
AdminScreenController = __decorate([
    (0, swagger_1.ApiTags)("Admin - Screen"),
    (0, common_1.Controller)("admin/screen"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, keycloak_connect_tbs_1.Resource)(rbac_enum_1.Controllers.ADMIN_SCREEN),
    (0, keycloak_connect_tbs_1.Roles)({ roles: [role_enum_1.Role.Admin, `realm:app-${role_enum_1.Role.Admin}`], mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY }),
    __metadata("design:paramtypes", [screen_service_1.ScreenService])
], AdminScreenController);
exports.AdminScreenController = AdminScreenController;
//# sourceMappingURL=admin-screen.controller.js.map