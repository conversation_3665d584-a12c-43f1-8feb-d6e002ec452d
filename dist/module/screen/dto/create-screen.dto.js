"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateScreenDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const screen_type_enum_1 = require("../enum/screen-type-enum");
const transform_boolean_decorator_1 = require("../../../decorator/transform-boolean.decorator");
class CreateScreenDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsBoolean)(),
    (0, transform_boolean_decorator_1.TransformBoolean)(),
    __metadata("design:type", Boolean)
], CreateScreenDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, default: 0 }),
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    __metadata("design:type", Number)
], CreateScreenDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Type of screen",
        enum: screen_type_enum_1.ScreenTypeEnum,
    }),
    (0, class_validator_1.IsEnum)(screen_type_enum_1.ScreenTypeEnum),
    __metadata("design:type", String)
], CreateScreenDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_transformer_1.Transform)(({ value }) => new Date(value)),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], CreateScreenDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_transformer_1.Transform)(({ value }) => new Date(value)),
    __metadata("design:type", Date)
], CreateScreenDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
        type: "string",
        format: "binary",
    }),
    __metadata("design:type", Object)
], CreateScreenDto.prototype, "file", void 0);
exports.CreateScreenDto = CreateScreenDto;
//# sourceMappingURL=create-screen.dto.js.map