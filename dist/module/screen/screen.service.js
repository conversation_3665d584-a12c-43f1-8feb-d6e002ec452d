"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScreenService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const path = require("path");
const amazons3_service_1 = require("../../amazon-s3/amazons3.service");
let ScreenService = class ScreenService {
    constructor(screenModel, s3Services) {
        this.screenModel = screenModel;
        this.s3Services = s3Services;
    }
    createScreen(createScreenDto, file) {
        return __awaiter(this, void 0, void 0, function* () {
            const uploaded = yield this._upload(file);
            createScreenDto.path = uploaded.Location;
            const newScreen = new this.screenModel(createScreenDto);
            return newScreen.save();
        });
    }
    findAllAdmin(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            if (String(params.status) === "true" || String(params.status) === "false") {
                filter.status = String(params.status) === "true";
            }
            if (params.type) {
                filter.type = params.type;
            }
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
            };
            const result = yield this.screenModel.paginate(filter, options);
            return result;
        });
    }
    update(id, params, file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            if (file) {
                const uploaded = yield this._upload(file);
                params.path = uploaded.Location;
            }
            const update = yield this.screenModel.findOneAndUpdate({ _id: id }, params, { new: true });
            if (!update) {
                throw "Update screen failed.";
            }
            return update;
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const screen = this.screenModel.findById(id);
            if (!screen) {
                throw new common_1.HttpException("Screen is not found", common_1.HttpStatus.BAD_REQUEST);
            }
            return screen;
        });
    }
    remove(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const deleting = yield this.screenModel.deleteOne({ _id: id }).exec();
            return deleting;
        });
    }
    findAllPublic(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            filter.status = true;
            if (params.type) {
                filter.type = params.type;
            }
            filter.startDate = { $lte: new Date() };
            filter.endDate = { $gte: new Date() };
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort || "-position",
            };
            const result = yield this.screenModel.paginate(filter, options);
            return result;
        });
    }
    _upload(file) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!file) {
                throw new common_1.HttpException("File cannot be empty.", common_1.HttpStatus.BAD_REQUEST);
            }
            const ext = path.extname(file.originalname);
            const mimetype = file.mimetype;
            if (!ext.match(/(mkv|flv|webm|avi|mov|wmv|mp4|mpg|mpeg|jpg|jpeg|png)$/)) {
                throw new common_1.HttpException("Invalid file format. Allowed file format: mkv, flv, webm, avi, mov, wmv, mp4, mpg, mpeg, jpg, jpeg or png", common_1.HttpStatus.BAD_REQUEST);
            }
            return yield this.s3Services.uploadFile(file.buffer, ext, "pos-assets/", mimetype);
        });
    }
};
ScreenService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Screen")),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, amazons3_service_1.AmazonS3Services])
], ScreenService);
exports.ScreenService = ScreenService;
//# sourceMappingURL=screen.service.js.map