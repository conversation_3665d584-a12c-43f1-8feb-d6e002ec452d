"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KibanaDashboardModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const kibana_dashboard_schema_1 = require("./schema/kibana-dashboard.schema");
const kibana_dashboard_controller_1 = require("./kibana-dashboard.controller");
const kibana_dashboard_service_1 = require("./kibana-dashboard.service");
const kibana_dashboard_pos_controller_1 = require("./kibana-dashboard-pos.controller");
let KibanaDashboardModule = class KibanaDashboardModule {
};
KibanaDashboardModule = __decorate([
    (0, common_1.Module)({
        controllers: [kibana_dashboard_controller_1.KibanaDashboardController, kibana_dashboard_pos_controller_1.KibanaDashboardPosController],
        providers: [kibana_dashboard_service_1.KibanaDashboardService],
        imports: [
            mongoose_1.MongooseModule.forFeature([
                {
                    name: kibana_dashboard_schema_1.KibanaDashboard.name,
                    schema: kibana_dashboard_schema_1.KibanaDashboardSchema,
                },
            ]),
        ],
    })
], KibanaDashboardModule);
exports.KibanaDashboardModule = KibanaDashboardModule;
//# sourceMappingURL=kibana-dashboard.module.js.map