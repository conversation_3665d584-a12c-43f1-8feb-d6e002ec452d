"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KibanaDashboardPosController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const role_enum_1 = require("../enum/role.enum");
const kibana_dashboard_service_1 = require("./kibana-dashboard.service");
let KibanaDashboardPosController = class KibanaDashboardPosController {
    constructor(kibanaDashboardService) {
        this.kibanaDashboardService = kibanaDashboardService;
    }
    getDashboardPos() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kibanaDashboardService.getDashboardPos();
        });
    }
};
__decorate([
    (0, common_1.Get)("/"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KibanaDashboardPosController.prototype, "getDashboardPos", null);
KibanaDashboardPosController = __decorate([
    (0, swagger_1.ApiTags)("Kibana Dashboard - POS"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, keycloak_connect_tbs_1.Roles)({
        roles: [role_enum_1.Role.Manager, `realm:app-${role_enum_1.Role.Manager}`, role_enum_1.Role.Operator, `realm:app-${role_enum_1.Role.Operator}`],
        mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY,
    }),
    (0, common_1.Controller)("kibana-dashboard-pos"),
    __metadata("design:paramtypes", [kibana_dashboard_service_1.KibanaDashboardService])
], KibanaDashboardPosController);
exports.KibanaDashboardPosController = KibanaDashboardPosController;
//# sourceMappingURL=kibana-dashboard-pos.controller.js.map