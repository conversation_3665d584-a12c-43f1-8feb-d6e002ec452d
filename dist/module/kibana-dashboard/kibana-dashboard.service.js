"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KibanaDashboardService = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const common_1 = require("@nestjs/common");
const kibana_dashboard_enum_1 = require("./enum/kibana-dashboard.enum");
const edit_kibana_dashboard_dto_1 = require("./dto/edit-kibana-dashboard.dto");
let KibanaDashboardService = class KibanaDashboardService {
    constructor(kibanaDashboardModel) {
        this.kibanaDashboardModel = kibanaDashboardModel;
    }
    create(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kibanaDashboardModel.create(payload);
        });
    }
    getAll() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kibanaDashboardModel.find();
        });
    }
    getDashboardPos() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kibanaDashboardModel.find({ show_for: { $in: [kibana_dashboard_enum_1.ShowForEnum.All, kibana_dashboard_enum_1.ShowForEnum.POS] } });
        });
    }
    getDashboardCms() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kibanaDashboardModel.find({ show_for: { $in: [kibana_dashboard_enum_1.ShowForEnum.All, kibana_dashboard_enum_1.ShowForEnum.CMS] } });
        });
    }
    editDashboard(payload, kibanaDashboardId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kibanaDashboardModel.findByIdAndUpdate(kibanaDashboardId, payload, { new: true });
        });
    }
    getDashboardById(kibanaDashboardId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kibanaDashboardModel.findById(kibanaDashboardId);
        });
    }
    deleteDashbaord(kibanaDashboardId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kibanaDashboardModel.findByIdAndDelete(kibanaDashboardId);
        });
    }
};
__decorate([
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [edit_kibana_dashboard_dto_1.EditKibanaDashBoardDto, String]),
    __metadata("design:returntype", Promise)
], KibanaDashboardService.prototype, "editDashboard", null);
__decorate([
    __param(0, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KibanaDashboardService.prototype, "getDashboardById", null);
__decorate([
    __param(0, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KibanaDashboardService.prototype, "deleteDashbaord", null);
KibanaDashboardService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("KibanaDashboard")),
    __metadata("design:paramtypes", [mongoose_2.Model])
], KibanaDashboardService);
exports.KibanaDashboardService = KibanaDashboardService;
//# sourceMappingURL=kibana-dashboard.service.js.map