/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { KibanaDashboardService } from "./kibana-dashboard.service";
import { CreateKibanaDashboardDto } from "./dto/create-kibana-dashboard.dto";
import { EditKibanaDashBoardDto } from "./dto/edit-kibana-dashboard.dto";
export declare class KibanaDashboardController {
    private readonly kibanaDashboardService;
    constructor(kibanaDashboardService: KibanaDashboardService);
    create(payload: CreateKibanaDashboardDto): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    getDashboardCms(): Promise<(import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    })[]>;
    get(): Promise<(import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    })[]>;
    getDashboardByid(kibanaDashboardId: string): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    editDashboardDetail(payload: EditKibanaDashBoardDto, kibanaDashboardId: string): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    deleteDashboard(kibanaDashboardId: string): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
}
