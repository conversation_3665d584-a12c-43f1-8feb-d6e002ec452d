/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose-paginate-v2" />
import { Model } from "mongoose";
import { KibanaDashboardDocument } from "./schema/kibana-dashboard.schema";
import { CreateKibanaDashboardDto } from "./dto/create-kibana-dashboard.dto";
import { EditKibanaDashBoardDto } from "./dto/edit-kibana-dashboard.dto";
export declare class KibanaDashboardService {
    private kibanaDashboardModel;
    constructor(kibanaDashboardModel: Model<KibanaDashboardDocument>);
    create(payload: CreateKibanaDashboardDto): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    getAll(): Promise<(import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    })[]>;
    getDashboardPos(): Promise<(import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    })[]>;
    getDashboardCms(): Promise<(import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    })[]>;
    editDashboard(payload: EditKibanaDashBoardDto, kibanaDashboardId: string): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    getDashboardById(kibanaDashboardId: string): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
    deleteDashbaord(kibanaDashboardId: string): Promise<import("./schema/kibana-dashboard.schema").KibanaDashboard & import("mongoose").Document<any, any, any> & {
        _id: import("mongoose").Types.ObjectId;
    }>;
}
