"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KibanaDashboardSchema = exports.KibanaDashboard = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const kibana_dashboard_enum_1 = require("../enum/kibana-dashboard.enum");
let KibanaDashboard = class KibanaDashboard extends mongoose_2.Document {
};
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String }),
    __metadata("design:type", String)
], KibanaDashboard.prototype, "url", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String }),
    __metadata("design:type", String)
], KibanaDashboard.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, enum: kibana_dashboard_enum_1.ShowForEnum }),
    __metadata("design:type", String)
], KibanaDashboard.prototype, "show_for", void 0);
KibanaDashboard = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
    })
], KibanaDashboard);
exports.KibanaDashboard = KibanaDashboard;
exports.KibanaDashboardSchema = mongoose_1.SchemaFactory.createForClass(KibanaDashboard);
//# sourceMappingURL=kibana-dashboard.schema.js.map