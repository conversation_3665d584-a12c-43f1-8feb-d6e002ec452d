"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KibanaDashboardController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const role_enum_1 = require("../enum/role.enum");
const kibana_dashboard_service_1 = require("./kibana-dashboard.service");
const create_kibana_dashboard_dto_1 = require("./dto/create-kibana-dashboard.dto");
const edit_kibana_dashboard_dto_1 = require("./dto/edit-kibana-dashboard.dto");
let KibanaDashboardController = class KibanaDashboardController {
    constructor(kibanaDashboardService) {
        this.kibanaDashboardService = kibanaDashboardService;
    }
    create(payload) {
        return this.kibanaDashboardService.create(payload);
    }
    getDashboardCms() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kibanaDashboardService.getDashboardCms();
        });
    }
    get() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kibanaDashboardService.getAll();
        });
    }
    getDashboardByid(kibanaDashboardId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kibanaDashboardService.getDashboardById(kibanaDashboardId);
        });
    }
    editDashboardDetail(payload, kibanaDashboardId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kibanaDashboardService.editDashboard(payload, kibanaDashboardId);
        });
    }
    deleteDashboard(kibanaDashboardId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kibanaDashboardService.deleteDashbaord(kibanaDashboardId);
        });
    }
};
__decorate([
    (0, common_1.Post)("/"),
    (0, swagger_1.ApiBody)({ type: create_kibana_dashboard_dto_1.CreateKibanaDashboardDto, description: "Create Kibana Dashboard" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_kibana_dashboard_dto_1.CreateKibanaDashboardDto]),
    __metadata("design:returntype", void 0)
], KibanaDashboardController.prototype, "create", null);
__decorate([
    (0, common_1.Get)("/cms"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KibanaDashboardController.prototype, "getDashboardCms", null);
__decorate([
    (0, common_1.Get)("/"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KibanaDashboardController.prototype, "get", null);
__decorate([
    (0, common_1.Get)("/:id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KibanaDashboardController.prototype, "getDashboardByid", null);
__decorate([
    (0, common_1.Patch)("/:id"),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [edit_kibana_dashboard_dto_1.EditKibanaDashBoardDto, String]),
    __metadata("design:returntype", Promise)
], KibanaDashboardController.prototype, "editDashboardDetail", null);
__decorate([
    (0, common_1.Delete)("/:id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KibanaDashboardController.prototype, "deleteDashboard", null);
KibanaDashboardController = __decorate([
    (0, swagger_1.ApiTags)("Kibana Dashboard"),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, keycloak_connect_tbs_1.Roles)({
        roles: [role_enum_1.Role.Admin, `realm:app-${role_enum_1.Role.Admin}`],
        mode: keycloak_connect_tbs_1.RoleMatchingMode.ANY,
    }),
    (0, common_1.Controller)("kibana-dashboard"),
    __metadata("design:paramtypes", [kibana_dashboard_service_1.KibanaDashboardService])
], KibanaDashboardController);
exports.KibanaDashboardController = KibanaDashboardController;
//# sourceMappingURL=kibana-dashboard.controller.js.map