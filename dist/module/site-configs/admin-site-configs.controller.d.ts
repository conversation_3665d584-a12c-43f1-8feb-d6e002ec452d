import { CreateConfigDto } from "./dto/create-config.dto";
import { UpdateConfigDto } from "./dto/update-config.dto";
import { SiteConfigService } from "./site-config.service";
import { FindAllConfigDto } from "./dto/find-all-config.dto";
export declare class AdminSiteConfigsController {
    private readonly service;
    constructor(service: SiteConfigService);
    findAll(query: FindAllConfigDto): Promise<any>;
    create(payload: CreateConfigDto): Promise<any>;
    findOne(id: string): Promise<import("./schema/site-config.schema").SiteConfig>;
    update(id: string, payload: UpdateConfigDto): Promise<any>;
    remove(id: string): Promise<any>;
}
