"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteConfigService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const tbs_site_config_1 = require("tbs-site-config");
let SiteConfigService = class SiteConfigService {
    constructor(configModel, siteConfigService) {
        this.configModel = configModel;
        this.siteConfigService = siteConfigService;
    }
    findAll() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.siteConfigService.getAll();
        });
    }
    findByKey(key) {
        return __awaiter(this, void 0, void 0, function* () {
            const config = yield this.siteConfigService.get(key);
            if (!config) {
                throw new common_1.HttpException("Config not found", common_1.HttpStatus.BAD_REQUEST);
            }
            return JSON.parse(config);
        });
    }
    findAllAdmin(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const { page = 1, limit = 10, sort, key_prefix } = params;
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort,
                lean: true,
            };
            const query = {};
            if (key_prefix)
                query.key = { $regex: new RegExp(`^${key_prefix}`, "i") };
            const res = yield this.configModel.paginate(query, options);
            res.docs = yield Promise.all(res.docs.map((item) => __awaiter(this, void 0, void 0, function* () {
                var _a;
                const redis = yield this.siteConfigService.get(item.key);
                if (redis) {
                    item.value = (_a = JSON.parse(redis)) === null || _a === void 0 ? void 0 : _a.value;
                }
                return item;
            })));
            return res;
        });
    }
    create(createConfigDto) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield this.siteConfigService.create(createConfigDto);
            }
            catch (err) {
                if (/already exists/.test(err.message)) {
                    throw new common_1.HttpException(err.message, common_1.HttpStatus.BAD_REQUEST);
                }
                throw err;
            }
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const fromDb = yield this.configModel.findById(id).exec();
            const fromRedis = yield this.siteConfigService.get(fromDb.key);
            if (fromRedis) {
                return JSON.parse(fromRedis);
            }
            return fromDb;
        });
    }
    update(id, payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = yield this.configModel.findById(id).lean();
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const update = yield this.configModel.findOneAndUpdate({ _id: id }, payload, { new: true });
            yield this.siteConfigService.update(update.key, Object.assign(Object.assign({}, data), payload));
            if (!update) {
                throw "Update data failed.";
            }
            return update;
        });
    }
    remove(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const check = yield this.configModel.findById(id, "key").lean();
            if (check) {
                yield this.configModel.deleteOne({ _id: check._id });
                yield this.siteConfigService.delete(check.key);
                return true;
            }
            return check;
        });
    }
};
SiteConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("SiteConfig")),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, typeof (_b = typeof tbs_site_config_1.TbsSiteConfigService !== "undefined" && tbs_site_config_1.TbsSiteConfigService) === "function" ? _b : Object])
], SiteConfigService);
exports.SiteConfigService = SiteConfigService;
//# sourceMappingURL=site-config.service.js.map