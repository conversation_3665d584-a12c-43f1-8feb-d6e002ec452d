import { PaginateModel } from "mongoose";
import { CreateConfigDto } from "./dto/create-config.dto";
import { UpdateConfigDto } from "./dto/update-config.dto";
import { SiteConfig, SiteConfigDocument } from "./schema/site-config.schema";
import { FindAllConfigDto } from "./dto/find-all-config.dto";
import { TbsSiteConfigService } from "tbs-site-config";
export declare class SiteConfigService {
    private configModel;
    private readonly siteConfigService;
    constructor(configModel: PaginateModel<SiteConfigDocument>, siteConfigService: TbsSiteConfigService);
    findAll(): Promise<any>;
    findByKey(key: string): Promise<any>;
    findAllAdmin(params: FindAllConfigDto): Promise<any>;
    create(createConfigDto: CreateConfigDto): Promise<any>;
    findById(id: string): Promise<SiteConfig | null>;
    update(id: string, payload: UpdateConfigDto): Promise<any>;
    remove(id: string): Promise<any>;
}
