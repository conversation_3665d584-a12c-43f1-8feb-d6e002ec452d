import Redis from "ioredis";
import { PaginateModel } from "mongoose";
import { StoreGroupService } from "../store-group/store-group.service";
import { CreatePlaylistDto } from "./dto/create-playlist.dto";
import { GetPlaylistDto } from "./dto/get-playlist.dto";
import { PlaylistDocument } from "./schema/playlist.schema";
export declare class PlaylistService {
    private playlistModel;
    private readonly redisService;
    private readonly storeGroupService;
    constructor(playlistModel: PaginateModel<PlaylistDocument>, redisService: Redis, storeGroupService: StoreGroupService);
    create(params: CreatePlaylistDto): Promise<any>;
    findById(id: string): Promise<any>;
    findAll(params: GetPlaylistDto): Promise<any>;
    update(id: string, params: CreatePlaylistDto): Promise<any>;
    remove(id: string): Promise<{
        message: string;
    }>;
    getActivePlaylists(storeCode?: string): Promise<any>;
    flushAllCache(): Promise<boolean>;
    _findStoreGroupIds(storeCode: string): Promise<any[]>;
}
