"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlaylistService = void 0;
const nestjs_redis_1 = require("@liaoliaots/nestjs-redis");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const ioredis_1 = require("ioredis");
const mongoose_2 = require("mongoose");
const store_group_service_1 = require("../store-group/store-group.service");
let PlaylistService = class PlaylistService {
    constructor(playlistModel, redisService, storeGroupService) {
        this.playlistModel = playlistModel;
        this.redisService = redisService;
        this.storeGroupService = storeGroupService;
    }
    create(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const playlist = new this.playlistModel(params);
            yield playlist.save();
            yield this.flushAllCache();
            return playlist;
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const playlist = yield this.playlistModel.findById(id);
            if (!playlist) {
                throw new common_1.HttpException("Playlist not found.", common_1.HttpStatus.NOT_FOUND);
            }
            return playlist;
        });
    }
    findAll(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const filter = {};
            const { page = 1, limit = 10, sort } = params;
            if (params.title) {
                filter.title = { $regex: new RegExp(params.title), $options: "i" };
            }
            if (params.ordering) {
                filter.ordering = params.ordering;
            }
            if (params.type) {
                filter.type = params.type;
            }
            if (params.keyword) {
                filter.$or = [
                    { title: { $regex: new RegExp(params.keyword), $options: "i" } },
                    { storeCode: { $regex: new RegExp(params.keyword), $options: "i" } },
                ];
            }
            const options = {
                page: Number(page),
                limit: Number(limit),
                forceCountFn: true,
                sort: sort || "-ordering",
            };
            const result = yield this.playlistModel.paginate(filter, options);
            return result;
        });
    }
    update(id, params) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const playlist = yield this.playlistModel.findOneAndUpdate({ _id: id }, params, {
                new: true,
            });
            if (!playlist) {
                throw new common_1.HttpException("Update data failed.", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            yield this.flushAllCache();
            return playlist;
        });
    }
    remove(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!mongoose_2.default.Types.ObjectId.isValid(id)) {
                throw new common_1.HttpException("Provided ID is not valid.", common_1.HttpStatus.BAD_REQUEST);
            }
            const playlist = yield this.playlistModel.findByIdAndDelete(id);
            if (!playlist) {
                throw new common_1.HttpException("Playlist not found.", common_1.HttpStatus.NOT_FOUND);
            }
            yield this.flushAllCache();
            return { message: "Playlist deleted successfully" };
        });
    }
    getActivePlaylists(storeCode) {
        return __awaiter(this, void 0, void 0, function* () {
            const cacheKey = !storeCode ? "playlist-all" : `playlist-${storeCode}`;
            const ids = yield this._findStoreGroupIds(storeCode);
            const cachedPlaylists = yield this.redisService.get(cacheKey);
            if (cachedPlaylists) {
                return JSON.parse(cachedPlaylists);
            }
            const currentDate = new Date();
            const filter = {
                startDate: { $lte: currentDate },
                endDate: { $gte: currentDate },
                $or: [{ store_group: { $exists: false } }, { store_group: { $size: 0 } }, { store_group: { $in: ids } }],
            };
            if (storeCode) {
                filter.storeCode = storeCode;
            }
            const playlists = yield this.playlistModel.find(filter).sort({ ordering: -1 });
            if (playlists.length > 0) {
                yield this.redisService.set(cacheKey, JSON.stringify(playlists), "EX", process.env.REDIS_PLAYLIST_CACHED_LIFETIME || 3600);
            }
            else {
                yield this.redisService.del(cacheKey);
            }
            return playlists;
        });
    }
    flushAllCache() {
        return __awaiter(this, void 0, void 0, function* () {
            const keys = yield this.redisService.keys("playlist-*");
            if (keys.length > 0) {
                yield this.redisService.del(...keys);
            }
            return true;
        });
    }
    _findStoreGroupIds(storeCode) {
        return __awaiter(this, void 0, void 0, function* () {
            const ids = [];
            if (storeCode) {
                const storeGroups = yield this.storeGroupService.getStoreGroups(storeCode);
                if (!storeGroups.length) {
                    return ids;
                }
                storeGroups.map((sg) => {
                    ids.push(sg._id);
                });
            }
            return ids;
        });
    }
};
PlaylistService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Playlist")),
    __param(1, (0, nestjs_redis_1.InjectRedis)()),
    __metadata("design:paramtypes", [typeof (_a = typeof mongoose_2.PaginateModel !== "undefined" && mongoose_2.PaginateModel) === "function" ? _a : Object, typeof (_b = typeof ioredis_1.default !== "undefined" && ioredis_1.default) === "function" ? _b : Object, store_group_service_1.StoreGroupService])
], PlaylistService);
exports.PlaylistService = PlaylistService;
//# sourceMappingURL=playlist.service.js.map