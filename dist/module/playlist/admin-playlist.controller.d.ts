import { CreatePlaylistDto } from "./dto/create-playlist.dto";
import { GetPlaylistDto } from "./dto/get-playlist.dto";
import { PlaylistService } from "./playlist.service";
export declare class AdminPlaylistController {
    private readonly playlistService;
    constructor(playlistService: PlaylistService);
    createPlaylist(createPlaylistDto: CreatePlaylistDto): Promise<any>;
    findOne(id: string): Promise<any>;
    find(pagination: GetPlaylistDto): Promise<any>;
    update(id: string, updatePlaylistDto: CreatePlaylistDto): Promise<any>;
    remove(id: string): Promise<{
        message: string;
    }>;
    refresh(): Promise<boolean>;
}
