"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsImageUrlRegistered = exports.IsImageUrlRegisteredConstraint = void 0;
const class_validator_1 = require("class-validator");
let IsImageUrlRegisteredConstraint = class IsImageUrlRegisteredConstraint {
    validate(imageUrl, args) {
        const imageBaseUrls = [
            process.env.IMAGE_BASE_URL,
            process.env.IMAGE_BASE_URL_POS,
            process.env.AWS_S3_BASE_URL,
            ...process.env.CUSTOM_IMAGE_URL.split(","),
        ];
        return imageBaseUrls.some((url) => imageUrl.includes(url));
    }
};
IsImageUrlRegisteredConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)()
], IsImageUrlRegisteredConstraint);
exports.IsImageUrlRegisteredConstraint = IsImageUrlRegisteredConstraint;
function IsImageUrlRegistered(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsImageUrlRegisteredConstraint,
        });
    };
}
exports.IsImageUrlRegistered = IsImageUrlRegistered;
//# sourceMappingURL=image-url-validator.js.map