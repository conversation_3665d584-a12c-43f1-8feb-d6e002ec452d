"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidDate = exports.ValidDateConstraint = void 0;
const class_validator_1 = require("class-validator");
let ValidDateConstraint = class ValidDateConstraint {
    validate(date, args) {
        return /^(1[0-9]{3}|2[0-9]{3})-(0[0-9]|1[0-2])-([0-2][0-9]|3[0-1])$/.test(date);
    }
    defaultMessage(args) {
        return `${args.property} must be in valid YYYY-MM-DD format`;
    }
};
ValidDateConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)()
], ValidDateConstraint);
exports.ValidDateConstraint = ValidDateConstraint;
function ValidDate(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: ValidDateConstraint,
        });
    };
}
exports.ValidDate = ValidDate;
//# sourceMappingURL=date-validator.dto.js.map