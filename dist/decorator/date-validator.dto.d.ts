import { ValidationOptions, ValidatorConstraintInterface, ValidationArguments } from "class-validator";
export declare class ValidDateConstraint implements ValidatorConstraintInterface {
    validate(date: string, args: ValidationArguments): boolean;
    defaultMessage(args: ValidationArguments): string;
}
export declare function ValidDate(validationOptions?: ValidationOptions): (object: Object, propertyName: string) => void;
