"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransformArray = void 0;
const class_transformer_1 = require("class-transformer");
function TransformArray() {
    return (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value !== "object") {
            return [value];
        }
        return value;
    });
}
exports.TransformArray = TransformArray;
//# sourceMappingURL=transform-array.decorator.js.map