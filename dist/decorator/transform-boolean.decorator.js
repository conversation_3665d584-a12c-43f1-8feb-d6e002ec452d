"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransformBoolean = void 0;
const class_transformer_1 = require("class-transformer");
function TransformBoolean() {
    return (0, class_transformer_1.Transform)(({ value }) => {
        if (value === "true")
            return true;
        if (value === "false")
            return false;
        if (value === true)
            return true;
        if (value === false)
            return false;
        return undefined;
    });
}
exports.TransformBoolean = TransformBoolean;
//# sourceMappingURL=transform-boolean.decorator.js.map