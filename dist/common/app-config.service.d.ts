import { ConfigService } from "@nestjs/config";
export declare class AppConfigService {
    private readonly configService;
    private readonly _mongoConnectionString;
    constructor(configService: ConfigService);
    get connectionString(): any;
    get sqlServerConfig(): {
        host: string;
        port: number;
        username: string;
        password: string;
        database: string;
    };
    private _getConnectionStringFromEnvFile;
    private _getConfigAws;
}
