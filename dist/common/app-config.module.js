"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppConfigModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const Joi = require("joi");
const app_config_service_1 = require("./app-config.service");
let AppConfigModule = class AppConfigModule {
};
AppConfigModule = __decorate([
    (0, common_1.Module)({
        exports: [app_config_service_1.AppConfigService],
        imports: [
            config_1.ConfigModule.forRoot({
                envFilePath: `${process.env.NODE_ENV ? process.env.NODE_ENV : ""}.env`,
                validationSchema: Joi.object({
                    APPS_PORT: Joi.number().required().default(3000),
                    MONGO_URL: Joi.string().required(),
                    SQL_SERVER_HOST: Joi.string().required(),
                    SQL_SERVER_PORT: Joi.number().default(1433),
                    SQL_SERVER_USERNAME: Joi.string().required(),
                    SQL_SERVER_PASSWORD: Joi.string().required(),
                    SQL_SERVER_DATABASE: Joi.string().required(),
                    UTILS_UPLOAD_FOLDER: Joi.string().required(),
                    KAFKA_NAME: Joi.string().required(),
                    KAFKA_CLIENT_ID: Joi.string().required(),
                    KAFKA_HOST: Joi.string().required(),
                    KAFKA_GROUP_ID: Joi.string().required(),
                    FIREBASE_CLIENT_EMAIL: Joi.string().required(),
                    FIREBASE_PRIVATE_KEY: Joi.string().required(),
                    FIREBASE_PROJECT_ID: Joi.string().required(),
                    KAFKA_TOPIC_PREFIX: Joi.string().required(),
                    REDIS_HOST: Joi.string().required(),
                    REDIS_PORT: Joi.number().required(),
                    REDIS_FCM_DB: Joi.number().required(),
                }),
            }),
        ],
        providers: [app_config_service_1.AppConfigService],
    })
], AppConfigModule);
exports.AppConfigModule = AppConfigModule;
//# sourceMappingURL=app-config.module.js.map