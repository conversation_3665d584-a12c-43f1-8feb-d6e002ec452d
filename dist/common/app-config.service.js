"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppConfigService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let AppConfigService = class AppConfigService {
    constructor(configService) {
        this.configService = configService;
        this._mongoConnectionString = this._getConnectionStringFromEnvFile();
    }
    get connectionString() {
        return this._mongoConnectionString;
    }
    get sqlServerConfig() {
        return {
            host: this.configService.get("SQL_SERVER_HOST"),
            port: this.configService.get("SQL_SERVER_PORT") || 1433,
            username: this.configService.get("SQL_SERVER_USERNAME"),
            password: this.configService.get("SQL_SERVER_PASSWORD"),
            database: this.configService.get("SQL_SERVER_DATABASE"),
        };
    }
    _getConnectionStringFromEnvFile() {
        return __awaiter(this, void 0, void 0, function* () {
            const mongoUrl = this.configService.get("MONGO_URL");
            const connectionString = `mongodb://${mongoUrl}`;
            if (!connectionString) {
                throw new Error("No connection string has been provided in the .env file.");
            }
            return connectionString;
        });
    }
    _getConfigAws() {
        const aws = {
            bucket: this.configService.get("AWS_S3_BUCKET"),
            accessKey: this.configService.get("AWS_ACCESS_KEY"),
            secretKey: this.configService.get("AWS_SECRET_KEY"),
        };
        return aws;
    }
};
AppConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AppConfigService);
exports.AppConfigService = AppConfigService;
//# sourceMappingURL=app-config.service.js.map