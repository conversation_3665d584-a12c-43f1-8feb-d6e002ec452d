"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AmazonS3Services = void 0;
const common_1 = require("@nestjs/common");
const AWS = require("aws-sdk");
const crypto_1 = require("crypto");
let AmazonS3Services = class AmazonS3Services {
    constructor() {
        this.AWS_S3_BUCKET = process.env.AWS_S3_BUCKET;
        this.s3 = new AWS.S3({
            accessKeyId: process.env.AWS_ACCESS_KEY,
            secretAccessKey: process.env.AWS_SECRET_KEY,
            region: process.env.AWS_REGION,
        });
    }
    uploadFile(file, ext, prefix, mimetype) {
        return __awaiter(this, void 0, void 0, function* () {
            prefix = process.env.NODE_ENV == "testing" ? prefix + "-test" : prefix;
            const payload = {
                Body: file,
                Bucket: this.AWS_S3_BUCKET,
                Key: prefix + (0, crypto_1.randomUUID)() + ext,
                ACL: "public-read",
                ContentType: mimetype,
            };
            try {
                const result = yield this.s3.upload(payload).promise();
                result.Location = result.Location.replace(process.env.AWS_S3_BASE_URL_OLD, process.env.AWS_S3_BASE_URL);
                return result;
            }
            catch (error) {
                console.log(error);
                throw new common_1.HttpException("Failed to upload", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        });
    }
};
AmazonS3Services = __decorate([
    (0, common_1.Injectable)()
], AmazonS3Services);
exports.AmazonS3Services = AmazonS3Services;
//# sourceMappingURL=amazons3.service.js.map