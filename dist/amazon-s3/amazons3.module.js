"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Amazons3Module = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const app_config_service_1 = require("../common/app-config.service");
const amazons3_service_1 = require("./amazons3.service");
let Amazons3Module = class Amazons3Module {
};
Amazons3Module = __decorate([
    (0, common_1.Module)({
        imports: [],
        controllers: [],
        providers: [app_config_service_1.AppConfigService, amazons3_service_1.AmazonS3Services, config_1.ConfigService],
        exports: [amazons3_service_1.AmazonS3Services],
    })
], Amazons3Module);
exports.Amazons3Module = Amazons3Module;
//# sourceMappingURL=amazons3.module.js.map