"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const event_emitter_1 = require("@nestjs/event-emitter");
const mongoose_1 = require("@nestjs/mongoose");
const schedule_1 = require("@nestjs/schedule");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const nestjs_elastic_apm_1 = require("../modules/nestjs-elastic-apm");
const amazons3_module_1 = require("./amazon-s3/amazons3.module");
const app_config_module_1 = require("./common/app-config.module");
const app_config_service_1 = require("./common/app-config.service");
const cache_manager_module_1 = require("./module/cache-manager/cache-manager.module");
const health_check_module_1 = require("./module/health-check/health-check.module");
let AppModule = class AppModule {
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            keycloak_connect_tbs_1.KeycloakConnectModule.register({
                authServerUrl: process.env.KEYCLOAK_HOST,
                clientId: process.env.KEYCLOAK_CLIENTID,
                secret: process.env.KEYCLOAK_SECRET,
                realm: process.env.KEYCLOAK_REALM,
                policyEnforcement: keycloak_connect_tbs_1.PolicyEnforcementMode.PERMISSIVE,
                tokenValidation: keycloak_connect_tbs_1.TokenValidation.ONLINE,
                internalUrls: process.env.INTERNAL_ACCESS_URL.split(","),
                app_port: +process.env.APPS_PORT,
                bypass_iss_check: Boolean(process.env.KEYCLOAK_BYPASS_ISS),
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [app_config_module_1.AppConfigModule],
                inject: [app_config_service_1.AppConfigService],
                useFactory: (appConfigService) => __awaiter(void 0, void 0, void 0, function* () {
                    const uri = yield appConfigService.connectionString;
                    const options = {
                        uri: uri,
                        dbName: "tbs_db_utils",
                        useNewUrlParser: true,
                        useUnifiedTopology: true,
                    };
                    return options;
                }),
            }),
            app_config_module_1.AppConfigModule,
            amazons3_module_1.Amazons3Module,
            health_check_module_1.HealthCheckModule,
            nestjs_elastic_apm_1.ApmModule.register(),
            cache_manager_module_1.CacheManagerModule,
            event_emitter_1.EventEmitterModule.forRoot(),
            schedule_1.ScheduleModule.forRoot(),
        ],
        providers: [
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.InternalAccessGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.AuthGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.ResourceGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.RoleGuard,
            },
        ],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map