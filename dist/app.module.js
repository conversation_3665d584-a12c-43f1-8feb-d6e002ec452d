"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const event_emitter_1 = require("@nestjs/event-emitter");
const mongoose_1 = require("@nestjs/mongoose");
const schedule_1 = require("@nestjs/schedule");
const keycloak_connect_tbs_1 = require("keycloak-connect-tbs");
const nestjs_elastic_apm_1 = require("../modules/nestjs-elastic-apm");
const tbs_site_config_1 = require("tbs-site-config");
const amazons3_module_1 = require("./amazon-s3/amazons3.module");
const app_config_module_1 = require("./common/app-config.module");
const app_config_service_1 = require("./common/app-config.service");
const banner_footer_module_1 = require("./module/banner-footer/banner-footer.module");
const blog_module_1 = require("./module/blog/blog.module");
const builderio_module_1 = require("./module/builderio/builderio.module");
const cache_manager_module_1 = require("./module/cache-manager/cache-manager.module");
const carbon_module_1 = require("./module/carbon/carbon.module");
const carousel_module_1 = require("./module/carousel/carousel.module");
const crm_module_1 = require("./module/crm/crm.module");
const email_notif_module_1 = require("./module/email-notif/email-notif.module");
const file_uploader_module_1 = require("./module/file/file-uploader.module");
const health_check_module_1 = require("./module/health-check/health-check.module");
const home_module_1 = require("./module/home/<USER>");
const keycloak_generator_module_1 = require("./module/keycloak-generator/keycloak-generator.module");
const kibana_dashboard_module_1 = require("./module/kibana-dashboard/kibana-dashboard.module");
const mega_menu_module_1 = require("./module/mega-menu/mega-menu.module");
const newsletter_module_1 = require("./module/newsletter/newsletter.module");
const offers_module_1 = require("./module/offers/offers.module");
const playlist_module_1 = require("./module/playlist/playlist.module");
const fcm_publisher_module_1 = require("./module/push-notif/fcm-publisher/fcm-publisher.module");
const fcm_user_mapper_module_1 = require("./module/push-notif/fcm-user-mapper/fcm-user-mapper.module");
const inbox_module_1 = require("./module/push-notif/inbox/inbox.module");
const notif_management_module_1 = require("./module/push-notif/notif-management/notif-management.module");
const push_notif_sender_module_1 = require("./module/push-notif/push-notif-sender/push-notif-sender.module");
const screen_module_1 = require("./module/screen/screen.module");
const site_config_schema_1 = require("./module/site-configs/schema/site-config.schema");
const site_configs_module_1 = require("./module/site-configs/site-configs.module");
const store_group_module_1 = require("./module/store-group/store-group.module");
const store_module_1 = require("./module/store/store.module");
const unsubscribe_module_1 = require("./module/unsubscribe/unsubscribe.module");
const url_shortener_module_1 = require("./module/url-shortener/url-shortener.module");
const in_store_service_module_1 = require("./module/in-store-service/in-store-service.module");
let AppModule = class AppModule {
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            keycloak_connect_tbs_1.KeycloakConnectModule.register({
                authServerUrl: process.env.KEYCLOAK_HOST,
                clientId: process.env.KEYCLOAK_CLIENTID,
                secret: process.env.KEYCLOAK_SECRET,
                realm: process.env.KEYCLOAK_REALM,
                policyEnforcement: keycloak_connect_tbs_1.PolicyEnforcementMode.PERMISSIVE,
                tokenValidation: keycloak_connect_tbs_1.TokenValidation.ONLINE,
                internalUrls: process.env.INTERNAL_ACCESS_URL.split(","),
                app_port: +process.env.APPS_PORT,
                bypass_iss_check: Boolean(process.env.KEYCLOAK_BYPASS_ISS),
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [app_config_module_1.AppConfigModule],
                inject: [app_config_service_1.AppConfigService],
                useFactory: (appConfigService) => __awaiter(void 0, void 0, void 0, function* () {
                    const uri = yield appConfigService.connectionString;
                    const options = {
                        uri: uri,
                        dbName: "tbs_db_utils",
                        useNewUrlParser: true,
                        useUnifiedTopology: true,
                    };
                    return options;
                }),
            }),
            tbs_site_config_1.TbsSiteConfigModule.registerAsync({
                options: { host: process.env.REDIS_HOST, port: +process.env.REDIS_PORT, db: +process.env.REDIS_CONFIG_DB },
                prefix: process.env.KAFKA_TOPIC_PREFIX,
                isMaster: true,
                mongoDbUrl: process.env.MONGO_URL,
                mongoDbName: "tbs_db_utils",
                mongoDbCollection: site_config_schema_1.SiteConfig.name.toLowerCase() + "s",
            }),
            app_config_module_1.AppConfigModule,
            store_module_1.StoreModule,
            file_uploader_module_1.FileUploaderModule,
            amazons3_module_1.Amazons3Module,
            site_configs_module_1.SiteConfigsModule,
            carbon_module_1.CarbonModule,
            carousel_module_1.CarouselModule,
            offers_module_1.OffersModule,
            home_module_1.HomeModule,
            newsletter_module_1.NewsletterModule,
            blog_module_1.BlogModule,
            health_check_module_1.HealthCheckModule,
            keycloak_generator_module_1.KeycloakGeneratorModule,
            nestjs_elastic_apm_1.ApmModule.register(),
            mega_menu_module_1.MegaMenuModule,
            screen_module_1.ScreenModule,
            url_shortener_module_1.UrlShortenerModule,
            builderio_module_1.BuilderIoModule,
            push_notif_sender_module_1.PushNotifSenderModule,
            kibana_dashboard_module_1.KibanaDashboardModule,
            unsubscribe_module_1.UnsubscribeModule,
            cache_manager_module_1.CacheManagerModule,
            banner_footer_module_1.BannerFooterModule,
            fcm_publisher_module_1.FcmPublisherModule,
            fcm_user_mapper_module_1.FcmUserMapperModule,
            inbox_module_1.InboxModule,
            event_emitter_1.EventEmitterModule.forRoot(),
            crm_module_1.CrmModule,
            schedule_1.ScheduleModule.forRoot(),
            notif_management_module_1.NotifManagementModule,
            email_notif_module_1.EmailNotifModule,
            store_group_module_1.StoreGroupModule,
            in_store_service_module_1.InStoreServiceModule,
            playlist_module_1.PlaylistModule,
        ],
        providers: [
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.InternalAccessGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.AuthGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.ResourceGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: keycloak_connect_tbs_1.RoleGuard,
            },
        ],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map