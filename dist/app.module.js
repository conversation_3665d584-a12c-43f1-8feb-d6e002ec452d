"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const typeorm_1 = require("@nestjs/typeorm");
const app_config_module_1 = require("./common/app-config.module");
const app_config_service_1 = require("./common/app-config.service");
const analytics_module_1 = require("./module/analytics/analytics.module");
const health_check_module_1 = require("./module/health-check/health-check.module");
let AppModule = class AppModule {
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [app_config_module_1.AppConfigModule],
                inject: [app_config_service_1.AppConfigService],
                useFactory: (appConfigService) => __awaiter(void 0, void 0, void 0, function* () {
                    const uri = yield appConfigService.connectionString;
                    const options = {
                        uri: uri,
                        dbName: "starter_db",
                        useNewUrlParser: true,
                        useUnifiedTopology: true,
                    };
                    return options;
                }),
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [app_config_module_1.AppConfigModule],
                inject: [app_config_service_1.AppConfigService],
                useFactory: (appConfigService) => {
                    const sqlConfig = appConfigService.sqlServerConfig;
                    return {
                        type: "mssql",
                        host: sqlConfig.host,
                        port: sqlConfig.port,
                        username: sqlConfig.username,
                        password: sqlConfig.password,
                        database: sqlConfig.database,
                        entities: [__dirname + "/**/*.entity{.ts,.js}"],
                        synchronize: false,
                        logging: true,
                        options: {
                            encrypt: false,
                            trustServerCertificate: true,
                        },
                    };
                },
            }),
            app_config_module_1.AppConfigModule,
            analytics_module_1.AnalyticsModule,
            health_check_module_1.HealthCheckModule,
        ],
        providers: [],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map