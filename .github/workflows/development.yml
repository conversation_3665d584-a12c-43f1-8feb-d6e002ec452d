name: Development

on:
  pull_request:
    branches: ["development"]
jobs:
  testing:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.*, 14.*]
    steps:
      - name: "Git Checkout"
        uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
      - run: npm clean-install
      - run: npm run lint
      - run: npm run test
      - run: npm run test:e2e
