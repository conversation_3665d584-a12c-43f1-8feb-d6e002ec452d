name: Deployment UAT

on:
  pull_request:
    branches: ["deployment-uat" ]
jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm clean-install
    - run: npm run lint
    - run: npm test
