{"name": "be-boilerplate-v2", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:local": "cross-env NODE_ENV=local nest start --watch", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --no-fix --max-warnings 0", "test": "cross-env NODE_ENV=testing jest", "test:watch": "cross-env NODE_ENV=testing jest --watch", "test:cov": "cross-env NODE_ENV=testing jest --coverage", "test:debug": "cross-env NODE_ENV=testing node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cross-env NODE_ENV=testing jest --config ./test/jest-e2e.json --forceExit", "prepare": "husky install", "deploy": "./deploy.sh utils 1.0.0"}, "dependencies": {"@elastic/elasticsearch": "^8.8.0", "@liaoliaots/nestjs-redis": "^9.0.5", "@nestjs/axios": "^1.0.1", "@nestjs/common": "^9.0.8", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.8", "@nestjs/elasticsearch": "^9.0.0", "@nestjs/event-emitter": "^1.4.2", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^9.0.8", "@nestjs/mongoose": "^9.2.0", "@nestjs/platform-express": "^9.0.8", "@nestjs/schedule": "^3.0.2", "@nestjs/swagger": "^6.0.5", "@nestjs/terminus": "^9.1.0", "aws-sdk": "^2.1184.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cross-env": "^7.0.3", "dayjs": "^1.11.6", "dotenv": "^16.0.1", "elastic-apm-node": "^3.40.1", "firebase-admin": "^11.6.0", "global-body-validator-tbs": "^0.0.10", "ioredis": "^5.3.2", "joi": "^17.6.0", "jwt-decode": "^3.1.2", "kafkajs": "^2.2.3", "keycloak-connect-tbs": "^1.0.17", "mongoose": "^6.4.0", "mongoose-paginate-v2": "^1.7.0", "nestjs-mongoose-crud": "^2.1.2", "read-excel-file": "^5.6.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "slugify": "^1.6.6", "swagger-ui-express": "^4.4.0", "tbs-site-config": "^0.0.8", "write-excel-file": "^1.4.27"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.1", "@nestjs/testing": "^9.0.8", "@types/express": "^4.17.13", "@types/jest": "27.5.0", "@types/multer": "^1.4.7", "@types/node": "^16.11.41", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.1", "jest": "28.0.3", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}