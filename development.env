APPS_PORT=3001
NODE_ENV=development
MONGO_HOST=mongo.development.tbsgroup.co.id

# SQL Server Configuration for Analytics
SQL_SERVER_HOST=************
SQL_SERVER_PORT=1433
SQL_SERVER_USERNAME=rodbusr01
SQL_SERVER_PASSWORD=roDBusr01!
SQL_SERVER_DATABASE=AnalyticsDataCenter
#SQL Server Config Ends
ELASTICSEARCH_URL=https://es.development.tbsgroup.co.id:9200
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASSWORD=Z7+cJKMglNzsJsM4wbLM
ELASTIC_POS_ALIAS=pos_product
ELASTIC_APM_ACTIVATE=true
ELASTIC_APM_LOG_LEVEL=info
ELASTIC_APM_CAPTURE_BODY=all
ELASTIC_APM_SECRET_TOKEN=YWM956E3sbOevte7Yj
ELASTIC_APM_API_KEY=VjBNUkVZVUJEcUEzX053Y2VYeGc6MFF0c1A1WHpUUU9ValhjemItVW1nQQ==
ELASTIC_APM_SERVER_URL=https://icarus-sandbox.apm.asia-southeast1.gcp.elastic-cloud.com
KEYCLOAK_HOST=https://keycloak.sit.tbsgroup.co.id
KEYCLOAK_REALM=tbs-icarus
KEYCLOAK_CLIENTID=tbs-app
KEYCLOAK_SECRET=********************************
KEYCLOAK_ADMIN_USER=admin-temp
KEYCLOAK_ADMIN_PASS=TBSDevops333!
KEYCLOAK_ADMIN_CLIENTID=admin-cli
KEYCLOAK_ADMIN_SECRET=********************************
KEYCLOAK_SET_DEFAULT_ROLE=true
KEYCLOAK_BYPASS_ISS=true
KEYCLOAK_WRAPPER_HOST=https://sit-keycloak-wrapper.tbsgroup.co.id
KEYCLOAK_PREDEFINE_POLICY=Super Admin,Store Staff,Customer,Employee,Store Manager
EMPLOYEE_DEFAULT_PIN=1234
DB_HOST=postgre.development.tbsgroup.co.id
DB_PORT=5432
DB_USER=postgres
DB_PASS=pMwGGQsKCL224Fp3b3cIy3nl5t9xQQM6
DB_NAME=tbs_keycloak
TWILIO_BASE_URL=https://conversations.twilio.com/v1
TWILIO_SID=**********************************
TWILIO_AUTH_TOKEN=c0702e5bc94edded267fc22c3b4d55d5
TWILIO_WHATSAPP_FROM=+14155238886
FACEBOOK_BASE_URL=
FACEBOOK_TOKEN_VERIFICATION=tB5_13CCusT0m3r!
FACEBOOK_PAGE_ACCESS_TOKEN=EAAPUqkBaARABAEgZBezzy0DT69I7B5paumFo5vztcfu3KBmGpnBtDlQ4LQGmEE3WHxXA3gL9MgntjGw06FZCGZAxDXmLKyEZBb6X8ZACGDQ0SrY1AHR1k27JR8q0muX56Bun4uWuUb2L725epLwvBaQtqWEermkUMbZBKzVhOriTtmTsvQMatp
FACEBOOK_PAGE_ID=110117625215543
FACEBOOK_APP_ID=1078252619301136
FACEBOOK_APP_SECRET=********************************
INSTAGRAM_TOKEN_VERIFICATION=tB5_1g_13CCusT0m3r!
INSTAGRAM_PAGE_ACCESS_TOKEN=EAAPUqkBaARABADEpTrY3EHibYZBaCXxHx7LEQU57hplofOZC2m4BHIFTYSdYuPxDZCYLLS5jBs0xk1pT7s3A6z2b2lMhZCxhIy90ha8UY3hTPtls7mnILU9FkZBbt5ZCDwin0zSMe1dTZAEFPjvnCZBSbpBk0VvU6yDPPPgE1Q2mnZC8WwuhPR5VB
TWITTER_BASE_URL=https://api.twitter.com
TWITTER_CONSUMER_KEY=*************************
TWITTER_CONSUMER_SECRET=2EHLhFSX3z2KHO1GRRI8VqWyVR5bPsachwthvkNLFPIWb2FHRv
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAOeZiAEAAAAAT9M1B6QO%2FYCRUe5bUtoY3LTt3kU%3DhHMszW5sFjO58xGpKZrT1s6ts20ebw7WEjtPLNgg0bP5Mdc2i8
TWITTER_CLIENT_ID=Q3lkVDljTG9oblpiRU8xdkZ2TTM6MTpjaQ
TWITTER_CLIENT_SECRET=MjTETFTAhyY9RSOdjNPFVLDwVqjte_2pSGkDIHhYDhUJsphJPA
TWITTER_APP_OWNER_ACCESS_TOKEN=*********-Ft8wET6WS6OIBrJ9PqA2JB4iGSAwZPMDXCqNYUDq
TWITTER_APP_OWNER_ACCESS_TOKEN_SECRET=fiidstO1NavAb1K5wHL5i9krT8DXdbtz24mQVC3ZHVUxq
TWITTER_USER_ID=********
FIREBASE_SERVICE_ACCOUNT_JSON=/beautyconcierge-b2bab-firebase-adminsdk-no4jt-596f1b3584.json
MEDIA_MAX_SIZE=1024
CASH_VOUCHER_IMAGE_MAX_SIZE=500
STICKER_IMAGE_MAX_SIZE=100
CART_RULE_IMAGE_MAX_SIZE=500
IMAGE_BASE_URL=https://www.thebodyshop.co.id/media/catalog/product
IMAGE_BASE_URL_POS=https://icarus-s3.s3.ap-southeast-3.amazonaws.com/products
CUSTOM_IMAGE_URL=https://media-mobileappsdev.tbsgroup.co.id,
VMS_URL=http://************
FORSTOK_INTEGRATION_URL=https://integration.forstok.com/api
FORSTOK_ACCOUNTS_URL=https://accounts.forstok.com/api
FORSTOK_ACCOUNT=<EMAIL>
FORSTOK_PASSWORD=Forstok1234
FORSTOK_SECRET=1407ca941a78431e54dc4c9e06407723
MIDTRANS_HOST=https://api.sandbox.midtrans.com
MIDTRANS_SNAP_HOST=https://app.sandbox.midtrans.com
MIDTRANS_SERVER_KEY=SB-Mid-server-VlrnHpiUWjVx0mj_nFWljE0I
MIDTRANS_CLIENT_KEY=SB-Mid-client-60RxTO05SDL9eui7
MIDTRANS_IS_PRODUCTION=false
MIDTRANS_IP_WHITELIST=*************,************
OVO_HOST=https://api.byte-stack.net
OVO_APP_ID=stagingkey
OVO_KEY=46d6e839771a3ab01779dfd2ab7b1ec2a8397cd92d981ae5620d1580dfbb9084
OVO_MERCHANT_NAME=Body Shop Online
OVO_MERCHANT_ID=116449
OVO_STORE_CODE=WEBSTORE01
OVO_STORE_NAME=WEB STORE
OVO_MID=WEBSTORE2101146
OVO_TID=********
OVO_PROCESSING_CODE=040000
OVO_APP_SOURCE=POS
OVO_PROVIDER_TIMEOUT=15
USERS_HOST="http://sit-users.tbsgroup.co.id"
OMS_HOST="http://sit-products.tbsgroup.co.id"
UTILS_HOST="http://sit-utils.tbsgroup.co.id"
USERS_SERVICE_URL="http://sit-users.tbsgroup.co.id"
SHIPPING_SERVICE_URL="http://localhost:3002"
PAYMENT_SERVICE_URL="https://sit-payments.tbsgroup.co.id"
UTIL_SERVICE_URL="http://sit-utils.tbsgroup.co.id"
ENGAGE_HOST=http://************:8080
ENGAGE_ACCT_HOST=http://************:8080
ENGAGE_ACCT_USER=ecomm
ENGAGE_ACCT_PASSWORD=4e-3-77-7-6d-37-35-35-2a-51-54-54-76-b-67-6e-5b-32-2a-28
ENGAGE_COMPANY_ID=0
ENGAGE_SITECODE_WEBSITE=34999
ENGAGE_SITECODE_APPS=34997
ERECEIPT_HOST=https://ms-api.thebodyshop.co.id/mid/notification/v1
ERECEIPT_API_KEY=pB1gYwRTTD2HP5BjeKSXUa2eDVYF6wuJ7OpY6X1a
POS_MAX_PER_SKU=10
POS_MAX_SKU_PRE_TRX=50
POS_MAX_AMOUNT=********
SICEPAT_PICKUP_HOST=http://pickup.sicepat.com:8087
SICEPAT_PICKUP_KEY=85CF1BCB627B4E5A88E1142557AC5D35
SICEPAT_PICKUP_ACCOUNT_CODE=TBS
SICEPAT_TRACKING_HOST=https://apitrek.sicepat.com
SICEPAT_TRACKING_KEY=605a21788bdb7457da331a77967a7561
GOSEND_HOST=https://integration-kilat-api.gojekapi.com/gokilat/v10
GOSEND_CLIENTID=the-body-shop-engine
GOSEND_PASSKEY=7a769c1c659015395f7b3bb2da45cdd141e33e6d7b82ee48149b905cc05520c8
GOSEND_PAYMENT_TYPE=3
JNE_HOST=http://apiv2.jne.co.id:10102
JNE_API_KEY=25c898a9faea1a100859ecd9ef674548
JNE_USERNAME=TESTAPI
JNE_OLSHOP_BRANCH=CGK000
JNE_OLSHOP_CUST=TESTAKUN
JNE_OLSHOP_INST=123
JNE_OLSHOP_INS_FLAG=N
SAP_HOST=http://apisanbox.coresyssap.com
SAP_API_KEY=bdshop2018
SAP_COMPANY_CODE=TGRN00065
MAGENTO_ENCRYPTION_HOST=https://codina.io
OTP_HOST=https://api.thebodyshop.co.id/prod
OTP_API_KEY=pB1gYwRTTD2HP5BjeKSXUa2eDVYF6wuJ7OpY6X1a
OTP_PREFIX_BY_PASS=testerauto
OTP_PREFIX_PHONE_BY_PASS=628000
APIGW_HOST=https://api.tbsgroup.co.id/api-gw-2
APIGW_API_KEY=fLuV3EtTnr8H9mJIFj6Qe9aVj4PeCqvV4LgcDKCO
ADMIN_USER=<EMAIL>
ADMIN_PASS=Password123
EMPLOYEE_DEFAULT_PIN=1234
SOYA_HOST=http://*************
SOYA_API_KEY=ffDcV1SrLwQwlaRwxEQnq8NxFx4ZZEArRPm4lid8pGwDbzYl3Kk7FZdkafxHVBa2bJHadRfgOHTz9qmk9zOwNxIwYKUEYeM8H8jFuxNPHaqKgf5TZwAfWPWBRUgPSHt1
AWS_S3_BUCKET=icarus-s3
AWS_ACCESS_KEY=********************
AWS_SECRET_KEY=zXG6TUlCSJ0laAVDu/xpiw3wSD9pBTuBbe4R3wOY
AWS_REGION=ap-southeast-3
AWS_S3_BASE_URL=https://icarus-s3.s3.ap-southeast-3.amazonaws.com
CORS_METHOD=GET,PUT,PATCH,POST,DELETE
CORS_DOMAIN=https://sit-cms-pos.tbsgroup.co.id,https://sit-web.tbsgroup.co.id,http://localhost:3000,http://localhost:3001,,http://localhost:3002,https://sit-users.tbsgroup.co.id,https://sit-products.tbsgroup.co.id,https://sit-payments.tbsgroup.co.id,https://sit-utils.tbsgrou
p.co.id,https://sit-marketplaces.tbsgroup.co.id,https://sit-shippings.tbsgroup.co.id
INTERNAL_ACCESS_URL=localhost,be-api-users,be-api-products,be-api-payments,be-api-utils,be-api-marketplaces,be-api-shippings,ap-southeast-3.compute.internal
USERS_PASSWORD_SALT=$2b$10$4p5XBoM9cBgY/x.Qz0hjTO
CRON_ORDER_LIMIT=30
CRON_ORDER_INTERVAL_VALUE=1
CRON_ORDER_INTERVAL_UNIT=hour#fill only with minute, hour, day
GOLD_HOST=http://*************:8081
GOLD_USER=aldata
GOLD_PASS=RVlD
GOLD_CHANNEL_ID=34999
GOLD_DB_HOST=*************
GOLD_DB_PORT=1521
GOLD_DB_NAME=GCENDB
GOLD_DB_USER=tbsprod
GOLD_DB_PASS=tbsprod
MONGO_URL=tbsi-developer:<EMAIL>:27017/?authMechanism=SCRAM-SHA-256&authSource=admin
MONGO_DATABASE=tbs_db_utils
ELASTIC_APM_SERVICE_NAME=be-api-products
GREETING_CARD_FAMILY_CODE=50806
REDIS_HOST=redis.staging-v1.tbsgroup.co.id
REDIS_PORT=6379
REDIS_HOME_CACHED_LIFETIME=3600
REDIS_PORT_PUBLIC=6380
REDIS_CONFIG_DB=0
REDIS_STOCK_COST=8
REDIS_FCM_DB=7
REDIS_FCM_PORT=6379
REDIS_RTS_FORSTOK_DB=0
REDIS_RTS_FORSTOK_PORT=6379
REDIS_MP_DB=1
CRON_AUTH_USER=admin-tbs
CRON_AUTH_PASS=TBSIcms@Desember2022
FORSTOK_INTEGRATION_URL=https://integration.forstok.com/api
FORSTOK_ACCOUNTS_URL=https://accounts.forstok.com/api
FORSTOK_ACCOUNT=<EMAIL>
FORSTOK_PASSWORD=hXSOuLWbLB9kunA48PKeww8twG2YmS5t
FORSTOK_SECRET=hXSOuLWbLB9kunA48PKeww8twG2YmS5t
REDIS_URL_CHECKER_CACHED_LIFETIME=3600
KAFKA_HOST=kafka.sit.tbsgroup.co.id:9092
KAFKA_NAME=fcm_publisher
KAFKA_CLIENT_ID=fcm_icarus
KAFKA_GROUP_ID=push_notif
KAFKA_AUTO_COMMIT=false
KAFKA_TOPIC_PREFIX=dev
ELASTIC_STOCK_REDIS_ALIAS=redis-stock
MS_TEAMS_INCOMING_WEBHOOK=https://thebodyshopcoid.webhook.office.com/webhookb2/5b9fb162-d1f4-4470-a9c7-2b73ce0e3d9c@106fc968-9283-41c4-af9c-f5e1f55cc39f/IncomingWebhook/040da7426baa4ae4a7c994a6d4af0a67/707110b8-e1e2-4d8c-8b48-70da292f5ad1
FE_WEB_DOMAIN = https://sit-web.tbsgroup.co.id
CRON_JOB_REDIS_DB=4
CRON_JOB_QUEUE_PREFIX=product
CRON_JOB_BASE_URL=http://localhost:3001/
PRODUCTS_UPLOAD_FOLDER=http://localhost:3001/
USERS_UPLOAD_FOLDER=http://localhost:3001/
UTILS_UPLOAD_FOLDER=http://localhost:3001/
PAYMENTS_UPLOAD_FOLDER=http://localhost:3001/
FIREBASE_PRIVATE_KEY=http://localhost:3001/
FIREBASE_CLIENT_EMAIL=http://localhost:3001/
FIREBASE_PRIVATE_KEY=http://localhost:3001/
FIREBASE_PROJECT_ID=http://localhost:3001/