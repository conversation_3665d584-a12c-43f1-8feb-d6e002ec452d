// {
//   "compilerOptions": {
//     "target": "ES6",
//     "lib": [
//       "dom",
//       "dom.iterable",
//       "esnext"
//     ],
//     "allowJs": false,
//     "allowUnusedLabels": false,
//     "skipLibCheck": true,
//     "strict": true,
//     "forceConsistentCasingInFileNames": true,
//     "noEmit": true,
//     "esModuleInterop": true,
//     "module": "esnext",
//     "moduleResolution": "node",
//     "resolveJsonModule": true,
//     "isolatedModules": true,
//     "noUnusedLocals": true,
//     "noUnusedParameters": true,
//     "jsx": "preserve",
//     "incremental": true,
//     "experimentalDecorators": true
//   },
//   "include": [
//     "**/*.ts",
//     "**/*.tsx"
//   ],
//   "exclude": [
//     "node_modules"
//   ]
// }

{
  "compilerOptions": {
    "target": "ES6",
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false
  }
}