version: '3.3'

services:
  dev:
    container_name: nestjs_backend_dev
    image: nestjs-api-dev:latest
    # environment:
    #   - MONGO_URL=mongodb://localhost:2701/tbs_backend_dev
    build:
      context: .
      dockerfile: ./Dockerfile
    command: "npm run start:dev"
    ports:
      - 3000:3000
      - 3001:9229
    networks:
      - nesjs-network
    depends_on:
      - mongodb
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    restart: unless-stopped
  mongodb:
    image: mongo:latest
    container_name: mongodb
    volumes:
      - db:/data/db
    ports:
      - 27017:27017
    networks:
      - nesjs-network
    restart: always
  prod:
    container_name: nestjs_backend
    image: nestjs-api:latest
    # environment:
    #   - MONGO_URL=mongodb://localhost:2701/tbs_backend_prod
    build:
      context: .
      dockerfile: ./Dockerfile
    command: "npm run start:prod"
    ports:
      - 3000:3000
      - 9229:9229
    networks:
      - nesjs-network
    depends_on:
      - mongodb
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    restart: unless-stopped
volumes:
  db:
networks:
  nesjs-network:
    driver: bridge
